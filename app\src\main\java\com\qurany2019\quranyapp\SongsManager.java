package com.qurany2019.quranyapp;

/**
 * Created by java_dude on 06/06/18.
 */

import java.io.File;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;
import java.io.FilenameFilter;
import java.util.ArrayList;
import java.util.HashMap;

public class SongsManager {
    // SDCard Path
    final String MEDIA_PATH = new String("/sdcard/");
    private ArrayList<HashMap<String, String>> songsList = new ArrayList<HashMap<String, String>>();

    // Constructor
    public SongsManager(){

    }

    /**
     * Function to read all mp3 files from sdcard
     * and store the details in ArrayList
     * */
    public ArrayList<HashMap<String, String>> getPlayList(String RecitesName){
        return getPlayListWithContext(null, RecitesName);
    }

    /**
     * Function to read all mp3 files with context support
     * */
    public ArrayList<HashMap<String, String>> getPlayListWithContext(android.content.Context context, String RecitesName){
        File home = new File(MEDIA_PATH);

        ArrayList<AuthorClass> list = new ArrayList<AuthorClass>();
        LnaguageClass lc = new LnaguageClass();

        // استخدام الدالة المُحدثة التي تفحص الملفات المحلية
        if (context != null) {
            android.util.Log.d("SONGS_MANAGER", "🔍 استخدام GuranAyaWithContext للبحث عن الملفات المحلية");
            list = lc.GuranAyaWithContext(context, RecitesName);
        } else {
            android.util.Log.d("SONGS_MANAGER", "⚠️ استخدام GuranAya العادي (بدون فحص ملفات محلية)");
            list = lc.GuranAya(RecitesName);
        }

        songsList.clear(); // تنظيف القائمة أولاً

        for(AuthorClass temp : list){
            android.util.Log.d("SONGS_MANAGER", "📝 إضافة سورة: " + temp.RealName + " - المسار: " + temp.ImgUrl);

            HashMap<String, String> song = new HashMap<String, String>();
            song.put("songTitle", temp.RealName);
            song.put("songPath", temp.ImgUrl);

            // Adding each song to SongList
            songsList.add(song);
        }

        android.util.Log.d("SONGS_MANAGER", "✅ تم تحميل " + songsList.size() + " سورة");
        // return songs list array
        return songsList;
    }

    /**
     * Class to filter files which are having .mp3 extension
     * */
    class FileExtensionFilter implements FilenameFilter {
        public boolean accept(File dir, String name) {
            return (name.endsWith(".mp3") || name.endsWith(".MP3"));
        }
    }
}

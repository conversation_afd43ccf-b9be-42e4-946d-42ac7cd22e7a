package com.qurany2019.quranyapp.database.dao;

import android.database.Cursor;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AchievementDao_Impl implements AchievementDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AchievementEntity> __insertionAdapterOfAchievementEntity;

  private final EntityDeletionOrUpdateAdapter<AchievementEntity> __deletionAdapterOfAchievementEntity;

  private final EntityDeletionOrUpdateAdapter<AchievementEntity> __updateAdapterOfAchievementEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAchievementById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAchievementByAchievementId;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAchievementsByType;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAchievementsByCategory;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllAchievements;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAchievementProgress;

  private final SharedSQLiteStatement __preparedStmtOfIncrementAchievementProgress;

  private final SharedSQLiteStatement __preparedStmtOfUnlockAchievement;

  private final SharedSQLiteStatement __preparedStmtOfResetAchievement;

  private final SharedSQLiteStatement __preparedStmtOfUpdateTypeTimestamp;

  private final SharedSQLiteStatement __preparedStmtOfUpdateCategoryTimestamp;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAllTimestamps;

  public AchievementDao_Impl(RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAchievementEntity = new EntityInsertionAdapter<AchievementEntity>(__db) {
      @Override
      public String createQuery() {
        return "INSERT OR REPLACE INTO `achievements_table` (`id`,`achievement_id`,`title`,`description`,`achievement_type`,`target_value`,`current_value`,`is_unlocked`,`unlock_date`,`icon_resource`,`reward_points`,`category`,`created_at`,`updated_at`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AchievementEntity value) {
        stmt.bindLong(1, value.getId());
        if (value.getAchievementId() == null) {
          stmt.bindNull(2);
        } else {
          stmt.bindString(2, value.getAchievementId());
        }
        if (value.getTitle() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getTitle());
        }
        if (value.getDescription() == null) {
          stmt.bindNull(4);
        } else {
          stmt.bindString(4, value.getDescription());
        }
        if (value.getAchievementType() == null) {
          stmt.bindNull(5);
        } else {
          stmt.bindString(5, value.getAchievementType());
        }
        stmt.bindLong(6, value.getTargetValue());
        stmt.bindLong(7, value.getCurrentValue());
        final int _tmp = value.isUnlocked() ? 1 : 0;
        stmt.bindLong(8, _tmp);
        stmt.bindLong(9, value.getUnlockDate());
        if (value.getIconResource() == null) {
          stmt.bindNull(10);
        } else {
          stmt.bindString(10, value.getIconResource());
        }
        stmt.bindLong(11, value.getRewardPoints());
        if (value.getCategory() == null) {
          stmt.bindNull(12);
        } else {
          stmt.bindString(12, value.getCategory());
        }
        stmt.bindLong(13, value.getCreatedAt());
        stmt.bindLong(14, value.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfAchievementEntity = new EntityDeletionOrUpdateAdapter<AchievementEntity>(__db) {
      @Override
      public String createQuery() {
        return "DELETE FROM `achievements_table` WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AchievementEntity value) {
        stmt.bindLong(1, value.getId());
      }
    };
    this.__updateAdapterOfAchievementEntity = new EntityDeletionOrUpdateAdapter<AchievementEntity>(__db) {
      @Override
      public String createQuery() {
        return "UPDATE OR ABORT `achievements_table` SET `id` = ?,`achievement_id` = ?,`title` = ?,`description` = ?,`achievement_type` = ?,`target_value` = ?,`current_value` = ?,`is_unlocked` = ?,`unlock_date` = ?,`icon_resource` = ?,`reward_points` = ?,`category` = ?,`created_at` = ?,`updated_at` = ? WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AchievementEntity value) {
        stmt.bindLong(1, value.getId());
        if (value.getAchievementId() == null) {
          stmt.bindNull(2);
        } else {
          stmt.bindString(2, value.getAchievementId());
        }
        if (value.getTitle() == null) {
          stmt.bindNull(3);
        } else {
          stmt.bindString(3, value.getTitle());
        }
        if (value.getDescription() == null) {
          stmt.bindNull(4);
        } else {
          stmt.bindString(4, value.getDescription());
        }
        if (value.getAchievementType() == null) {
          stmt.bindNull(5);
        } else {
          stmt.bindString(5, value.getAchievementType());
        }
        stmt.bindLong(6, value.getTargetValue());
        stmt.bindLong(7, value.getCurrentValue());
        final int _tmp = value.isUnlocked() ? 1 : 0;
        stmt.bindLong(8, _tmp);
        stmt.bindLong(9, value.getUnlockDate());
        if (value.getIconResource() == null) {
          stmt.bindNull(10);
        } else {
          stmt.bindString(10, value.getIconResource());
        }
        stmt.bindLong(11, value.getRewardPoints());
        if (value.getCategory() == null) {
          stmt.bindNull(12);
        } else {
          stmt.bindString(12, value.getCategory());
        }
        stmt.bindLong(13, value.getCreatedAt());
        stmt.bindLong(14, value.getUpdatedAt());
        stmt.bindLong(15, value.getId());
      }
    };
    this.__preparedStmtOfDeleteAchievementById = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM achievements_table WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAchievementByAchievementId = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM achievements_table WHERE achievement_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAchievementsByType = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM achievements_table WHERE achievement_type = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAchievementsByCategory = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM achievements_table WHERE category = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllAchievements = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM achievements_table";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAchievementProgress = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET current_value = ?, updated_at = ? WHERE achievement_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementAchievementProgress = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET current_value = current_value + ?, updated_at = ? WHERE achievement_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUnlockAchievement = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET is_unlocked = 1, unlock_date = ?, updated_at = ? WHERE achievement_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfResetAchievement = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET current_value = 0, is_unlocked = 0, unlock_date = 0, updated_at = ? WHERE achievement_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateTypeTimestamp = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET updated_at = ? WHERE achievement_type = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateCategoryTimestamp = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET updated_at = ? WHERE category = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAllTimestamps = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE achievements_table SET updated_at = ?";
        return _query;
      }
    };
  }

  @Override
  public long insertAchievement(final AchievementEntity achievement) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      long _result = __insertionAdapterOfAchievementEntity.insertAndReturnId(achievement);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Long> insertAllAchievements(final List<AchievementEntity> achievementsList) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      List<Long> _result = __insertionAdapterOfAchievementEntity.insertAndReturnIdsList(achievementsList);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int deleteAchievement(final AchievementEntity achievement) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total +=__deletionAdapterOfAchievementEntity.handle(achievement);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateAchievement(final AchievementEntity achievement) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total +=__updateAdapterOfAchievementEntity.handle(achievement);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int deleteAchievementById(final int achievementId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAchievementById.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, achievementId);
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAchievementById.release(_stmt);
    }
  }

  @Override
  public int deleteAchievementByAchievementId(final String achievementId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAchievementByAchievementId.acquire();
    int _argIndex = 1;
    if (achievementId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementId);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAchievementByAchievementId.release(_stmt);
    }
  }

  @Override
  public int deleteAchievementsByType(final String achievementType) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAchievementsByType.acquire();
    int _argIndex = 1;
    if (achievementType == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementType);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAchievementsByType.release(_stmt);
    }
  }

  @Override
  public int deleteAchievementsByCategory(final String category) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAchievementsByCategory.acquire();
    int _argIndex = 1;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAchievementsByCategory.release(_stmt);
    }
  }

  @Override
  public int deleteAllAchievements() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllAchievements.acquire();
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAllAchievements.release(_stmt);
    }
  }

  @Override
  public int updateAchievementProgress(final String achievementId, final int currentValue,
      final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAchievementProgress.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, currentValue);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 3;
    if (achievementId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementId);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateAchievementProgress.release(_stmt);
    }
  }

  @Override
  public int incrementAchievementProgress(final String achievementId, final int increment,
      final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementAchievementProgress.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, increment);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 3;
    if (achievementId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementId);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfIncrementAchievementProgress.release(_stmt);
    }
  }

  @Override
  public int unlockAchievement(final String achievementId, final long unlockDate,
      final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUnlockAchievement.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, unlockDate);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 3;
    if (achievementId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementId);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUnlockAchievement.release(_stmt);
    }
  }

  @Override
  public int resetAchievement(final String achievementId, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfResetAchievement.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    if (achievementId == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementId);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfResetAchievement.release(_stmt);
    }
  }

  @Override
  public int updateTypeTimestamp(final String achievementType, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateTypeTimestamp.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    if (achievementType == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, achievementType);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateTypeTimestamp.release(_stmt);
    }
  }

  @Override
  public int updateCategoryTimestamp(final String category, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateCategoryTimestamp.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    if (category == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, category);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateCategoryTimestamp.release(_stmt);
    }
  }

  @Override
  public int updateAllTimestamps(final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAllTimestamps.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateAllTimestamps.release(_stmt);
    }
  }

  @Override
  public AchievementEntity getAchievementById(final int achievementId) {
    final String _sql = "SELECT * FROM achievements_table WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, achievementId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AchievementEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _result.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _result.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _result.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _result.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _result.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _result.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _result.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _result.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _result.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _result.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _result.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public AchievementEntity getAchievementByAchievementId(final String achievementId) {
    final String _sql = "SELECT * FROM achievements_table WHERE achievement_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AchievementEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _result.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _result.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _result.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _result.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _result.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _result.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _result.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _result.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _result.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _result.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _result.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<AchievementEntity> getAchievementByAchievementIdLiveData(
      final String achievementId) {
    final String _sql = "SELECT * FROM achievements_table WHERE achievement_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<AchievementEntity>() {
      @Override
      public AchievementEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final AchievementEntity _result;
          if(_cursor.moveToFirst()) {
            _result = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _result.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _result.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _result.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _result.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _result.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _result.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _result.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _result.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _result.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _result.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _result.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result.setUpdatedAt(_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getAllAchievements() {
    final String _sql = "SELECT * FROM achievements_table ORDER BY is_unlocked DESC, achievement_type, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getAllAchievementsLiveData() {
    final String _sql = "SELECT * FROM achievements_table ORDER BY is_unlocked DESC, achievement_type, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getUnlockedAchievements() {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getUnlockedAchievementsLiveData() {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getLockedAchievements() {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 0 ORDER BY achievement_type, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getLockedAchievementsLiveData() {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 0 ORDER BY achievement_type, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getAchievementsByType(final String achievementType) {
    final String _sql = "SELECT * FROM achievements_table WHERE achievement_type = ? ORDER BY is_unlocked DESC, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementType);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getAchievementsByTypeLiveData(
      final String achievementType) {
    final String _sql = "SELECT * FROM achievements_table WHERE achievement_type = ? ORDER BY is_unlocked DESC, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementType);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getAchievementsByCategory(final String category) {
    final String _sql = "SELECT * FROM achievements_table WHERE category = ? ORDER BY is_unlocked DESC, achievement_type, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getAchievementsByCategoryLiveData(
      final String category) {
    final String _sql = "SELECT * FROM achievements_table WHERE category = ? ORDER BY is_unlocked DESC, achievement_type, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getCompletedButNotUnlockedAchievements() {
    final String _sql = "SELECT * FROM achievements_table WHERE current_value >= target_value AND is_unlocked = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AchievementEntity> getInProgressAchievements() {
    final String _sql = "SELECT * FROM achievements_table WHERE current_value > 0 AND current_value < target_value ORDER BY (current_value * 100 / target_value) DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getInProgressAchievementsLiveData() {
    final String _sql = "SELECT * FROM achievements_table WHERE current_value > 0 AND current_value < target_value ORDER BY (current_value * 100 / target_value) DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getTotalAchievementsCount() {
    final String _sql = "SELECT COUNT(*) FROM achievements_table";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getAchievementCount() {
    final String _sql = "SELECT COUNT(*) FROM achievements_table";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getUnlockedAchievementsCount() {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE is_unlocked = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getLockedAchievementsCount() {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE is_unlocked = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getAchievementsCountByType(final String achievementType) {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE achievement_type = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementType);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getUnlockedAchievementsCountByType(final String achievementType) {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE achievement_type = ? AND is_unlocked = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementType);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getAchievementsCountByCategory(final String category) {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE category = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getUnlockedAchievementsCountByCategory(final String category) {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE category = ? AND is_unlocked = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (category == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, category);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTotalRewardPoints() {
    final String _sql = "SELECT SUM(reward_points) FROM achievements_table WHERE is_unlocked = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getOverallProgressPercentage() {
    final String _sql = "SELECT AVG(current_value * 100.0 / target_value) FROM achievements_table WHERE target_value > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getProgressPercentageByType(final String achievementType) {
    final String _sql = "SELECT AVG(current_value * 100.0 / target_value) FROM achievements_table WHERE achievement_type = ? AND target_value > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementType == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementType);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AchievementEntity> getRecentlyUnlockedAchievements(final int limit) {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> getRecentlyUnlockedAchievementsLiveData(
      final int limit) {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AchievementEntity> getRecentlyUpdatedAchievements(final int limit) {
    final String _sql = "SELECT * FROM achievements_table ORDER BY updated_at DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AchievementEntity> getAchievementsUnlockedBetweenDates(final long startDate,
      final long endDate) {
    final String _sql = "SELECT * FROM achievements_table WHERE is_unlocked = 1 AND unlock_date BETWEEN ? AND ? ORDER BY unlock_date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getAchievementsUnlockedCountBetweenDates(final long startDate, final long endDate) {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE is_unlocked = 1 AND unlock_date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endDate);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AchievementEntity> searchAchievements(final String searchQuery) {
    final String _sql = "SELECT * FROM achievements_table WHERE title LIKE ? OR description LIKE ? ORDER BY is_unlocked DESC, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    _argIndex = 2;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AchievementEntity>> searchAchievementsLiveData(final String searchQuery) {
    final String _sql = "SELECT * FROM achievements_table WHERE title LIKE ? OR description LIKE ? ORDER BY is_unlocked DESC, title";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    _argIndex = 2;
    if (searchQuery == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, searchQuery);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"achievements_table"}, false, new Callable<List<AchievementEntity>>() {
      @Override
      public List<AchievementEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
          final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
          final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
          final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
          final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
          final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
          final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AchievementEntity _item;
            _item = new AchievementEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpAchievementId;
            if (_cursor.isNull(_cursorIndexOfAchievementId)) {
              _tmpAchievementId = null;
            } else {
              _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
            }
            _item.setAchievementId(_tmpAchievementId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            _item.setTitle(_tmpTitle);
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            _item.setDescription(_tmpDescription);
            final String _tmpAchievementType;
            if (_cursor.isNull(_cursorIndexOfAchievementType)) {
              _tmpAchievementType = null;
            } else {
              _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
            }
            _item.setAchievementType(_tmpAchievementType);
            final int _tmpTargetValue;
            _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
            _item.setTargetValue(_tmpTargetValue);
            final int _tmpCurrentValue;
            _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
            _item.setCurrentValue(_tmpCurrentValue);
            final boolean _tmpIsUnlocked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
            _tmpIsUnlocked = _tmp != 0;
            _item.setUnlocked(_tmpIsUnlocked);
            final long _tmpUnlockDate;
            _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
            _item.setUnlockDate(_tmpUnlockDate);
            final String _tmpIconResource;
            if (_cursor.isNull(_cursorIndexOfIconResource)) {
              _tmpIconResource = null;
            } else {
              _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
            }
            _item.setIconResource(_tmpIconResource);
            final int _tmpRewardPoints;
            _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
            _item.setRewardPoints(_tmpRewardPoints);
            final String _tmpCategory;
            if (_cursor.isNull(_cursorIndexOfCategory)) {
              _tmpCategory = null;
            } else {
              _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            }
            _item.setCategory(_tmpCategory);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<String> getAllAchievementTypes() {
    final String _sql = "SELECT DISTINCT achievement_type FROM achievements_table ORDER BY achievement_type";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<String> getAllAchievementCategories() {
    final String _sql = "SELECT DISTINCT category FROM achievements_table ORDER BY category";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int checkAchievementIdExists(final String achievementId) {
    final String _sql = "SELECT COUNT(*) FROM achievements_table WHERE achievement_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (achievementId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, achievementId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AchievementEntity> getAchievementsOlderThan(final long timestamp) {
    final String _sql = "SELECT * FROM achievements_table WHERE updated_at < ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, timestamp);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAchievementId = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_id");
      final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
      final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
      final int _cursorIndexOfAchievementType = CursorUtil.getColumnIndexOrThrow(_cursor, "achievement_type");
      final int _cursorIndexOfTargetValue = CursorUtil.getColumnIndexOrThrow(_cursor, "target_value");
      final int _cursorIndexOfCurrentValue = CursorUtil.getColumnIndexOrThrow(_cursor, "current_value");
      final int _cursorIndexOfIsUnlocked = CursorUtil.getColumnIndexOrThrow(_cursor, "is_unlocked");
      final int _cursorIndexOfUnlockDate = CursorUtil.getColumnIndexOrThrow(_cursor, "unlock_date");
      final int _cursorIndexOfIconResource = CursorUtil.getColumnIndexOrThrow(_cursor, "icon_resource");
      final int _cursorIndexOfRewardPoints = CursorUtil.getColumnIndexOrThrow(_cursor, "reward_points");
      final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AchievementEntity> _result = new ArrayList<AchievementEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AchievementEntity _item;
        _item = new AchievementEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpAchievementId;
        if (_cursor.isNull(_cursorIndexOfAchievementId)) {
          _tmpAchievementId = null;
        } else {
          _tmpAchievementId = _cursor.getString(_cursorIndexOfAchievementId);
        }
        _item.setAchievementId(_tmpAchievementId);
        final String _tmpTitle;
        if (_cursor.isNull(_cursorIndexOfTitle)) {
          _tmpTitle = null;
        } else {
          _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
        }
        _item.setTitle(_tmpTitle);
        final String _tmpDescription;
        if (_cursor.isNull(_cursorIndexOfDescription)) {
          _tmpDescription = null;
        } else {
          _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
        }
        _item.setDescription(_tmpDescription);
        final String _tmpAchievementType;
        if (_cursor.isNull(_cursorIndexOfAchievementType)) {
          _tmpAchievementType = null;
        } else {
          _tmpAchievementType = _cursor.getString(_cursorIndexOfAchievementType);
        }
        _item.setAchievementType(_tmpAchievementType);
        final int _tmpTargetValue;
        _tmpTargetValue = _cursor.getInt(_cursorIndexOfTargetValue);
        _item.setTargetValue(_tmpTargetValue);
        final int _tmpCurrentValue;
        _tmpCurrentValue = _cursor.getInt(_cursorIndexOfCurrentValue);
        _item.setCurrentValue(_tmpCurrentValue);
        final boolean _tmpIsUnlocked;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsUnlocked);
        _tmpIsUnlocked = _tmp != 0;
        _item.setUnlocked(_tmpIsUnlocked);
        final long _tmpUnlockDate;
        _tmpUnlockDate = _cursor.getLong(_cursorIndexOfUnlockDate);
        _item.setUnlockDate(_tmpUnlockDate);
        final String _tmpIconResource;
        if (_cursor.isNull(_cursorIndexOfIconResource)) {
          _tmpIconResource = null;
        } else {
          _tmpIconResource = _cursor.getString(_cursorIndexOfIconResource);
        }
        _item.setIconResource(_tmpIconResource);
        final int _tmpRewardPoints;
        _tmpRewardPoints = _cursor.getInt(_cursorIndexOfRewardPoints);
        _item.setRewardPoints(_tmpRewardPoints);
        final String _tmpCategory;
        if (_cursor.isNull(_cursorIndexOfCategory)) {
          _tmpCategory = null;
        } else {
          _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
        }
        _item.setCategory(_tmpCategory);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}

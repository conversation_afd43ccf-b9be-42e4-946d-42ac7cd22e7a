<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layoutDirection="ltr"
    android:orientation="horizontal"
    android:layout_height="wrap_content"
    android:paddingLeft="15dp"
    android:paddingRight="15dp"
    android:paddingTop="9dp"
    android:paddingBottom="9dp">


    <Switch
        android:background="@drawable/menuback"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/switch1"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:layout_alignBottom="@+id/textView"
        android:textOn="@string/ar"
        android:textOff="@string/en"
        android:showText="true"
        android:textSize="24dp"
        android:checked="false" />

    <TextView


        android:gravity="right"
        android:background="@color/white"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?android:attr/textAppearanceLarge"
        android:text="item name"
        android:id="@+id/textView"
        android:layout_alignParentTop="true"
        android:paddingTop="3dp"
        android:paddingRight="7dp"
        android:layout_alignBottom="@+id/imgchannel"
        android:layout_toLeftOf="@+id/imgchannel"
        android:layout_toRightOf="@+id/switch1"
        android:layout_toEndOf="@+id/switch1"
        android:layout_weight="1"
        android:textSize="20dp" />

    <ImageView
        android:background="@color/white"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:id="@+id/imgchannel"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true"
        android:src="@drawable/add1"
        android:layout_marginLeft="7dp" />


</LinearLayout>

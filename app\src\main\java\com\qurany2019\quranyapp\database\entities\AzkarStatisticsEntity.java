package com.qurany2019.quranyapp.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.Index;
import androidx.room.Ignore;

@Entity(
    tableName = "azkar_statistics_table",
    indices = {@Index("date"), @Index("category")}
)
public class AzkarStatisticsEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "date")
    private String date; // Format: yyyy-MM-dd

    @ColumnInfo(name = "category")
    private String category; // morning, evening, sleep, wake, prayer, all

    @ColumnInfo(name = "total_azkar_count")
    private int totalAzkarCount;

    @ColumnInfo(name = "completed_azkar_count")
    private int completedAzkarCount;

    @ColumnInfo(name = "total_tasbih_count")
    private int totalTasbihCount; // إجمالي التسبيحات

    @ColumnInfo(name = "completion_percentage")
    private float completionPercentage;

    @ColumnInfo(name = "session_duration")
    private long sessionDuration; // in milliseconds

    @ColumnInfo(name = "streak_days")
    private int streakDays; // عدد الأيام المتتالية

    @ColumnInfo(name = "best_completion_time")
    private long bestCompletionTime; // أسرع وقت إنجاز

    @ColumnInfo(name = "average_completion_time")
    private long averageCompletionTime;

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Constructors
    public AzkarStatisticsEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public AzkarStatisticsEntity(String date, String category) {
        this.date = date;
        this.category = category;
        this.totalAzkarCount = 0;
        this.completedAzkarCount = 0;
        this.totalTasbihCount = 0;
        this.completionPercentage = 0.0f;
        this.sessionDuration = 0;
        this.streakDays = 0;
        this.bestCompletionTime = 0;
        this.averageCompletionTime = 0;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public int getTotalAzkarCount() {
        return totalAzkarCount;
    }

    public void setTotalAzkarCount(int totalAzkarCount) {
        this.totalAzkarCount = totalAzkarCount;
        updateCompletionPercentage();
    }

    public int getCompletedAzkarCount() {
        return completedAzkarCount;
    }

    public void setCompletedAzkarCount(int completedAzkarCount) {
        this.completedAzkarCount = completedAzkarCount;
        updateCompletionPercentage();
        this.updatedAt = System.currentTimeMillis();
    }

    public int getTotalTasbihCount() {
        return totalTasbihCount;
    }

    public void setTotalTasbihCount(int totalTasbihCount) {
        this.totalTasbihCount = totalTasbihCount;
        this.updatedAt = System.currentTimeMillis();
    }

    public float getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(float completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public long getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(long sessionDuration) {
        this.sessionDuration = sessionDuration;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getStreakDays() {
        return streakDays;
    }

    public void setStreakDays(int streakDays) {
        this.streakDays = streakDays;
    }

    public long getBestCompletionTime() {
        return bestCompletionTime;
    }

    public void setBestCompletionTime(long bestCompletionTime) {
        this.bestCompletionTime = bestCompletionTime;
    }

    public long getAverageCompletionTime() {
        return averageCompletionTime;
    }

    public void setAverageCompletionTime(long averageCompletionTime) {
        this.averageCompletionTime = averageCompletionTime;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    private void updateCompletionPercentage() {
        if (totalAzkarCount > 0) {
            this.completionPercentage = ((float) completedAzkarCount / totalAzkarCount) * 100;
        } else {
            this.completionPercentage = 0.0f;
        }
    }

    public void incrementCompletedAzkar() {
        this.completedAzkarCount++;
        updateCompletionPercentage();
        this.updatedAt = System.currentTimeMillis();
    }

    public void addTasbihCount(int count) {
        this.totalTasbihCount += count;
        this.updatedAt = System.currentTimeMillis();
    }

    public void addSessionDuration(long duration) {
        this.sessionDuration += duration;
        this.updatedAt = System.currentTimeMillis();
    }

    public boolean isFullyCompleted() {
        return totalAzkarCount > 0 && completedAzkarCount >= totalAzkarCount;
    }

    public String getFormattedDuration() {
        long seconds = sessionDuration / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else {
            return String.format("%d:%02d", minutes, seconds % 60);
        }
    }

    @Override
    public String toString() {
        return "AzkarStatisticsEntity{" +
                "id=" + id +
                ", date='" + date + '\'' +
                ", category='" + category + '\'' +
                ", totalAzkarCount=" + totalAzkarCount +
                ", completedAzkarCount=" + completedAzkarCount +
                ", totalTasbihCount=" + totalTasbihCount +
                ", completionPercentage=" + completionPercentage +
                ", sessionDuration=" + sessionDuration +
                ", streakDays=" + streakDays +
                ", bestCompletionTime=" + bestCompletionTime +
                ", averageCompletionTime=" + averageCompletionTime +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}

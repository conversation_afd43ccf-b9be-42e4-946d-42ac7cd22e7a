package com.qurany2019.quranyapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.Toolbar;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import java.util.Locale;

public class SettingsActivity extends AppCompatActivity {

    private AdView mAdView;
    private Switch switchNotifications, switchAutoPlay, switchDarkMode;
    private TextView tvFontSize, darkModeTitle, darkModeDesc;
    private android.widget.ImageView darkModeIcon;
    private SharedPreferences prefs;
    private android.widget.LinearLayout arabicLanguageOption, englishLanguageOption;
    private android.widget.ImageView arabicSelectedIcon, englishSelectedIcon;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق الإعدادات المحفوظة
        applyLanguageSettings();
        applyDarkModeSettings();

        setContentView(R.layout.activity_settings);

        // إعداد التولبار
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(getString(R.string.settings_title));
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // تهيئة الإعلانات
        MobileAds.initialize(this);
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        // تهيئة المتغيرات
        prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        initViews();
        loadSettings();
        setupListeners();
    }

    private void initViews() {
        switchNotifications = findViewById(R.id.switchNotifications);
        switchAutoPlay = findViewById(R.id.switchAutoPlay);
        switchDarkMode = findViewById(R.id.switchDarkMode);
        tvFontSize = findViewById(R.id.tvFontSize);

        // عناصر الوضع الليلي المحسنة
        darkModeIcon = findViewById(R.id.darkModeIcon);
        darkModeTitle = findViewById(R.id.darkModeTitle);
        darkModeDesc = findViewById(R.id.darkModeDesc);

        // خيارات اللغة الجديدة
        arabicLanguageOption = findViewById(R.id.arabicLanguageOption);
        englishLanguageOption = findViewById(R.id.englishLanguageOption);
        arabicSelectedIcon = findViewById(R.id.arabicSelectedIcon);
        englishSelectedIcon = findViewById(R.id.englishSelectedIcon);
    }

    private void loadSettings() {
        // تحميل الإعدادات المحفوظة
        boolean isArabic = prefs.getBoolean("is_arabic", true);
        boolean isDarkMode = prefs.getBoolean("dark_mode", false);

        switchNotifications.setChecked(prefs.getBoolean("notifications_enabled", true));
        switchAutoPlay.setChecked(prefs.getBoolean("auto_play", false));
        switchDarkMode.setChecked(isDarkMode);

        int fontSize = prefs.getInt("font_size", 16);
        tvFontSize.setText(fontSize + " sp");

        // تحديث عرض اللغة المختارة
        updateLanguageSelection(isArabic);

        // تحديث واجهة الوضع الليلي
        updateDarkModeUI(isDarkMode);
    }

    private void setupListeners() {
        // مستمع خيار اللغة العربية
        arabicLanguageOption.setOnClickListener(v -> {
            saveLanguageSetting(true);
            applyLanguageChangeGlobally("ar");
        });

        // مستمع خيار اللغة الإنجليزية
        englishLanguageOption.setOnClickListener(v -> {
            saveLanguageSetting(false);
            applyLanguageChangeGlobally("en");
        });

        // مستمع الإشعارات
        switchNotifications.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                saveNotificationSetting(isChecked);
            }
        });

        // مستمع التشغيل التلقائي
        switchAutoPlay.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                saveAutoPlaySetting(isChecked);
            }
        });

        // مستمع الوضع الليلي المحسن
        switchDarkMode.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                saveDarkModeSetting(isChecked);
                updateDarkModeUI(isChecked);
                applyDarkMode(isChecked);
            }
        });
    }

    // دالة تحديث عرض اللغة المختارة
    private void updateLanguageSelection(boolean isArabic) {
        if (isArabic) {
            arabicSelectedIcon.setVisibility(android.view.View.VISIBLE);
            englishSelectedIcon.setVisibility(android.view.View.GONE);
        } else {
            arabicSelectedIcon.setVisibility(android.view.View.GONE);
            englishSelectedIcon.setVisibility(android.view.View.VISIBLE);
        }
    }

    private void saveLanguageSetting(boolean isArabic) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("is_arabic", isArabic);
        editor.apply();

        // تحديث إعدادات اللغة في التطبيق
        SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        SharedPreferences.Editor langEditor = langPrefs.edit();
        langEditor.putBoolean("is_arabic", isArabic);
        langEditor.apply();

        SaveSettings.LanguageSelect = isArabic ? 1 : 2;
    }

    private void saveNotificationSetting(boolean enabled) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("notifications_enabled", enabled);
        editor.apply();
    }

    private void saveAutoPlaySetting(boolean enabled) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("auto_play", enabled);
        editor.apply();
    }

    private void applyLanguageSettings() {
        SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = langPrefs.getBoolean("is_arabic", true);
        setLocale(isArabic ? "ar" : "en");
    }

    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    // دالة تطبيق تغيير اللغة على جميع الصفحات
    private void applyLanguageChangeGlobally(String languageCode) {
        // تطبيق اللغة الجديدة
        setLocale(languageCode);

        // تحديث عرض اللغة المختارة
        updateLanguageSelection(languageCode.equals("ar"));

        // رسالة تأكيد
        String message = languageCode.equals("ar") ?
            "تم تغيير اللغة إلى العربية" :
            "Language changed to English";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

        // إرسال broadcast لإعلام جميع الأنشطة بتغيير اللغة
        Intent broadcastIntent = new Intent("com.qurany2019.quranyapp.LANGUAGE_CHANGED");
        broadcastIntent.putExtra("language_code", languageCode);
        sendBroadcast(broadcastIntent);

        // إعادة إنشاء النشاط الحالي لتطبيق التغييرات
        recreate();

        // إعادة تشغيل التطبيق بشكل أكثر فعالية
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP |
                       Intent.FLAG_ACTIVITY_NEW_TASK |
                       Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    // دوال الوضع الليلي
    private void saveDarkModeSetting(boolean enabled) {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("dark_mode", enabled);
        editor.apply();
    }

    // دالة تحديث واجهة الوضع الليلي مع الترجمة
    private void updateDarkModeUI(boolean isDarkMode) {
        if (darkModeIcon != null && darkModeTitle != null && darkModeDesc != null) {
            if (isDarkMode) {
                darkModeIcon.setImageResource(R.drawable.ic_sun);
                darkModeTitle.setText(getString(R.string.night_mode_light));
                darkModeDesc.setText(getString(R.string.light_mode_desc));
                // تأثير حركي جميل للأيقونة
                darkModeIcon.animate()
                    .scaleX(1.3f)
                    .scaleY(1.3f)
                    .setDuration(300)
                    .withEndAction(() -> darkModeIcon.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(300)
                        .start())
                    .start();
            } else {
                darkModeIcon.setImageResource(R.drawable.ic_moon);
                darkModeTitle.setText(getString(R.string.night_mode_dark));
                darkModeDesc.setText(getString(R.string.dark_mode_desc_dynamic));
                // تأثير حركي جميل للأيقونة
                darkModeIcon.animate()
                    .scaleX(1.3f)
                    .scaleY(1.3f)
                    .setDuration(300)
                    .withEndAction(() -> darkModeIcon.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(300)
                        .start())
                    .start();
            }
        }
    }

    private void applyDarkModeSettings() {
        SharedPreferences darkPrefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        boolean isDarkMode = darkPrefs.getBoolean("dark_mode", false);
        applyDarkMode(isDarkMode);
    }

    private void applyDarkMode(boolean isDarkMode) {
        if (isDarkMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}

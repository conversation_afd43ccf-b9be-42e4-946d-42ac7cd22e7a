<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.wake_lock" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- الإضافة المطلوبة الجديدة -->

    <!-- أذونات القبلة وأوقات الصلاة -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- أذونات البوصلة والحساسات -->
    <uses-feature android:name="android.hardware.sensor.accelerometer" android:required="true" />
    <uses-feature android:name="android.hardware.sensor.compass" android:required="true" />

    <!-- أذونات إضافية للإشعارات -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <!-- أذونات إشعارات الوسائط الكاملة -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

    <application
        android:name=".QuranApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.QuranyApp"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true">


        <!-- AdMob App ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7841751633097845~7935499105"/>

        <!-- Register the Alarm Receiver -->
        <receiver android:name="com.qurany2019.quranyapp.Receiver" />

        <!-- Prayer Times Notification Receiver -->
        <receiver android:name="com.qurany2019.quranyapp.PrayerNotificationReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.PRAYER_NOTIFICATION" />
            </intent-filter>
        </receiver>

        <!-- Azkar Reminder Receiver -->
        <receiver android:name="com.qurany2019.quranyapp.AzkarReminderReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.AZKAR_REMINDER" />
            </intent-filter>
        </receiver>

        <!-- Boot Receiver to restart notifications -->
        <receiver android:name="com.qurany2019.quranyapp.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.qurany2019.quranyapp.Splash"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.QuranyApp">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.MainActivity"
            android:label="@string/title_activity_main"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.MainActivitySimple"
            android:label="@string/title_activity_main"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivitySimple" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.About"
            android:label="@string/title_activity_about"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.Hisn"
            android:label="@string/title_activity_hisn"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity android:name="com.qurany2019.quranyapp.Whtml"
            android:theme="@style/Theme.QuranyApp" />

        <activity
            android:name="com.qurany2019.quranyapp.Tazker"
            android:label="@string/title_activity_tazker"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.AyaList"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp" />

        <activity
            android:name="com.qurany2019.quranyapp.managerdb"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp"
            android:launchMode="singleTop" />

        <activity
            android:name="com.qurany2019.quranyapp.RecitesName"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp" />

        <activity
            android:name="com.qurany2019.quranyapp.Sellings"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp" />

        <activity
            android:name="com.qurany2019.quranyapp.quranActivity"
            android:label="Read Quran"
            android:configChanges="orientation|screenSize" />

        <activity android:name=".PrivcyActivity" />

        <activity
            android:name="com.qurany2019.quranyapp.SettingsActivity"
            android:label="الإعدادات"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.QiblaActivity"
            android:label="اتجاه القبلة"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.PrayerTimesActivity"
            android:label="أوقات الصلاة"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.AzkarActivity"
            android:label="@string/azkar_title"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.AzkarDetailActivity"
            android:label="تفاصيل الأذكار"
            android:parentActivityName="com.qurany2019.quranyapp.AzkarActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.AzkarActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.MoreActivity"
            android:label="المزيد"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <!-- أنشطة النظام الشامل -->
        <activity
            android:name="com.qurany2019.quranyapp.AzkarStatisticsActivity"
            android:label="إحصائيات الأذكار"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.AchievementsActivity"
            android:label="الإنجازات"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <activity
            android:name="com.qurany2019.quranyapp.ReminderSettingsActivity"
            android:label="إعدادات التذكيرات"
            android:parentActivityName="com.qurany2019.quranyapp.SettingsActivity"
            android:theme="@style/Theme.QuranyApp">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.SettingsActivity" />
        </activity>

        <!-- خدمة إشعارات الوسائط مع أزرار التحكم -->
        <service
            android:name=".MediaNotificationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

        <!-- خدمة إشعارات بسيطة وفعالة -->
        <service
            android:name=".SimpleMediaNotification"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

        <!-- BroadcastReceiver لأوامر الوسائط -->
        <receiver
            android:name=".MediaActionReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.MEDIA_ACTION" />
            </intent-filter>
        </receiver>

        <!-- BroadcastReceiver للإشعار البسيط -->
        <receiver
            android:name=".SimpleMediaReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.SIMPLE_MEDIA_CONTROL" />
            </intent-filter>
        </receiver>

        <!-- BroadcastReceiver للتحكم المباشر -->
        <receiver
            android:name=".StaticMediaReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="STATIC_MEDIA_CONTROL" />
            </intent-filter>
        </receiver>

    </application>

</manifest>

# نقل طلب الأذونات إلى صفحة مواقيت الصلاة

## التغيير المطبق:
تم نقل طلب أذونات الموقع (العادي والخلفية) من الصفحة الرئيسية إلى صفحة مواقيت الصلاة.

## الأسباب:

### 1. **تجربة مستخدم أفضل:**
- المستخدم يفهم الغرض من الإذن عندما يكون في صفحة مواقيت الصلاة
- لا نزعج المستخدم بطلب أذونات في الصفحة الرئيسية
- السياق واضح: "أحتاج موقعك لحساب أوقات الصلاة"

### 2. **متطلبات Google Play:**
- يساعد في حل مشكلة Google Play المتعلقة بوضوح الغرض من إذن الموقع في الخلفية
- الميزة محددة بوضوح: إشعارات أوقات الصلاة التلقائية

### 3. **منطق أفضل:**
- الأذونات تُطلب عند الحاجة الفعلية
- إذا لم يدخل المستخدم صفحة مواقيت الصلاة، لا نطلب الأذونات

## التغييرات المطبقة:

### 1. في MainActivity.java:
```java
// تم إزالة طلب الأذونات من onCreate()
// لا نطلب الأذونات هنا - سيتم طلبها في صفحة مواقيت الصلاة عند الحاجة
```

### 2. في PrayerTimesActivity.java:

#### أ. رسالة توضيحية لإذن الموقع العادي:
```java
private void showLocationPermissionDialog() {
    android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
    builder.setTitle("إذن الوصول للموقع")
           .setMessage("يحتاج التطبيق للوصول إلى موقعك لحساب أوقات الصلاة الدقيقة حسب منطقتك الجغرافية.\n\n" +
                      "سيتم استخدام الموقع أيضاً لإرسال إشعارات أوقات الصلاة في الأوقات المناسبة حتى عند السفر.")
           .setPositiveButton("السماح", (dialog, which) -> {
               // طلب الإذن
           })
           .setNegativeButton("رفض", (dialog, which) -> {
               // استخدام موقع افتراضي
           })
           .show();
}
```

#### ب. رسالة توضيحية لإذن الموقع في الخلفية:
```java
private void showBackgroundLocationPermissionDialog() {
    android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
    builder.setTitle("إذن الموقع في الخلفية")
           .setMessage("لضمان دقة إشعارات أوقات الصلاة عند السفر أو تغيير الموقع، يحتاج التطبيق للوصول إلى موقعك حتى عندما يكون مغلقاً.\n\n" +
                      "هذا يضمن تحديث أوقات الصلاة تلقائياً حسب موقعك الجديد.")
           .setPositiveButton("السماح", (dialog, which) -> {
               // طلب إذن الخلفية
           })
           .setNegativeButton("لاحقاً", (dialog, which) -> {
               // يمكن تفعيله لاحقاً
           })
           .show();
}
```

#### ج. تسلسل طلب الأذونات:
1. **عند فتح صفحة مواقيت الصلاة:** إذا لم تكن هناك أوقات محفوظة، يتم طلب إذن الموقع العادي
2. **بعد منح إذن الموقع العادي:** يتم طلب إذن الموقع في الخلفية تلقائياً
3. **في حالة الرفض:** يتم استخدام موقع افتراضي (الرياض)

## الفوائد:

### 1. **للمستخدم:**
- ✅ فهم واضح لسبب طلب الإذن
- ✅ عدم إزعاج في الصفحة الرئيسية
- ✅ خيار استخدام موقع افتراضي

### 2. **للمطور:**
- ✅ حل مشكلة Google Play
- ✅ كود أكثر تنظيماً
- ✅ تجربة مستخدم أفضل

### 3. **لـ Google Play:**
- ✅ الغرض من الإذن واضح
- ✅ الميزة محددة بدقة
- ✅ يتبع أفضل الممارسات

## النتيجة:
الآن عندما يفتح المستخدم صفحة مواقيت الصلاة لأول مرة، سيرى:

1. **رسالة واضحة** تشرح لماذا يحتاج التطبيق للموقع
2. **خيارات واضحة:** السماح أو الرفض
3. **تسلسل منطقي:** إذن الموقع العادي أولاً، ثم إذن الخلفية
4. **بديل مناسب:** استخدام موقع افتراضي في حالة الرفض

هذا التغيير يحسن تجربة المستخدم ويساعد في حل مشكلة Google Play المتعلقة بوضوح الغرض من أذونات الموقع.

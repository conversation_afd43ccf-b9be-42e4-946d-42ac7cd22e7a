package com.qurany2019.quranyapp.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.lifecycle.LiveData;

import com.qurany2019.quranyapp.database.entities.AzkarEntity;

import java.util.List;

@Dao
public interface AzkarDao {

    // Insert operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertAzkar(AzkarEntity azkar);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAllAzkar(List<AzkarEntity> azkarList);

    // Update operations
    @Update
    int updateAzkar(AzkarEntity azkar);

    // Delete operations
    @Delete
    int deleteAzkar(AzkarEntity azkar);

    @Query("DELETE FROM azkar_table WHERE id = :azkarId")
    int deleteAzkarById(int azkarId);

    @Query("DELETE FROM azkar_table WHERE category = :category")
    int deleteAzkarByCategory(String category);

    @Query("DELETE FROM azkar_table")
    int deleteAllAzkar();

    // Select operations
    @Query("SELECT * FROM azkar_table WHERE id = :azkarId")
    AzkarEntity getAzkarById(int azkarId);

    @Query("SELECT * FROM azkar_table WHERE azkar_id = :azkarId")
    AzkarEntity getAzkarByAzkarId(String azkarId);

    @Query("SELECT * FROM azkar_table WHERE category = :category ORDER BY order_index ASC")
    List<AzkarEntity> getAzkarByCategory(String category);

    @Query("SELECT * FROM azkar_table WHERE category = :category ORDER BY order_index ASC")
    LiveData<List<AzkarEntity>> getAzkarByCategoryLiveData(String category);

    @Query("SELECT * FROM azkar_table ORDER BY category, order_index ASC")
    List<AzkarEntity> getAllAzkar();

    @Query("SELECT * FROM azkar_table ORDER BY category, order_index ASC")
    LiveData<List<AzkarEntity>> getAllAzkarLiveData();

    // Count operations
    @Query("SELECT COUNT(*) FROM azkar_table")
    int getAzkarCount();

    @Query("SELECT COUNT(*) FROM azkar_table WHERE category = :category")
    int getAzkarCountByCategory(String category);

    // Search operations
    @Query("SELECT * FROM azkar_table WHERE title LIKE :searchQuery OR content LIKE :searchQuery ORDER BY category, order_index ASC")
    List<AzkarEntity> searchAzkar(String searchQuery);

    @Query("SELECT * FROM azkar_table WHERE title LIKE :searchQuery OR content LIKE :searchQuery ORDER BY category, order_index ASC")
    LiveData<List<AzkarEntity>> searchAzkarLiveData(String searchQuery);

    // Category operations
    @Query("SELECT DISTINCT category FROM azkar_table ORDER BY category")
    List<String> getAllCategories();

    @Query("SELECT DISTINCT category FROM azkar_table ORDER BY category")
    LiveData<List<String>> getAllCategoriesLiveData();

    // Order operations
    @Query("SELECT MAX(order_index) FROM azkar_table WHERE category = :category")
    int getMaxOrderIndexByCategory(String category);

    @Query("UPDATE azkar_table SET order_index = :newOrderIndex WHERE id = :azkarId")
    int updateAzkarOrder(int azkarId, int newOrderIndex);

    // Bulk operations
    @Query("UPDATE azkar_table SET updated_at = :timestamp WHERE category = :category")
    int updateCategoryTimestamp(String category, long timestamp);

    // Statistics queries
    @Query("SELECT AVG(target_count) FROM azkar_table WHERE category = :category")
    double getAverageTargetCountByCategory(String category);

    @Query("SELECT SUM(target_count) FROM azkar_table WHERE category = :category")
    int getTotalTargetCountByCategory(String category);

    // Recent operations
    @Query("SELECT * FROM azkar_table ORDER BY updated_at DESC LIMIT :limit")
    List<AzkarEntity> getRecentlyUpdatedAzkar(int limit);

    @Query("SELECT * FROM azkar_table ORDER BY created_at DESC LIMIT :limit")
    List<AzkarEntity> getRecentlyCreatedAzkar(int limit);

    // Validation queries
    @Query("SELECT COUNT(*) FROM azkar_table WHERE azkar_id = :azkarId")
    int checkAzkarIdExists(String azkarId);

    @Query("SELECT COUNT(*) FROM azkar_table WHERE category = :category AND order_index = :orderIndex")
    int checkOrderIndexExists(String category, int orderIndex);

    // Maintenance operations
    @Query("UPDATE azkar_table SET updated_at = :timestamp")
    int updateAllTimestamps(long timestamp);

    @Query("SELECT * FROM azkar_table WHERE updated_at < :timestamp")
    List<AzkarEntity> getAzkarOlderThan(long timestamp);
}

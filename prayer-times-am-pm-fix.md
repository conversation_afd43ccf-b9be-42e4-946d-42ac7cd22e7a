# إصلاح مشكلة عرض أوقات الصلاة (AM/PM)

## المشكلة:
أوقات الصلاة المسائية (العصر، المغرب، العشاء) تظهر بـ "ص" بدلاً من "م" في الصفحة الرئيسية.

## السبب:
كان هناك تضارب في طريقة التعامل مع تحويل AM/PM بين:
1. البيانات المحفوظة من API (تحتوي على AM/PM بالإنجليزية)
2. دوال التحويل في MainActivity و MainActivitySimple
3. دالة convertTimeFormat في PrayerTimesActivity

## الحل المطبق:

### 1. تحديث PrayerTimesActivity.java
```java
private String convertTimeFormat(String time) {
    try {
        SharedPreferences prefs = getSharedPreferences("language_prefs", Context.MODE_PRIVATE);
        boolean isArabic = prefs.getBoolean("is_arabic", true);

        if (isArabic) {
            // تحويل AM/PM الإنجليزية إلى العربية
            return time.replace("AM", "ص").replace("PM", "م");
        } else {
            // تحويل العربية إلى الإنجليزية إذا لزم الأمر
            return time.replace("ص", "AM").replace("م", "PM");
        }
    } catch (Exception e) {
        return time;
    }
}
```

### 2. تحديث MainActivity.java
```java
private String convertTo12HourFormat(String time) {
    if (time == null || time.isEmpty()) return time;

    try {
        // تطبيق الترجمة حسب اللغة المختارة
        android.content.SharedPreferences langPrefs = getSharedPreferences("language_prefs", android.content.Context.MODE_PRIVATE);
        boolean isArabic = langPrefs.getBoolean("is_arabic", true);

        // إذا كان الوقت يحتوي على AM/PM بالفعل، قم بالترجمة فقط
        if (time.contains("AM") || time.contains("PM")) {
            if (isArabic) {
                return time.replace("AM", "ص").replace("PM", "م");
            } else {
                return time;
            }
        }

        // إذا كان الوقت يحتوي على ص/م، تحويل للإنجليزية إذا لزم الأمر
        if (time.contains("ص") || time.contains("م")) {
            if (isArabic) {
                return time; // الوقت بالعربية بالفعل
            } else {
                return time.replace("ص", "AM").replace("م", "PM");
            }
        }

        // إذا كان الوقت بتنسيق 24 ساعة، قم بالتحويل
        String cleanTime = time.split(" ")[0];
        SimpleDateFormat input = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
        SimpleDateFormat output = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

        String formattedTime = output.format(input.parse(cleanTime));

        if (isArabic) {
            return formattedTime.replace("AM", "ص").replace("PM", "م");
        } else {
            return formattedTime;
        }
    } catch (Exception e) {
        return time;
    }
}
```

### 3. تحديث MainActivitySimple.java
تم تطبيق نفس التحديث على MainActivitySimple.java

## النتيجة:
- ✅ الفجر: يظهر بـ "ص" (صباحاً)
- ✅ الظهر: يظهر بـ "م" (مساءً) 
- ✅ العصر: يظهر بـ "م" (مساءً)
- ✅ المغرب: يظهر بـ "م" (مساءً)
- ✅ العشاء: يظهر بـ "م" (مساءً)

## الملفات المحدثة:
1. `app/src/main/java/com/qurany2019/quranyapp/PrayerTimesActivity.java`
2. `app/src/main/java/com/qurany2019/quranyapp/MainActivity.java`
3. `app/src/main/java/com/qurany2019/quranyapp/MainActivitySimple.java`

## كيفية عمل الحل:
1. يتحقق الكود من نوع البيانات الموجودة (AM/PM أو ص/م أو 24 ساعة)
2. يطبق التحويل المناسب حسب اللغة المختارة
3. يضمن عدم التحويل المزدوج للبيانات
4. يحافظ على التوافق مع جميع مصادر البيانات

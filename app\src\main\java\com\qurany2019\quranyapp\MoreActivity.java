package com.qurany2019.quranyapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import java.util.Locale;

public class MoreActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق إعدادات اللغة
        applyLanguageSettings();

        setContentView(R.layout.activity_more);

        setupMoreItems();
    }

    private void applyLanguageSettings() {
        SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = langPrefs.getBoolean("is_arabic", true);
        setLocale(isArabic ? "ar" : "en");
    }

    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    private void setupMoreItems() {
        // الإعدادات
        LinearLayout settingsItem = findViewById(R.id.settingsItem);
        if (settingsItem != null) {
            settingsItem.setOnClickListener(v -> {
                animateItem(settingsItem);
                Intent intent = new Intent(this, SettingsActivity.class);
                startActivity(intent);
            });
        }

        // عن التطبيق
        LinearLayout aboutItem = findViewById(R.id.aboutItem);
        if (aboutItem != null) {
            aboutItem.setOnClickListener(v -> {
                animateItem(aboutItem);
                Intent intent = new Intent(this, About.class);
                startActivity(intent);
            });
        }

        // مشاركة التطبيق
        LinearLayout shareItem = findViewById(R.id.shareItem);
        if (shareItem != null) {
            shareItem.setOnClickListener(v -> {
                animateItem(shareItem);
                shareApp();
            });
        }

        // تقييم التطبيق
        LinearLayout rateItem = findViewById(R.id.rateItem);
        if (rateItem != null) {
            rateItem.setOnClickListener(v -> {
                animateItem(rateItem);
                rateApp();
            });
        }

        // السبحة الإلكترونية
        LinearLayout tasbihItem = findViewById(R.id.tasbihItem);
        if (tasbihItem != null) {
            tasbihItem.setOnClickListener(v -> {
                animateItem(tasbihItem);
                Intent intent = new Intent(this, Tazker.class);
                startActivity(intent);
            });
        }

        // حصن المسلم
        LinearLayout hisnItem = findViewById(R.id.hisnItem);
        if (hisnItem != null) {
            hisnItem.setOnClickListener(v -> {
                animateItem(hisnItem);
                Intent intent = new Intent(this, Hisn.class);
                startActivity(intent);
            });
        }



        // أوقات الصلاة
        LinearLayout prayerTimesItem = findViewById(R.id.prayerTimesItem);
        if (prayerTimesItem != null) {
            prayerTimesItem.setOnClickListener(v -> {
                animateItem(prayerTimesItem);
                Intent intent = new Intent(this, PrayerTimesActivity.class);
                startActivity(intent);
            });
        }

        // قراء القرآن
        LinearLayout readersItem = findViewById(R.id.readersItem);
        if (readersItem != null) {
            readersItem.setOnClickListener(v -> {
                animateItem(readersItem);
                Intent intent = new Intent(this, RecitesName.class);
                startActivity(intent);
            });
        }

        // زر الرجوع
        LinearLayout backButton = findViewById(R.id.backButton);
        if (backButton != null) {
            backButton.setOnClickListener(v -> {
                animateItem(backButton);
                finish();
            });
        }
    }

    private void animateItem(View view) {
        view.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction(() -> {
                    view.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start();
                })
                .start();
    }

    private void shareApp() {
        try {
            final String appPackageName = getPackageName();
            String message = getString(R.string.share_message) + " " + getString(R.string.app_name);
            String link = "http://play.google.com/store/apps/details?id=" + appPackageName;
            Intent share = new Intent(Intent.ACTION_SEND);
            share.setType("text/plain");
            share.putExtra(Intent.EXTRA_TEXT, message + "\n" + link);
            Intent chooser = Intent.createChooser(share, "مشاركة التطبيق");
            startActivity(chooser);
        } catch (Exception e) {
            Toast.makeText(this, "حدث خطأ في المشاركة", Toast.LENGTH_SHORT).show();
        }
    }

    private void rateApp() {
        try {
            Intent rateIntent = new Intent(Intent.ACTION_VIEW);
            rateIntent.setData(Uri.parse("market://details?id=" + getPackageName()));
            startActivity(rateIntent);
        } catch (Exception e) {
            Intent rateIntent = new Intent(Intent.ACTION_VIEW);
            rateIntent.setData(Uri.parse("https://play.google.com/store/apps/details?id=" + getPackageName()));
            startActivity(rateIntent);
        }
    }
}

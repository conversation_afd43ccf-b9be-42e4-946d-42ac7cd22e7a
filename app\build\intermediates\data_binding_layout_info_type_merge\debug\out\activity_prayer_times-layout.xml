<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_prayer_times" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_prayer_times.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_prayer_times_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="566" endOffset="51"/></Target><Target id="@+id/app_bar" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="22" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="66"/></Target><Target id="@+id/tvCityName" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="72" endOffset="60"/></Target><Target id="@+id/tvCurrentDate" view="TextView"><Expressions/><location startLine="74" startOffset="20" endLine="82" endOffset="59"/></Target><Target id="@+id/tvCurrentTime" view="TextView"><Expressions/><location startLine="84" startOffset="20" endLine="92" endOffset="60"/></Target><Target id="@+id/tvNextPrayer" view="TextView"><Expressions/><location startLine="94" startOffset="20" endLine="103" endOffset="55"/></Target><Target id="@+id/cardFajr" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="120" startOffset="12" endLine="179" endOffset="47"/></Target><Target id="@+id/tvFajr" view="TextView"><Expressions/><location startLine="169" startOffset="20" endLine="176" endOffset="68"/></Target><Target id="@+id/cardSunrise" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="182" startOffset="12" endLine="241" endOffset="47"/></Target><Target id="@+id/tvSunrise" view="TextView"><Expressions/><location startLine="231" startOffset="20" endLine="238" endOffset="71"/></Target><Target id="@+id/cardDhuhr" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="244" startOffset="12" endLine="303" endOffset="47"/></Target><Target id="@+id/tvDhuhr" view="TextView"><Expressions/><location startLine="293" startOffset="20" endLine="300" endOffset="69"/></Target><Target id="@+id/cardAsr" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="306" startOffset="12" endLine="365" endOffset="47"/></Target><Target id="@+id/tvAsr" view="TextView"><Expressions/><location startLine="355" startOffset="20" endLine="362" endOffset="67"/></Target><Target id="@+id/cardMaghrib" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="368" startOffset="12" endLine="427" endOffset="47"/></Target><Target id="@+id/tvMaghrib" view="TextView"><Expressions/><location startLine="417" startOffset="20" endLine="424" endOffset="71"/></Target><Target id="@+id/cardIsha" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="430" startOffset="12" endLine="489" endOffset="47"/></Target><Target id="@+id/tvIsha" view="TextView"><Expressions/><location startLine="479" startOffset="20" endLine="486" endOffset="68"/></Target><Target id="@+id/switchNotifications" view="Switch"><Expressions/><location startLine="530" startOffset="24" endLine="534" endOffset="52"/></Target><Target id="@+id/tvNotificationInfo" view="TextView"><Expressions/><location startLine="539" startOffset="20" endLine="546" endOffset="56"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="558" startOffset="4" endLine="564" endOffset="57"/></Target></Targets></Layout>
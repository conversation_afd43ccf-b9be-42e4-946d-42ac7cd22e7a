package com.qurany2019.quranyapp.viewmodel;

import android.app.Application;
import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.qurany2019.quranyapp.database.entities.AzkarEntity;
import com.qurany2019.quranyapp.database.entities.AzkarProgressEntity;
import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;
import com.qurany2019.quranyapp.repository.AzkarRepository;

import java.util.List;

public class AzkarViewModel extends AndroidViewModel {
    private AzkarRepository repository;
    
    // LiveData for UI
    private LiveData<List<AzkarEntity>> allAzkar;
    private LiveData<List<AzkarProgressEntity>> todayProgress;
    private LiveData<List<AzkarStatisticsEntity>> todayStatistics;
    private LiveData<List<AchievementEntity>> allAchievements;
    
    // MutableLiveData for events
    private MutableLiveData<String> toastMessage = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private MutableLiveData<AchievementEntity> newAchievementUnlocked = new MutableLiveData<>();

    public AzkarViewModel(@NonNull Application application) {
        super(application);
        repository = new AzkarRepository(application);
        
        // Initialize LiveData
        allAzkar = repository.getAllAzkar();
        todayProgress = repository.getTodayProgress();
        todayStatistics = repository.getTodayStatistics();
        allAchievements = repository.getAllAchievements();
        
        // Initialize default data if needed
        repository.initializeDefaultData();
    }

    // ==================== GETTERS FOR LIVEDATA ====================
    
    public LiveData<List<AzkarEntity>> getAllAzkar() {
        return allAzkar;
    }
    
    public LiveData<List<AzkarEntity>> getAzkarByCategory(String category) {
        return repository.getAzkarByCategory(category);
    }
    
    public LiveData<List<AzkarProgressEntity>> getTodayProgress() {
        return todayProgress;
    }
    
    public LiveData<AzkarProgressEntity> getAzkarProgress(int azkarId) {
        return repository.getAzkarProgress(azkarId);
    }
    
    public LiveData<List<AzkarStatisticsEntity>> getTodayStatistics() {
        return todayStatistics;
    }
    
    public LiveData<AzkarStatisticsEntity> getCategoryStatistics(String category) {
        return repository.getCategoryStatistics(category);
    }
    
    public LiveData<List<AchievementEntity>> getAllAchievements() {
        return allAchievements;
    }
    
    public LiveData<List<AchievementEntity>> getUnlockedAchievements() {
        return repository.getUnlockedAchievements();
    }
    
    // Event LiveData
    public LiveData<String> getToastMessage() {
        return toastMessage;
    }
    
    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }
    
    public LiveData<AchievementEntity> getNewAchievementUnlocked() {
        return newAchievementUnlocked;
    }

    // ==================== AZKAR OPERATIONS ====================
    
    public void insertAzkar(AzkarEntity azkar) {
        repository.insertAzkar(azkar);
    }
    
    public void insertAllAzkar(List<AzkarEntity> azkarList) {
        repository.insertAllAzkar(azkarList);
    }
    
    public void updateAzkar(AzkarEntity azkar) {
        repository.updateAzkar(azkar);
    }
    
    public void deleteAzkar(AzkarEntity azkar) {
        repository.deleteAzkar(azkar);
    }

    // ==================== PROGRESS OPERATIONS ====================
    
    public void incrementAzkarProgress(int azkarId) {
        repository.incrementAzkarProgress(azkarId);
        
        // Check for achievements
        checkAchievements();
        
        // Show completion message if needed
        // This will be handled by observing the progress LiveData
    }
    
    public void resetAzkarProgress(int azkarId) {
        repository.resetAzkarProgress(azkarId);
        showToast("تم إعادة تعيين التقدم");
    }
    
    public void insertProgress(AzkarProgressEntity progress) {
        repository.insertProgress(progress);
    }
    
    public void updateProgress(AzkarProgressEntity progress) {
        repository.updateProgress(progress);
    }

    // ==================== ACHIEVEMENT OPERATIONS ====================
    
    public void insertAchievement(AchievementEntity achievement) {
        repository.insertAchievement(achievement);
    }
    
    public void updateAchievementProgress(String achievementId, int increment) {
        repository.updateAchievementProgress(achievementId, increment);
    }
    
    public void unlockAchievement(String achievementId) {
        repository.unlockAchievement(achievementId);
    }
    
    private void checkAchievements() {
        // This method will check if any achievements should be unlocked
        // Based on current progress and statistics
        
        // Example achievement checks:
        // - Complete first azkar
        // - Complete 10 azkar in a day
        // - Maintain a 7-day streak
        // etc.
        
        // For now, we'll implement basic achievement checking
        // This can be expanded based on specific achievement requirements
    }

    // ==================== UI HELPER METHODS ====================
    
    public void showToast(String message) {
        toastMessage.setValue(message);
    }
    
    public void setLoading(boolean loading) {
        isLoading.setValue(loading);
    }
    
    public void showAchievementUnlocked(AchievementEntity achievement) {
        newAchievementUnlocked.setValue(achievement);
    }
    
    // Clear event after consumption
    public void clearToastMessage() {
        toastMessage.setValue(null);
    }
    
    public void clearAchievementUnlocked() {
        newAchievementUnlocked.setValue(null);
    }

    // ==================== UTILITY METHODS ====================
    
    public String getCurrentDate() {
        return repository.getCurrentDate();
    }
    
    // Calculate completion percentage for a category
    public float calculateCategoryCompletion(String category, List<AzkarProgressEntity> progressList) {
        if (progressList == null || progressList.isEmpty()) {
            return 0f;
        }
        
        int completed = 0;
        int total = progressList.size();
        
        for (AzkarProgressEntity progress : progressList) {
            if (progress.isCompleted()) {
                completed++;
            }
        }
        
        return total > 0 ? ((float) completed / total) * 100 : 0f;
    }
    
    // Calculate total tasbih count
    public int calculateTotalTasbihCount(List<AzkarProgressEntity> progressList) {
        if (progressList == null || progressList.isEmpty()) {
            return 0;
        }
        
        int total = 0;
        for (AzkarProgressEntity progress : progressList) {
            total += progress.getCurrentCount();
        }
        
        return total;
    }
    
    // Check if all azkar in category are completed
    public boolean isCategoryCompleted(List<AzkarProgressEntity> progressList) {
        if (progressList == null || progressList.isEmpty()) {
            return false;
        }
        
        for (AzkarProgressEntity progress : progressList) {
            if (!progress.isCompleted()) {
                return false;
            }
        }
        
        return true;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        repository.cleanup();
    }
}

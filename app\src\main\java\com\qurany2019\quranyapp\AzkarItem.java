package com.qurany2019.quranyapp;

public class AzkarItem {
    private String title;
    private String content;
    private int count;
    private int currentCount;
    private boolean isCompleted;
    private String azkarId;
    private long lastReadTime;

    public AzkarItem(String title, String content, int count) {
        this.title = title;
        this.content = content;
        this.count = count;
        this.currentCount = 0;
        this.isCompleted = false;
        this.azkarId = generateId(title, content);
        this.lastReadTime = 0;
    }

    // Generate unique ID for each azkar
    private String generateId(String title, String content) {
        return String.valueOf((title + content).hashCode());
    }

    // Getters
    public String getTitle() {
        return title;
    }

    public String getContent() {
        return content;
    }

    public int getCount() {
        return count;
    }

    public int getCurrentCount() {
        return currentCount;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public String getAzkarId() {
        return azkarId;
    }

    public long getLastReadTime() {
        return lastReadTime;
    }

    public int getRemainingCount() {
        return count - currentCount;
    }

    public float getProgress() {
        if (count == 0) return 1.0f;
        return (float) currentCount / count;
    }

    public int getProgressPercentage() {
        return Math.round(getProgress() * 100);
    }

    // Setters
    public void setTitle(String title) {
        this.title = title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public void setCurrentCount(int currentCount) {
        this.currentCount = Math.min(currentCount, count);
        this.isCompleted = (this.currentCount >= count);
        this.lastReadTime = System.currentTimeMillis();
    }

    public void setLastReadTime(long lastReadTime) {
        this.lastReadTime = lastReadTime;
    }

    // Helper methods
    public void incrementCount() {
        if (currentCount < count) {
            currentCount++;
            this.lastReadTime = System.currentTimeMillis();
            if (currentCount >= count) {
                isCompleted = true;
            }
        }
    }

    public void resetCount() {
        currentCount = 0;
        isCompleted = false;
        lastReadTime = 0;
    }

    public void markAsCompleted() {
        this.currentCount = count;
        this.isCompleted = true;
        this.lastReadTime = System.currentTimeMillis();
    }

    public String getCountText() {
        return currentCount + " / " + count;
    }

    public String getStatusText() {
        if (isCompleted) {
            return "مكتمل ✓";
        } else if (currentCount > 0) {
            return currentCount + " من " + count;
        } else {
            return "لم يبدأ";
        }
    }

    public String getProgressText() {
        return currentCount + "/" + count;
    }

    // Status check methods
    public boolean isStarted() {
        return currentCount > 0;
    }

    public boolean isInProgress() {
        return currentCount > 0 && currentCount < count;
    }

    public boolean isNotStarted() {
        return currentCount == 0;
    }

    // For debugging
    @Override
    public String toString() {
        return "AzkarItem{" +
                "title='" + title + '\'' +
                ", currentCount=" + currentCount +
                ", count=" + count +
                ", completed=" + isCompleted +
                '}';
    }

    // Equals and hashCode for proper comparison
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        AzkarItem azkarItem = (AzkarItem) obj;
        return azkarId.equals(azkarItem.azkarId);
    }

    @Override
    public int hashCode() {
        return azkarId.hashCode();
    }
}

package com.qurany2019.quranyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;
import java.util.Locale;

/**
 * BaseActivity - نشاط أساسي لضمان تطبيق اللغة على جميع الأنشطة
 */
public abstract class BaseActivity extends AppCompatActivity {
    
    private static final String TAG = "BaseActivity";
    private static final String LANGUAGE_PREFS = "language_prefs";
    private static final String IS_ARABIC_KEY = "is_arabic";
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // تطبيق اللغة قبل استدعاء super.onCreate()
        applyLanguageSettings();
        super.onCreate(savedInstanceState);
        Log.d(TAG, "Language applied for: " + this.getClass().getSimpleName());
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // التحقق من تغيير اللغة عند العودة للنشاط
        checkAndApplyLanguageChange();
    }
    
    /**
     * تطبيق إعدادات اللغة المحفوظة
     */
    private void applyLanguageSettings() {
        try {
            SharedPreferences langPrefs = getSharedPreferences(LANGUAGE_PREFS, MODE_PRIVATE);
            boolean isArabic = langPrefs.getBoolean(IS_ARABIC_KEY, true);
            String languageCode = isArabic ? "ar" : "en";
            
            // تطبيق اللغة على السياق الحالي
            setLocale(languageCode);
            
            Log.d(TAG, "Applied language: " + languageCode + " for " + this.getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Error applying language settings: " + e.getMessage());
            // في حالة الخطأ، استخدم العربية كافتراضي
            setLocale("ar");
        }
    }
    
    /**
     * التحقق من تغيير اللغة وتطبيقها إذا لزم الأمر
     */
    private void checkAndApplyLanguageChange() {
        try {
            SharedPreferences langPrefs = getSharedPreferences(LANGUAGE_PREFS, MODE_PRIVATE);
            boolean isArabic = langPrefs.getBoolean(IS_ARABIC_KEY, true);
            String expectedLanguage = isArabic ? "ar" : "en";
            String currentLanguage = getCurrentLanguage();
            
            // إذا كانت اللغة الحالية مختلفة عن المتوقعة، أعد تطبيق اللغة
            if (!expectedLanguage.equals(currentLanguage)) {
                Log.d(TAG, "Language mismatch detected. Expected: " + expectedLanguage + ", Current: " + currentLanguage);
                setLocale(expectedLanguage);
                
                // إعادة إنشاء النشاط لتطبيق التغييرات
                recreate();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error checking language change: " + e.getMessage());
        }
    }
    
    /**
     * تعيين اللغة للسياق الحالي
     */
    private void setLocale(String languageCode) {
        try {
            Locale locale = new Locale(languageCode);
            Locale.setDefault(locale);
            
            Configuration config = new Configuration();
            config.locale = locale;
            
            // تطبيق التكوين على الموارد
            getResources().updateConfiguration(config, getResources().getDisplayMetrics());
            
            Log.d(TAG, "Locale set to: " + languageCode);
        } catch (Exception e) {
            Log.e(TAG, "Error setting locale: " + e.getMessage());
        }
    }
    
    /**
     * الحصول على اللغة الحالية
     */
    private String getCurrentLanguage() {
        try {
            return getResources().getConfiguration().locale.getLanguage();
        } catch (Exception e) {
            Log.e(TAG, "Error getting current language: " + e.getMessage());
            return "ar"; // افتراضي
        }
    }
    
    /**
     * دالة مساعدة للحصول على حالة اللغة العربية
     */
    protected boolean isArabicLanguage() {
        try {
            SharedPreferences langPrefs = getSharedPreferences(LANGUAGE_PREFS, MODE_PRIVATE);
            return langPrefs.getBoolean(IS_ARABIC_KEY, true);
        } catch (Exception e) {
            Log.e(TAG, "Error checking Arabic language: " + e.getMessage());
            return true; // افتراضي
        }
    }
    
    /**
     * دالة مساعدة لحفظ إعدادات اللغة
     */
    protected void saveLanguageSettings(boolean isArabic) {
        try {
            SharedPreferences langPrefs = getSharedPreferences(LANGUAGE_PREFS, MODE_PRIVATE);
            SharedPreferences.Editor editor = langPrefs.edit();
            editor.putBoolean(IS_ARABIC_KEY, isArabic);
            editor.apply();
            
            Log.d(TAG, "Language settings saved: " + (isArabic ? "Arabic" : "English"));
        } catch (Exception e) {
            Log.e(TAG, "Error saving language settings: " + e.getMessage());
        }
    }
    
    /**
     * إنشاء سياق مع اللغة المحددة (للاستخدام في الخدمات)
     */
    public static Context createLanguageContext(Context context, String languageCode) {
        try {
            Locale locale = new Locale(languageCode);
            Configuration config = new Configuration(context.getResources().getConfiguration());
            config.setLocale(locale);
            return context.createConfigurationContext(config);
        } catch (Exception e) {
            Log.e(TAG, "Error creating language context: " + e.getMessage());
            return context;
        }
    }
}

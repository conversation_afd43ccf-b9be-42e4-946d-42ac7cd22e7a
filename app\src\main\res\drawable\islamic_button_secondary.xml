<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/islamic_gold_light" />
            <corners android:radius="12dp" />
            <stroke
                android:width="2dp"
                android:color="@color/islamic_green_dark" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/islamic_green" />
        </shape>
    </item>
</selector>

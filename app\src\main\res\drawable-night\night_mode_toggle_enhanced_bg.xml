<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط في الوضع الليلي -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="25dp" />
            <gradient
                android:startColor="#FF6F00"
                android:endColor="#FFD700"
                android:angle="45" />
            <stroke android:width="2dp" android:color="#FFC107" />
        </shape>
    </item>
    
    <!-- الحالة العادية في الوضع الليلي -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="25dp" />
            <gradient
                android:startColor="#FF9800"
                android:endColor="#FFD700"
                android:angle="45" />
            <stroke android:width="2dp" android:color="#FF6F00" />
        </shape>
    </item>
</selector>

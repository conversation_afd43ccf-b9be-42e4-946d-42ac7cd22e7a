package com.qurany2019.quranyapp.utils;

import android.content.Context;
import com.qurany2019.quranyapp.database.AzkarDatabase;
import com.qurany2019.quranyapp.database.entities.AzkarEntity;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AzkarDataInitializer {
    
    private static final ExecutorService executor = Executors.newSingleThreadExecutor();
    
    public static void initializeDefaultData(Context context) {
        executor.execute(() -> {
            AzkarDatabase database = AzkarDatabase.getInstance(context);

            // التحقق من وجود بيانات مسبقة
            if (database.azkarDao().getAzkarCount() == 0) {
                insertDefaultAzkar(database);
            }

            // إنشاء الإنجازات الافتراضية مقفلة - تفتح عند تحقيق الشروط
            int achievementCount = database.achievementDao().getAchievementCount();
            if (achievementCount == 0) {
                insertDefaultLockedAchievements(database);
            } else {
                // احذف جميع الإنجازات وأعد إنشاءها بمعرفات الترجمة الجديدة
                database.achievementDao().deleteAllAchievements();
                insertDefaultLockedAchievements(database);
            }
        });
    }
    
    private static void insertDefaultAzkar(AzkarDatabase database) {
        // أذكار الصباح
        database.azkarDao().insertAzkar(new AzkarEntity("morning_1", "آية الكرسي",
            "اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ", 1, "morning", 1));
        database.azkarDao().insertAzkar(new AzkarEntity("morning_2", "سورة الإخلاص",
            "قُلْ هُوَ اللَّهُ أَحَدٌ", 3, "morning", 2));
        database.azkarDao().insertAzkar(new AzkarEntity("morning_3", "دعاء الصباح",
            "أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ", 1, "morning", 3));
        database.azkarDao().insertAzkar(new AzkarEntity("morning_4", "التسبيح",
            "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ", 100, "morning", 4));
        database.azkarDao().insertAzkar(new AzkarEntity("morning_5", "التهليل",
            "لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ", 100, "morning", 5));
        
        // أذكار المساء
        database.azkarDao().insertAzkar(new AzkarEntity("evening_1", "آية الكرسي",
            "اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ", 1, "evening", 1));
        database.azkarDao().insertAzkar(new AzkarEntity("evening_2", "سورة الإخلاص",
            "قُلْ هُوَ اللَّهُ أَحَدٌ", 3, "evening", 2));
        database.azkarDao().insertAzkar(new AzkarEntity("evening_3", "دعاء المساء",
            "أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ", 1, "evening", 3));
        database.azkarDao().insertAzkar(new AzkarEntity("evening_4", "التسبيح",
            "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ", 100, "evening", 4));
        database.azkarDao().insertAzkar(new AzkarEntity("evening_5", "الاستغفار",
            "أَسْتَغْفِرُ اللَّهَ وَأَتُوبُ إِلَيْهِ", 100, "evening", 5));
        
        // أذكار النوم
        database.azkarDao().insertAzkar(new AzkarEntity("sleep_1", "دعاء النوم",
            "بِاسْمِكَ رَبِّي وَضَعْتُ جَنْبِي", 1, "sleep", 1));
        database.azkarDao().insertAzkar(new AzkarEntity("sleep_2", "آية الكرسي",
            "اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ", 1, "sleep", 2));
        database.azkarDao().insertAzkar(new AzkarEntity("sleep_3", "التسبيح",
            "سُبْحَانَ اللَّهِ", 33, "sleep", 3));
        database.azkarDao().insertAzkar(new AzkarEntity("sleep_4", "الحمد",
            "الْحَمْدُ لِلَّهِ", 33, "sleep", 4));
        database.azkarDao().insertAzkar(new AzkarEntity("sleep_5", "التكبير",
            "اللَّهُ أَكْبَرُ", 34, "sleep", 5));

        // أذكار الاستيقاظ
        database.azkarDao().insertAzkar(new AzkarEntity("wake_1", "دعاء الاستيقاظ",
            "الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا", 1, "wake", 1));
        database.azkarDao().insertAzkar(new AzkarEntity("wake_2", "التهليل",
            "لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ", 1, "wake", 2));

        // أذكار بعد الصلاة
        database.azkarDao().insertAzkar(new AzkarEntity("prayer_1", "الاستغفار",
            "أَسْتَغْفِرُ اللَّهَ", 3, "prayer", 1));
        database.azkarDao().insertAzkar(new AzkarEntity("prayer_2", "دعاء السلام",
            "اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ", 1, "prayer", 2));
        database.azkarDao().insertAzkar(new AzkarEntity("prayer_3", "التسبيح",
            "سُبْحَانَ اللَّهِ", 33, "prayer", 3));
        database.azkarDao().insertAzkar(new AzkarEntity("prayer_4", "الحمد",
            "الْحَمْدُ لِلَّهِ", 33, "prayer", 4));
        database.azkarDao().insertAzkar(new AzkarEntity("prayer_5", "التكبير",
            "اللَّهُ أَكْبَرُ", 34, "prayer", 5));
    }

    private static void insertDefaultLockedAchievements(AzkarDatabase database) {
        // === إنجازات البداية (بسيطة جداً) ===
        createAchievementWithStringId(database, "first_step", "achievement_first_step", "achievement_first_step_desc", "beginner", 1, 5);
        createAchievementWithStringId(database, "morning_light", "achievement_morning_light", "achievement_morning_light_desc", "beginner", 1, 10);
        createAchievementWithStringId(database, "evening_peace", "achievement_evening_peace", "achievement_evening_peace_desc", "beginner", 1, 10);
        createAchievementWithStringId(database, "sleep_blessing", "achievement_sleep_blessing", "achievement_sleep_blessing_desc", "beginner", 1, 10);
        createAchievementWithStringId(database, "five_dhikr", "achievement_five_dhikr", "achievement_five_dhikr_desc", "beginner", 5, 15);

        // === إنجازات يومية (بسيطة) ===
        createAchievementWithStringId(database, "daily_starter", "achievement_daily_starter", "achievement_daily_starter_desc", "daily", 10, 20);
        createAchievementWithStringId(database, "morning_evening", "achievement_morning_evening", "achievement_morning_evening_desc", "daily", 2, 25);
        createAchievementWithStringId(database, "twenty_five", "achievement_twenty_five", "achievement_twenty_five_desc", "daily", 25, 30);
        createAchievementWithStringId(database, "half_hundred", "achievement_half_hundred", "achievement_half_hundred_desc", "daily", 50, 40);
        createAchievementWithStringId(database, "daily_champion", "achievement_daily_champion", "achievement_daily_champion_desc", "daily", 100, 60);

        // === إنجازات أسبوعية (متوسطة) ===
        createAchievementWithStringId(database, "three_days", "achievement_three_days", "achievement_three_days_desc", "weekly", 3, 50);
        createAchievementWithStringId(database, "blessed_week", "achievement_blessed_week", "achievement_blessed_week_desc", "weekly", 7, 100);
        createAchievementWithStringId(database, "two_weeks", "achievement_two_weeks", "achievement_two_weeks_desc", "weekly", 14, 150);
        createAchievementWithStringId(database, "weekly_warrior", "achievement_weekly_warrior", "achievement_weekly_warrior_desc", "weekly", 500, 120);
        createAchievementWithStringId(database, "consistent_week", "achievement_consistent_week", "achievement_consistent_week_desc", "weekly", 14, 130);

        // === إنجازات شهرية (متقدمة) ===
        createAchievementWithStringId(database, "golden_month", "achievement_golden_month", "achievement_golden_month_desc", "monthly", 30, 300);
        createAchievementWithStringId(database, "month_master", "achievement_month_master", "achievement_month_master_desc", "monthly", 1000, 250);
        createAchievementWithStringId(database, "forty_days", "achievement_forty_days", "achievement_forty_days_desc", "monthly", 40, 400);
        createAchievementWithStringId(database, "two_months", "achievement_two_months", "achievement_two_months_desc", "monthly", 60, 600);
        createAchievementWithStringId(database, "monthly_legend", "achievement_monthly_legend", "achievement_monthly_legend_desc", "monthly", 2000, 500);

        // === إنجازات المعالم (صعبة) ===
        createAchievementWithStringId(database, "hundred_dhikr", "achievement_hundred_dhikr", "achievement_hundred_dhikr_desc", "milestone", 100, 80);
        createAchievementWithStringId(database, "five_hundred", "achievement_five_hundred", "achievement_five_hundred_desc", "milestone", 500, 150);
        createAchievementWithStringId(database, "thousand_dhikr", "achievement_thousand_dhikr", "achievement_thousand_dhikr_desc", "milestone", 1000, 300);
        createAchievementWithStringId(database, "five_thousand", "achievement_five_thousand", "achievement_five_thousand_desc", "milestone", 5000, 600);
        createAchievementWithStringId(database, "ten_thousand", "achievement_ten_thousand", "achievement_ten_thousand_desc", "milestone", 10000, 1000);

        // === إنجازات التحدي (صعبة جداً) ===
        createAchievementWithStringId(database, "hundred_days", "achievement_hundred_days", "achievement_hundred_days_desc", "challenge", 100, 800);
        createAchievementWithStringId(database, "half_year", "achievement_half_year", "achievement_half_year_desc", "challenge", 180, 1200);
        createAchievementWithStringId(database, "blessed_year", "achievement_blessed_year", "achievement_blessed_year_desc", "challenge", 365, 2000);
        createAchievementWithStringId(database, "twenty_five_thousand", "achievement_twenty_five_thousand", "achievement_twenty_five_thousand_desc", "challenge", 25000, 1500);
        createAchievementWithStringId(database, "fifty_thousand", "achievement_fifty_thousand", "achievement_fifty_thousand_desc", "challenge", 50000, 2500);

        // === إنجازات الأساطير (مستحيلة تقريباً) ===
        createAchievementWithStringId(database, "two_years", "achievement_two_years", "achievement_two_years_desc", "legendary", 730, 3000);
        createAchievementWithStringId(database, "hundred_thousand", "achievement_hundred_thousand", "achievement_hundred_thousand_desc", "legendary", 100000, 4000);
        createAchievementWithStringId(database, "three_years", "achievement_three_years", "achievement_three_years_desc", "legendary", 1095, 5000);
        createAchievementWithStringId(database, "million_dhikr", "achievement_million_dhikr", "achievement_million_dhikr_desc", "legendary", 1000000, 10000);
        createAchievementWithStringId(database, "eternal_remembrance", "achievement_eternal_remembrance", "achievement_eternal_remembrance_desc", "legendary", 1825, 15000);
    }

    // دالة مساعدة لإنشاء الإنجازات
    private static void createAchievement(AzkarDatabase database, String id, String title, String description, String type, int target, int points) {
        AchievementEntity achievement = new AchievementEntity(id, title, description, type, target, "general");
        achievement.setRewardPoints(points);
        achievement.setIconResource("ic_achievement");
        database.achievementDao().insertAchievement(achievement);
    }

    // دالة مساعدة لإنشاء الإنجازات باستخدام معرفات النصوص
    private static void createAchievementWithStringId(AzkarDatabase database, String id, String titleStringId, String descStringId, String type, int target, int points) {
        // سنحفظ معرف النص بدلاً من النص نفسه
        // سيتم تحميل النص الفعلي عند عرض الإنجاز حسب اللغة المختارة
        AchievementEntity achievement = new AchievementEntity(id, titleStringId, descStringId, type, target, "general");
        achievement.setRewardPoints(points);
        achievement.setIconResource("ic_achievement");
        database.achievementDao().insertAchievement(achievement);
    }
}

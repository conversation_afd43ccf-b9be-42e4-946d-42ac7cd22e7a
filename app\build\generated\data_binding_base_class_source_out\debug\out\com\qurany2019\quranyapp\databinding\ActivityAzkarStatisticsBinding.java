// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.github.mikephil.charting.charts.BarChart;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAzkarStatisticsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final TextView completedAzkarCount;

  @NonNull
  public final TextView completionPercentage;

  @NonNull
  public final ProgressBar overallProgressBar;

  @NonNull
  public final TextView sessionDuration;

  @NonNull
  public final TextView streakDays;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView totalAzkarCount;

  @NonNull
  public final TextView totalTasbihCount;

  @NonNull
  public final BarChart weeklyChart;

  private ActivityAzkarStatisticsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull TextView completedAzkarCount, @NonNull TextView completionPercentage,
      @NonNull ProgressBar overallProgressBar, @NonNull TextView sessionDuration,
      @NonNull TextView streakDays, @NonNull Toolbar toolbar, @NonNull TextView totalAzkarCount,
      @NonNull TextView totalTasbihCount, @NonNull BarChart weeklyChart) {
    this.rootView = rootView;
    this.completedAzkarCount = completedAzkarCount;
    this.completionPercentage = completionPercentage;
    this.overallProgressBar = overallProgressBar;
    this.sessionDuration = sessionDuration;
    this.streakDays = streakDays;
    this.toolbar = toolbar;
    this.totalAzkarCount = totalAzkarCount;
    this.totalTasbihCount = totalTasbihCount;
    this.weeklyChart = weeklyChart;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAzkarStatisticsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAzkarStatisticsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_azkar_statistics, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAzkarStatisticsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.completedAzkarCount;
      TextView completedAzkarCount = ViewBindings.findChildViewById(rootView, id);
      if (completedAzkarCount == null) {
        break missingId;
      }

      id = R.id.completionPercentage;
      TextView completionPercentage = ViewBindings.findChildViewById(rootView, id);
      if (completionPercentage == null) {
        break missingId;
      }

      id = R.id.overallProgressBar;
      ProgressBar overallProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (overallProgressBar == null) {
        break missingId;
      }

      id = R.id.sessionDuration;
      TextView sessionDuration = ViewBindings.findChildViewById(rootView, id);
      if (sessionDuration == null) {
        break missingId;
      }

      id = R.id.streakDays;
      TextView streakDays = ViewBindings.findChildViewById(rootView, id);
      if (streakDays == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.totalAzkarCount;
      TextView totalAzkarCount = ViewBindings.findChildViewById(rootView, id);
      if (totalAzkarCount == null) {
        break missingId;
      }

      id = R.id.totalTasbihCount;
      TextView totalTasbihCount = ViewBindings.findChildViewById(rootView, id);
      if (totalTasbihCount == null) {
        break missingId;
      }

      id = R.id.weeklyChart;
      BarChart weeklyChart = ViewBindings.findChildViewById(rootView, id);
      if (weeklyChart == null) {
        break missingId;
      }

      return new ActivityAzkarStatisticsBinding((CoordinatorLayout) rootView, completedAzkarCount,
          completionPercentage, overallProgressBar, sessionDuration, streakDays, toolbar,
          totalAzkarCount, totalTasbihCount, weeklyChart);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.qurany2019.quranyapp;

import android.app.Application;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.util.Log;
import androidx.appcompat.app.AppCompatDelegate;
import java.util.Locale;

public class QuranApplication extends Application {

    private static final String TAG = "QuranApplication";

    @Override
    public void onCreate() {
        super.onCreate();

        // تطبيق إعدادات اللغة عند بدء التطبيق
        applyLanguageSettings();

        // تطبيق الوضع الليلي عند بدء التطبيق
        applyDarkModeSettings();
    }

    private void applyDarkModeSettings() {
        SharedPreferences prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        boolean isDarkMode = prefs.getBoolean("dark_mode", false);
        
        if (isDarkMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }

    private void applyLanguageSettings() {
        try {
            SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
            boolean isArabic = langPrefs.getBoolean("is_arabic", true);
            String languageCode = isArabic ? "ar" : "en";

            // تطبيق اللغة على مستوى التطبيق
            Locale locale = new Locale(languageCode);
            Locale.setDefault(locale);

            Configuration config = new Configuration();
            config.locale = locale;
            getResources().updateConfiguration(config, getResources().getDisplayMetrics());

            Log.d(TAG, "Global language applied: " + languageCode);
        } catch (Exception e) {
            Log.e(TAG, "Error applying global language settings: " + e.getMessage());
        }
    }
}

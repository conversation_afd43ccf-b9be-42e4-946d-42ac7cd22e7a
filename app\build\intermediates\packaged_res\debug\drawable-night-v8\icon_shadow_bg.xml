<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- الظل في الوضع الليلي -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="oval">
            <solid android:color="#60000000" />
        </shape>
    </item>
    
    <!-- الخلفية الرئيسية للوضع الليلي -->
    <item android:bottom="2dp" android:right="2dp">
        <selector>
            <!-- حالة الضغط في الوضع الليلي -->
            <item android:state_pressed="true">
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#FFEB3B"
                        android:endColor="#FFA000"
                        android:angle="135"
                        android:type="linear" />
                    <stroke
                        android:width="2dp"
                        android:color="#FF6F00" />
                </shape>
            </item>
            
            <!-- الحالة العادية في الوضع الليلي -->
            <item>
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#FFD700"
                        android:endColor="#FF9800"
                        android:angle="135"
                        android:type="linear" />
                    <stroke
                        android:width="1dp"
                        android:color="#FFCC02" />
                </shape>
            </item>
        </selector>
    </item>
    
</layer-list>

<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="#E6A000"
                android:endColor="#CC8F00" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="#FFD700"
                android:endColor="#FFA000" />
            <stroke
                android:width="2dp"
                android:color="#FFFFFF" />
        </shape>
    </item>
    
</selector>

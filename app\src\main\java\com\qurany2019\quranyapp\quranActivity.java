package com.qurany2019.quranyapp;

import android.content.Context;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;
import android.graphics.Typeface;
import android.os.Bundle;
import android.app.Activity;
import android.text.method.ScrollingMovementMethod;
import android.util.Log;
import android.util.TypedValue;
import android.view.GestureDetector;
import android.view.GestureDetector.OnGestureListener;
import android.view.Menu;
import android.view.MotionEvent;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import java.util.ArrayList;
import java.util.List;
import static android.graphics.Color.parseColor;

public class quranActivity extends Activity implements OnGestureListener {

    GestureDetector gDetector;
    TextView tv;
    private static final int SWIPE_MIN_DISTANCE = 120;
    private static final int SWIPE_MAX_OFF_PATH = 250;
    private static final int SWIPE_THRESHOLD_VELOCITY = 200;
    private static final int SWIPE_THRESHOLD = 100;
    private static final int SWIPE_VELOCITY_THRESHOLD = 100;

    int appendonce = 299;

    pref pf;
    Context context;
    TextView quranMain;
    LinearLayout sc;
    ScrollView scrl;
    Spinner suraSelector;
    LinearLayout menuBar;

    int curSura;
    int curFont;
    float fontSize;
    int fontDp;
    ImageView nextSuraB;
    ImageView prevSuraB;
    List<ImageView> btns;
    List<Typeface> fonts;
    Typeface tf;

    String bgColor = "#ff000000";
    String ayaNumColor = "#dddddd";
    String ayaColor = "#EDEDED";
    int colorMode;

    String colors[][] = {
            {"Night Modern", "#ff000000", "#dddddd", "#EDEDED"},
            {"Light", "#ffffffff", "#0000dd", "#101010"}
    };

    String fontNames[] = { "uthmanic_hafs", "PDMS_Saleem", "MADDINA", "AMER", "UthmanTNB", "noorehidayat" };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.d("Activity", "Started");
        super.onCreate(savedInstanceState);

        // إزالة fullscreen mode لإظهار شريط الأزرار بشكل طبيعي

        setContentView(R.layout.quran);

        gDetector = new GestureDetector(this);
        context = getApplicationContext();
        pf = new pref(context);

        curSura = pf.curSura;
        curFont = pf.curFont;
        colorMode = pf.colorMode;
        fontSize = pf.fontSize;

        bgColor = colors[colorMode][1];
        ayaNumColor = colors[colorMode][2];
        ayaColor = colors[colorMode][3];

        sc = (LinearLayout) findViewById(R.id.Snd);
        scrl = (ScrollView) findViewById(R.id.scrollView);
        menuBar = (LinearLayout) findViewById(R.id.menuBar);
        suraSelector = (Spinner) findViewById(R.id.suraSelect);

        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, android.R.id.text1);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        for (int i = 0; i < quran.arnames.length; i++) {
            spinnerAdapter.add((i + 1) + ": " + quran.arnames[i]);
        }
        spinnerAdapter.notifyDataSetChanged();

        suraSelector.post(new Runnable() {
            public void run() {
                suraSelector.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                        int s = (int) id;
                        ((TextView) suraSelector.getChildAt(0)).setTextColor(parseColor(ayaColor));
                        print_quran(s);
                    }
                    public void onNothingSelected(AdapterView<?> parent) { }
                });
            }
        });
        suraSelector.setAdapter(spinnerAdapter);
        suraSelector.setSelection(curSura);

        Typeface FA = Typeface.createFromAsset(getAssets(), "fontawesome.ttf");

        fonts = new ArrayList<>();
        fonts.add(Typeface.createFromAsset(getAssets(), "uthmanic_hafs.ttf"));
        fonts.add(Typeface.createFromAsset(getAssets(), "PDMS_Saleem.ttf"));
        fonts.add(Typeface.createFromAsset(getAssets(), "MADDINA.ttf"));
        fonts.add(Typeface.createFromAsset(getAssets(), "AMER.ttf"));
        fonts.add(Typeface.createFromAsset(getAssets(), "UthmanTNB.ttf"));
        fonts.add(Typeface.createFromAsset(getAssets(), "QUR_STD.TTF"));
        fonts.add(Typeface.createFromAsset(getAssets(), "noorehidayat.ttf"));

        tf = fonts.get(curFont);

        quranMain = (TextView) findViewById(R.id.quranMain);
        quranMain.setMovementMethod(new ScrollingMovementMethod());
        quranMain.setTypeface(tf);
        quranMain.setTextSize(fontSize);
        quranMain.setTextColor(parseColor(ayaColor));
        sc.setBackgroundColor(parseColor(bgColor));
        quranMain.setBackgroundColor(parseColor(bgColor));

        quranMain.setMovementMethod(new ScrollingMovementMethod());
        quranMain.setTextIsSelectable(true);
        quranMain.setFocusable(true);
        quranMain.setFocusableInTouchMode(true);

        print_quran(curSura);
        quranMain.setTextSize(TypedValue.COMPLEX_UNIT_DIP, fontSize);

        nextSuraB = (ImageView) findViewById(R.id.suraNext);
        nextSuraB.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                nextSura();
            }
        });

        prevSuraB = (ImageView) findViewById(R.id.suraPrev);
        prevSuraB.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                prevSura();
            }
        });

        ImageView fontChangeB = (ImageView) findViewById(R.id.fontChange);
        fontChangeB.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                fontChange();
            }
        });

        final ImageView fontPlusB = (ImageView) findViewById(R.id.fontPlus);
        fontPlusB.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                fontPlus();
            }
        });

        final ImageView fontMinusB = (ImageView) findViewById(R.id.fontMinus);
        fontMinusB.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                fontMinus();
            }
        });

        final ImageView colorModeB = (ImageView) findViewById(R.id.colorMode);
        colorModeB.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                colorMode();
            }
        });

        btns = new ArrayList<>();
        btns.add(nextSuraB);
        btns.add(prevSuraB);
        btns.add(fontPlusB);
        btns.add(fontMinusB);
        btns.add(colorModeB);
        btns.add(fontChangeB);

        System.out.println(suraSelector.getChildCount());
    }
    public void fontChange() {
        if (curFont < fonts.size() - 1)
            curFont++;
        else
            curFont = 0;

        quranMain.setTypeface(fonts.get(curFont));

        // نضيف حماية قبل عرض التوست
        if (curFont >= 0 && curFont < fontNames.length) {
            Toast.makeText(context, "Font: " + fontNames[curFont], Toast.LENGTH_SHORT).show();
        } else {
            Log.e("fontChange", "Invalid fontNames index: " + curFont);
        }
    }

    public void nextSura() {
        int sura = curSura + 1;
        if (sura > 113) {
            Toast.makeText(this, getString(R.string.last_sura), Toast.LENGTH_SHORT).show();
        } else {
            suraSelector.setSelection(sura);
            print_quran(sura);
        }
    }

    public void prevSura() {
        int sura = curSura - 1;
        if (sura < 0) {
            Toast.makeText(this, getString(R.string.first_sura), Toast.LENGTH_SHORT).show();
        } else {
            suraSelector.setSelection(sura);
            print_quran(sura);
        }
    }

    public void fontPlus() {
        Float factor = 1.1f;
        fontSize = fontSize * factor;
        quranMain.setTextSize(TypedValue.COMPLEX_UNIT_DIP, fontSize);
    }

    public void fontMinus() {
        Float factor = 0.9f;
        fontSize = fontSize * factor;
        quranMain.setTextSize(TypedValue.COMPLEX_UNIT_DIP, fontSize);
    }

    public void colorMode() {
        if (colorMode < colors.length - 1)
            colorMode++;
        else
            colorMode = 0;
        bgColor = colors[colorMode][1];
        ayaColor = colors[colorMode][3];
        quranMain.setBackgroundColor(parseColor(bgColor));
        quranMain.setTextColor(parseColor(ayaColor));
        sc.setBackgroundColor(parseColor(bgColor));
        print_quran(curSura);
    }

    public void print_quran(final int sura) {
        Thread q = new Thread(new Runnable() {
            @Override
            public void run() {
                runOnUiThread(() -> {
                    curSura = sura;
                    quran q = new quran();
                    String[] verses = null;
                    quranMain.setText("");
                    TextView suraTitle = findViewById(R.id.suraTitle);
                    Typeface surahTypeface = Typeface.createFromAsset(getAssets(), "fonts/Qurraan.otf");
                    suraTitle.setTypeface(surahTypeface);
                    suraTitle.setText(getString(R.string.surah_title_prefix) + " " + q.arnames[sura]);
                    String newText = "";
                    newText += (sura + 1) + ": " + q.arnames[sura] + "\n";
                    if (sura > 113 || sura < 0) {
                        Toast.makeText(context, getString(R.string.sura_not_found), Toast.LENGTH_SHORT).show();
                    }
                    try {
                        verses = q.qread(sura);
                        for (int x = 0; x < verses.length && x <= appendonce; x++) {
                            newText += verses[x] + "  " + (x + 1) + "  ";
                        }
                        quranMain.setText(newText);
                    } catch (NullPointerException npe) {
                        System.out.println("NPE: " + npe.getMessage());
                    }
                });
            }
        });
        q.start();
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        super.dispatchTouchEvent(ev);
        return gDetector.onTouchEvent(ev);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override
    public boolean onDown(MotionEvent arg0) {
        return true;
    }

    public void rightToLeft() { nextSura(); }
    public void lefttoRight() { prevSura(); }

    @Override
    public boolean onFling(MotionEvent start, MotionEvent finish, float velocityX, float velocityY) {
        if (Math.abs(start.getY() - finish.getY()) > SWIPE_MAX_OFF_PATH)
            return false;
        if (start.getX() - finish.getX() > SWIPE_MIN_DISTANCE && Math.abs(velocityX) > SWIPE_THRESHOLD_VELOCITY) {
            lefttoRight();
        } else if (finish.getX() - start.getX() > SWIPE_MIN_DISTANCE && Math.abs(velocityX) > SWIPE_THRESHOLD_VELOCITY) {
            rightToLeft();
        }
        return true;
    }

    @Override
    public boolean onTouchEvent(MotionEvent me) {
        return gDetector.onTouchEvent(me);
    }

    @Override
    public void onLongPress(MotionEvent arg0) {
        if (menuBar.getVisibility() == View.VISIBLE)
            menuBar.setVisibility(View.GONE);
        else
            menuBar.setVisibility(View.VISIBLE);
        Log.d("onLongPress", "in onlongpress");
    }

    @Override
    public void onShowPress(MotionEvent arg0) {
        Log.d("onShowPress", "in onShowPress");
    }

    @Override
    public boolean onSingleTapUp(MotionEvent arg0) {
        Log.d("onSingleTapup", "in onsingletapup");
        return true;
    }

    @Override
    public boolean onScroll(MotionEvent arg0, MotionEvent arg1, float arg2, float arg3) {
        int ScrY = scrl.getScrollY();
        // إصلاح: إبقاء الشريط مرئيًا دائمًا دون إبهات
        menuBar.setAlpha(1.0f); // 1.0 = غير شفاف
        return false;
    }

    @Override
    protected void onDestroy() {
        pf.save(fontSize, curSura, colorMode, curFont);
        Log.d("Activity", "onDestroy");
        super.onDestroy();
    }
}
<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#1B5E20"
                android:endColor="#2E7D32" />
            <stroke
                android:width="2dp"
                android:color="@color/islamic_gold_light" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#2E7D32"
                android:endColor="#4CAF50" />
            <stroke
                android:width="1dp"
                android:color="@color/islamic_green_dark" />
        </shape>
    </item>
</selector>

package com.qurany2019.quranyapp.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.lifecycle.LiveData;

import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;

import java.util.List;

@Dao
public interface AzkarStatisticsDao {

    // Insert operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertStatistics(AzkarStatisticsEntity statistics);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAllStatistics(List<AzkarStatisticsEntity> statisticsList);

    // Update operations
    @Update
    int updateStatistics(AzkarStatisticsEntity statistics);

    // Delete operations
    @Delete
    int deleteStatistics(AzkarStatisticsEntity statistics);

    @Query("DELETE FROM azkar_statistics_table WHERE id = :statisticsId")
    int deleteStatisticsById(int statisticsId);

    @Query("DELETE FROM azkar_statistics_table WHERE date = :date")
    int deleteStatisticsByDate(String date);

    @Query("DELETE FROM azkar_statistics_table WHERE date = :date AND category = :category")
    int deleteStatisticsByDateAndCategory(String date, String category);

    @Query("DELETE FROM azkar_statistics_table WHERE date < :date")
    int deleteStatisticsOlderThan(String date);

    @Query("DELETE FROM azkar_statistics_table")
    int deleteAllStatistics();

    // Select operations
    @Query("SELECT * FROM azkar_statistics_table WHERE id = :statisticsId")
    AzkarStatisticsEntity getStatisticsById(int statisticsId);

    @Query("SELECT * FROM azkar_statistics_table WHERE date = :date AND category = :category")
    AzkarStatisticsEntity getStatisticsByDateAndCategory(String date, String category);

    @Query("SELECT * FROM azkar_statistics_table WHERE date = :date AND category = :category")
    LiveData<AzkarStatisticsEntity> getStatisticsByDateAndCategoryLiveData(String date, String category);

    @Query("SELECT * FROM azkar_statistics_table WHERE date = :date ORDER BY category")
    List<AzkarStatisticsEntity> getStatisticsByDate(String date);

    @Query("SELECT * FROM azkar_statistics_table WHERE date = :date ORDER BY category")
    LiveData<List<AzkarStatisticsEntity>> getStatisticsByDateLiveData(String date);

    @Query("SELECT * FROM azkar_statistics_table WHERE category = :category ORDER BY date DESC")
    List<AzkarStatisticsEntity> getStatisticsByCategory(String category);

    @Query("SELECT * FROM azkar_statistics_table WHERE category = :category ORDER BY date DESC")
    LiveData<List<AzkarStatisticsEntity>> getStatisticsByCategoryLiveData(String category);

    @Query("SELECT * FROM azkar_statistics_table ORDER BY date DESC, category")
    List<AzkarStatisticsEntity> getAllStatistics();

    @Query("SELECT * FROM azkar_statistics_table ORDER BY date DESC, category")
    LiveData<List<AzkarStatisticsEntity>> getAllStatisticsLiveData();

    // Date range queries
    @Query("SELECT * FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    List<AzkarStatisticsEntity> getStatisticsBetweenDates(String startDate, String endDate);

    @Query("SELECT * FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    LiveData<List<AzkarStatisticsEntity>> getStatisticsBetweenDatesLiveData(String startDate, String endDate);

    @Query("SELECT * FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate AND category = :category ORDER BY date DESC")
    List<AzkarStatisticsEntity> getStatisticsBetweenDatesAndCategory(String startDate, String endDate, String category);

    // Completion statistics
    @Query("SELECT * FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY date DESC")
    List<AzkarStatisticsEntity> getFullyCompletedStatistics();

    @Query("SELECT * FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY date DESC")
    LiveData<List<AzkarStatisticsEntity>> getFullyCompletedStatisticsLiveData();

    @Query("SELECT COUNT(*) FROM azkar_statistics_table WHERE completion_percentage = 100.0 AND date BETWEEN :startDate AND :endDate")
    int getFullyCompletedDaysCount(String startDate, String endDate);

    @Query("SELECT COUNT(*) FROM azkar_statistics_table WHERE completion_percentage = 100.0 AND category = :category AND date BETWEEN :startDate AND :endDate")
    int getFullyCompletedDaysCountByCategory(String startDate, String endDate, String category);

    // Aggregation queries
    @Query("SELECT AVG(completion_percentage) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate")
    double getAverageCompletionPercentage(String startDate, String endDate);

    @Query("SELECT AVG(completion_percentage) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate AND category = :category")
    double getAverageCompletionPercentageByCategory(String startDate, String endDate, String category);

    @Query("SELECT SUM(total_tasbih_count) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate")
    int getTotalTasbihCount(String startDate, String endDate);

    @Query("SELECT SUM(total_tasbih_count) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate AND category = :category")
    int getTotalTasbihCountByCategory(String startDate, String endDate, String category);

    @Query("SELECT SUM(session_duration) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate")
    long getTotalSessionDuration(String startDate, String endDate);

    @Query("SELECT AVG(session_duration) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate AND session_duration > 0")
    double getAverageSessionDuration(String startDate, String endDate);

    // Best performance queries
    @Query("SELECT MIN(best_completion_time) FROM azkar_statistics_table WHERE best_completion_time > 0")
    long getBestOverallCompletionTime();

    @Query("SELECT MAX(completion_percentage) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate")
    double getBestCompletionPercentage(String startDate, String endDate);

    @Query("SELECT MAX(total_tasbih_count) FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate")
    int getMaxTasbihCountInDay(String startDate, String endDate);

    @Query("SELECT MAX(streak_days) FROM azkar_statistics_table")
    int getMaxStreakDays();

    // Recent statistics
    @Query("SELECT * FROM azkar_statistics_table ORDER BY updated_at DESC LIMIT :limit")
    List<AzkarStatisticsEntity> getRecentStatistics(int limit);

    @Query("SELECT * FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY updated_at DESC LIMIT :limit")
    List<AzkarStatisticsEntity> getRecentCompletions(int limit);

    // Category analysis
    @Query("SELECT DISTINCT category FROM azkar_statistics_table ORDER BY category")
    List<String> getAllCategories();

    @Query("SELECT category FROM azkar_statistics_table WHERE date BETWEEN :startDate AND :endDate GROUP BY category ORDER BY AVG(completion_percentage) DESC")
    List<String> getCategoryPerformanceAnalysis(String startDate, String endDate);

    // Streak calculations
    @Query("SELECT DISTINCT date FROM azkar_statistics_table WHERE completion_percentage = 100.0 ORDER BY date DESC")
    List<String> getFullCompletionDates();

    @Query("SELECT COUNT(DISTINCT date) FROM azkar_statistics_table WHERE completion_percentage = 100.0 AND date BETWEEN :startDate AND :endDate")
    int getFullCompletionDaysCount(String startDate, String endDate);

    // Maintenance operations
    @Query("UPDATE azkar_statistics_table SET updated_at = :timestamp WHERE date = :date")
    int updateDateTimestamp(String date, long timestamp);

    @Query("SELECT * FROM azkar_statistics_table WHERE updated_at < :timestamp")
    List<AzkarStatisticsEntity> getStatisticsOlderThan(long timestamp);

    // Validation
    @Query("SELECT COUNT(*) FROM azkar_statistics_table WHERE date = :date AND category = :category")
    int checkStatisticsExists(String date, String category);

    // Custom update operations
    @Query("UPDATE azkar_statistics_table SET total_azkar_count = :totalCount, completed_azkar_count = :completedCount, completion_percentage = :percentage, updated_at = :timestamp WHERE date = :date AND category = :category")
    int updateCompletionStats(String date, String category, int totalCount, int completedCount, float percentage, long timestamp);

    @Query("UPDATE azkar_statistics_table SET total_tasbih_count = total_tasbih_count + :count, updated_at = :timestamp WHERE date = :date AND category = :category")
    int addTasbihCount(String date, String category, int count, long timestamp);

    @Query("UPDATE azkar_statistics_table SET session_duration = session_duration + :duration, updated_at = :timestamp WHERE date = :date AND category = :category")
    int addSessionDuration(String date, String category, long duration, long timestamp);

    @Query("UPDATE azkar_statistics_table SET streak_days = :streakDays, updated_at = :timestamp WHERE date = :date AND category = :category")
    int updateStreakDays(String date, String category, int streakDays, long timestamp);
}

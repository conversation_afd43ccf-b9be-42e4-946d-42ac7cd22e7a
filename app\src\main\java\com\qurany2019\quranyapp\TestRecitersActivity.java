package com.qurany2019.quranyapp;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;

public class TestRecitersActivity extends AppCompatActivity {

    private ListView listView;
    private ArrayList<String> reciterNames;
    private ArrayList<String> reciterCodes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recites_name);

        // إعداد ListView
        listView = findViewById(R.id.listView);
        
        // إنشاء قائمة القراء يدوياً للاختبار
        createTestRecitersList();
        
        // إعداد الـ Adapter
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
            android.R.layout.simple_list_item_1, reciterNames);
        listView.setAdapter(adapter);
        
        // إعداد النقر على العناصر
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                String selectedReciter = reciterCodes.get(position);
                String selectedName = reciterNames.get(position);
                
                Toast.makeText(TestRecitersActivity.this, 
                    "تم اختيار: " + selectedName, Toast.LENGTH_SHORT).show();
                
                // الانتقال إلى صفحة السور
                navigateToSurahList(selectedReciter);
            }
        });
        
        Log.d("TestReciters", "تم تحميل " + reciterNames.size() + " قارئ");
    }

    private void createTestRecitersList() {
        reciterNames = new ArrayList<>();
        reciterCodes = new ArrayList<>();
        
        // إضافة القراء المشهورين
        addReciter("basit", "عبد الباسط عبد الصمد");
        addReciter("afs", "مشاري العفاسي");
        addReciter("maher", "ماهر المعيقلي");
        addReciter("shur", "سعود الشريم");
        addReciter("sds", "عبدالرحمن السديس");
        addReciter("yasser", "ياسر الدوسري");
        addReciter("sobhi", "إسلام صبحي");
        addReciter("saud", "أحمد سعود");
        addReciter("qatami", "ناصر القطامي");
        addReciter("fares", "فارس عباد");
        addReciter("shatri", "أبو بكر الشاطري");
        addReciter("ajm", "أحمد بن علي العجمي");
        addReciter("husary", "محمود خليل الحصري");
        addReciter("minshawi", "محمد صديق المنشاوي");
        addReciter("tablawi", "محمد محمود الطبلاوي");
        addReciter("rifai", "ياسر الرفاعي");
        addReciter("ghamdi", "سعد الغامدي");
        addReciter("juhani", "عبدالله الجهني");
        addReciter("budair", "عبدالله بدير");
        addReciter("shuraim", "سعود الشريم");
    }
    
    private void addReciter(String code, String name) {
        reciterCodes.add(code);
        reciterNames.add(name);
    }

    private void navigateToSurahList(String reciterName) {
        try {
            // محاولة الانتقال إلى ModernAyaListActivity أولاً
            Intent intent = new Intent(this, ModernAyaListActivity.class);
            intent.putExtra("RecitesName", reciterName);
            startActivity(intent);
            Log.d("TestReciters", "انتقال إلى ModernAyaListActivity مع القارئ: " + reciterName);
        } catch (Exception e) {
            // في حالة عدم وجود ModernAyaListActivity، استخدم AyaList العادي
            try {
                Intent intent = new Intent(this, AyaList.class);
                intent.putExtra("RecitesName", reciterName);
                startActivity(intent);
                Log.d("TestReciters", "انتقال إلى AyaList مع القارئ: " + reciterName);
            } catch (Exception ex) {
                Log.e("TestReciters", "فشل في الانتقال إلى صفحة السور: " + ex.getMessage());
                Toast.makeText(this, "خطأ في فتح صفحة السور", Toast.LENGTH_SHORT).show();
            }
        }
    }
}

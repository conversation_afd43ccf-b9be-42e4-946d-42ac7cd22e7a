package com.qurany2019.quranyapp;

import android.content.DialogInterface;
import android.content.SharedPreferences;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.InputType;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;
import android.content.SharedPreferences;
import android.os.Bundle;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import java.util.ArrayList;
/**
 * Created by java_dude on 06/06/18.
 */
public class Tazker extends AppCompatActivity {
    private AdView mAdView;

    TextView Tzekr, tvTotalCount, tvDailyTarget;
    TextView[] tvCounts = new TextView[5]; // عرض عدادات كل ذكر
    Button counter;
    int currentDhikrIndex = 0;
    int[] dhikrCounts = new int[5]; // عدادات الأذكار الخمسة
    String currentDhikr;
    ArrayList<String> azkar;
    private SharedPreferences pref;
    private int dailyTarget = 100;
    private int totalDailyCount = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق إعدادات اللغة المحفوظة
        applyLanguageSettings();

        // إزالة fullscreen mode لإظهار شريط الأزرار بشكل طبيعي
        setContentView(R.layout.activity_tazker);

        // إعداد الإعلانات
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        // إعداد شريط الأدوات
        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);

        // ربط العناصر
        counter = (Button) findViewById(R.id.counter);
        Tzekr = (TextView) findViewById(R.id.txtzekr);
        tvTotalCount = (TextView) findViewById(R.id.tvTotalCount);
        tvDailyTarget = (TextView) findViewById(R.id.tvDailyTarget);

        // ربط عدادات كل ذكر
        tvCounts[0] = (TextView) findViewById(R.id.tvCount0);
        tvCounts[1] = (TextView) findViewById(R.id.tvCount1);
        tvCounts[2] = (TextView) findViewById(R.id.tvCount2);
        tvCounts[3] = (TextView) findViewById(R.id.tvCount3);
        tvCounts[4] = (TextView) findViewById(R.id.tvCount4);

        // إعداد الأذكار باستخدام LnaguageClass
        azkar = new ArrayList<String>();
        String[] azkarArray = LnaguageClass.getAzkarArray();
        for (String dhikr : azkarArray) {
            azkar.add(dhikr);
        }

        // تحميل البيانات المحفوظة
        loadSavedData();

        // تحديث الواجهة
        updateDisplay();


    }
    // تحميل البيانات المحفوظة
    private void loadSavedData() {
        pref = getSharedPreferences("tasbih_data", MODE_PRIVATE);
        currentDhikrIndex = pref.getInt("current_dhikr_index", 0);

        for (int i = 0; i < dhikrCounts.length; i++) {
            dhikrCounts[i] = pref.getInt("dhikr_count_" + i, 0);
        }

        totalDailyCount = pref.getInt("total_daily_count", 0);
        dailyTarget = pref.getInt("daily_target", 100);
    }

    // حفظ البيانات
    private void saveData() {
        SharedPreferences.Editor editor = pref.edit();
        editor.putInt("current_dhikr_index", currentDhikrIndex);

        for (int i = 0; i < dhikrCounts.length; i++) {
            editor.putInt("dhikr_count_" + i, dhikrCounts[i]);
        }

        editor.putInt("total_daily_count", totalDailyCount);
        editor.putInt("daily_target", dailyTarget);
        editor.apply();
    }

    // تحديث الواجهة
    private void updateDisplay() {
        currentDhikr = azkar.get(currentDhikrIndex);
        Tzekr.setText(currentDhikr);
        counter.setText(String.valueOf(dhikrCounts[currentDhikrIndex]));
        tvTotalCount.setText(String.valueOf(totalDailyCount));
        tvDailyTarget.setText(String.valueOf(dailyTarget));

        // تحديث عدادات كل ذكر
        for (int i = 0; i < tvCounts.length; i++) {
            tvCounts[i].setText(String.valueOf(dhikrCounts[i]));
        }
    }

    public void next(View view) {
        if (currentDhikrIndex < azkar.size() - 1) {
            currentDhikrIndex++;
            updateDisplay();
        }
    }
    public void prev(View view) {
        if (currentDhikrIndex > 0) {
            currentDhikrIndex--;
            updateDisplay();
        }
    }

    public void count(View view) {
        dhikrCounts[currentDhikrIndex]++;
        totalDailyCount++;
        updateDisplay();
        saveData();
        checkTarget();
    }

    public void resetCounter(View view) {
        showResetDialog();
    }

    private void showResetDialog() {
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle(getString(R.string.reset_counters_title));

            String[] options = {
                getString(R.string.reset_current_only),
                getString(R.string.reset_all_statistics)
            };

            builder.setItems(options, (dialog, which) -> {
                if (which == 0) {
                    // إعادة تعيين الذكر الحالي فقط
                    showConfirmResetCurrentDialog();
                } else if (which == 1) {
                    // إعادة تعيين جميع الإحصائيات
                    showConfirmResetAllDialog();
                }
                dialog.dismiss();
            });

            builder.setNegativeButton(getString(R.string.cancel_dialog), (dialog, which) -> dialog.dismiss());

            AlertDialog dialog = builder.create();
            dialog.show();
        } catch (Exception e) {
            // في حالة فشل الحوار، اعرض الخيار المباشر
            showConfirmResetCurrentDialog();
        }
    }

    private void showConfirmResetCurrentDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.confirm_reset_title));
        builder.setMessage(String.format(getString(R.string.confirm_reset_current_message), azkar.get(currentDhikrIndex)));

        builder.setPositiveButton(getString(R.string.yes_button), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dhikrCounts[currentDhikrIndex] = 0;
                updateDisplay();
                saveData();
                showMessage(String.format(getString(R.string.counter_reset_message), azkar.get(currentDhikrIndex)));
                dialog.dismiss();
            }
        });

        builder.setNegativeButton(getString(R.string.cancel_dialog), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        builder.show();
    }

    private void showConfirmResetAllDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.confirm_reset_all_title));
        builder.setMessage(getString(R.string.confirm_reset_all_message));

        builder.setPositiveButton(getString(R.string.yes_delete_all), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // إعادة تعيين جميع العدادات
                for (int i = 0; i < dhikrCounts.length; i++) {
                    dhikrCounts[i] = 0;
                }
                totalDailyCount = 0;
                updateDisplay();
                saveData();
                showMessage(getString(R.string.all_statistics_reset_message));
                dialog.dismiss();
            }
        });

        builder.setNegativeButton(getString(R.string.cancel_dialog), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        builder.show();
    }

    public void setTarget(View view) {
        showTargetDialog();
    }

    private void showTargetDialog() {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.target_dialog_title));
        builder.setMessage(getString(R.string.target_dialog_message));

        // إنشاء EditText لإدخال الهدف مع تصميم جميل
        final android.widget.EditText input = new android.widget.EditText(this);
        input.setInputType(android.text.InputType.TYPE_CLASS_NUMBER);
        input.setHint(getString(R.string.target_input_hint));
        input.setText(String.valueOf(dailyTarget));
        input.setGravity(android.view.Gravity.CENTER);
        input.setTextSize(22);
        input.setPadding(30, 25, 30, 25);

        // تحسين مظهر حقل الإدخال
        try {
            input.setTextColor(getResources().getColor(android.R.color.black));
            input.setHintTextColor(getResources().getColor(android.R.color.darker_gray));
            input.setBackgroundResource(android.R.drawable.edit_text);
        } catch (Exception e) {
            // في حالة فشل التلوين، استخدم الافتراضي
        }

        // إضافة padding للـ EditText
        android.widget.LinearLayout container = new android.widget.LinearLayout(this);
        container.setOrientation(android.widget.LinearLayout.VERTICAL);
        container.setPadding(60, 30, 60, 30);

        // إضافة نص توضيحي إضافي
        android.widget.TextView hintText = new android.widget.TextView(this);
        hintText.setText(getString(R.string.target_hint_text));
        hintText.setTextSize(14);
        hintText.setGravity(android.view.Gravity.CENTER);
        hintText.setPadding(0, 0, 0, 20);
        try {
            hintText.setTextColor(getResources().getColor(android.R.color.darker_gray));
        } catch (Exception e) {
            // استخدام اللون الافتراضي
        }

        container.addView(hintText);
        container.addView(input);

        // إضافة أزرار سريعة للأهداف الشائعة
        android.widget.TextView quickLabel = new android.widget.TextView(this);
        quickLabel.setText(getString(R.string.quick_goals_label));
        quickLabel.setTextSize(16);
        quickLabel.setGravity(android.view.Gravity.CENTER);
        quickLabel.setPadding(0, 25, 0, 15);
        quickLabel.setTypeface(null, android.graphics.Typeface.BOLD);
        container.addView(quickLabel);

        android.widget.LinearLayout buttonsContainer = new android.widget.LinearLayout(this);
        buttonsContainer.setOrientation(android.widget.LinearLayout.HORIZONTAL);
        buttonsContainer.setGravity(android.view.Gravity.CENTER);

        int[] quickTargets = {33, 100, 300, 500, 1000};
        for (int target : quickTargets) {
            android.widget.Button quickBtn = new android.widget.Button(this);
            quickBtn.setText(String.valueOf(target));
            quickBtn.setTextSize(12);
            quickBtn.setPadding(15, 10, 15, 10);

            android.widget.LinearLayout.LayoutParams btnParams = new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT);
            btnParams.setMargins(8, 0, 8, 0);
            quickBtn.setLayoutParams(btnParams);

            quickBtn.setOnClickListener(v -> {
                input.setText(String.valueOf(target));
                input.setSelection(input.getText().length());
            });
            buttonsContainer.addView(quickBtn);
        }
        container.addView(buttonsContainer);

        builder.setView(container);

        builder.setPositiveButton(getString(R.string.save_button), (dialog, which) -> {
            String targetText = input.getText().toString().trim();
            if (!targetText.isEmpty()) {
                try {
                    int newTarget = Integer.parseInt(targetText);
                    if (newTarget > 0 && newTarget <= 10000) {
                        dailyTarget = newTarget;
                        updateDisplay();
                        saveData();
                        showMessage("✅ تم حفظ هدفك اليومي: " + newTarget + " تسبيحة");
                    } else {
                        showMessage("⚠️ يرجى إدخال رقم بين 1 و 10000");
                    }
                } catch (NumberFormatException e) {
                    showMessage("❌ يرجى إدخال رقم صحيح");
                }
            }
            dialog.dismiss();
        });

        builder.setNegativeButton(getString(R.string.cancel_button), (dialog, which) -> dialog.dismiss());

        androidx.appcompat.app.AlertDialog dialog = builder.create();
        dialog.show();

        // تحسين مظهر الأزرار
        try {
            android.widget.Button positiveButton = dialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_POSITIVE);
            android.widget.Button negativeButton = dialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_NEGATIVE);

            if (positiveButton != null) {
                positiveButton.setTextSize(16);
                positiveButton.setTypeface(null, android.graphics.Typeface.BOLD);
            }

            if (negativeButton != null) {
                negativeButton.setTextSize(16);
            }
        } catch (Exception e) {
            // في حالة فشل تخصيص الأزرار
        }
    }

    private void checkTarget() {
        if (dhikrCounts[currentDhikrIndex] % 33 == 0 && dhikrCounts[currentDhikrIndex] > 0) {
            showCongratulationsDialog();
        }
    }

    private void showCongratulationsDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(getString(R.string.congratulations_title));
        builder.setMessage(String.format(getString(R.string.congratulations_message), dhikrCounts[currentDhikrIndex]));
        builder.setIcon(R.mipmap.ic_launcher);
        builder.setPositiveButton(getString(R.string.congratulations_button), new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        builder.show();
    }

    // دالة عرض الرسائل
    private void showMessage(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    // دالة تطبيق إعدادات اللغة
    private void applyLanguageSettings() {
        SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = langPrefs.getBoolean("is_arabic", true);
        setLocale(isArabic ? "ar" : "en");
    }

    private void setLocale(String languageCode) {
        java.util.Locale locale = new java.util.Locale(languageCode);
        java.util.Locale.setDefault(locale);
        android.content.res.Configuration config = new android.content.res.Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

}
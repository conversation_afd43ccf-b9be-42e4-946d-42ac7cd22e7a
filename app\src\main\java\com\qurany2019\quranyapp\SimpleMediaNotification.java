package com.qurany2019.quranyapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;
import androidx.core.app.NotificationCompat;

/**
 * خدمة إشعارات بسيطة وفعالة - بدون تعقيدات
 */
public class SimpleMediaNotification extends Service {

    private static final String CHANNEL_ID = "simple_quran_channel";
    private static final int NOTIFICATION_ID = 2001;
    private static final String TAG = "SimpleMediaNotification";

    // معلومات التشغيل
    public static String currentTitle = "القرآن الكريم";
    public static String currentReciter = "";
    public static boolean isPlaying = false;

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        Log.d(TAG, "تم إنشاء الخدمة البسيطة");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        String action = intent != null ? intent.getAction() : null;
        Log.d(TAG, "استقبال أمر: " + action);

        if ("SHOW".equals(action)) {
            showNotification();
        } else if ("HIDE".equals(action)) {
            stopForeground(true);
            stopSelf();
        } else if ("PLAY_PAUSE".equals(action)) {
            handlePlayPause();
        } else if ("NEXT".equals(action)) {
            handleNext();
        } else if ("PREVIOUS".equals(action)) {
            handlePrevious();
        } else {
            showNotification();
        }

        return START_STICKY;
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "تشغيل القرآن",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("إشعارات تشغيل القرآن الكريم");
            channel.setShowBadge(false);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    private void showNotification() {
        // إنشاء PendingIntent لفتح التطبيق
        Intent openAppIntent = new Intent(this, managerdb.class);
        openAppIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);

        PendingIntent openAppPendingIntent = PendingIntent.getActivity(
            this, 0, openAppIntent, getPendingIntentFlags()
        );

        // إنشاء أزرار التحكم
        PendingIntent playPausePendingIntent = createActionPendingIntent("PLAY_PAUSE", 1);
        PendingIntent nextPendingIntent = createActionPendingIntent("NEXT", 2);
        PendingIntent previousPendingIntent = createActionPendingIntent("PREVIOUS", 3);

        String title = currentTitle;
        String text = currentReciter.isEmpty() ? "القرآن الكريم" : currentReciter;

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_media_play)
            .setContentTitle(title)
            .setContentText(text)
            .setContentIntent(openAppPendingIntent)
            .setOngoing(isPlaying)
            .setAutoCancel(false)
            .setShowWhen(false)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_TRANSPORT)

            // أزرار التحكم
            .addAction(
                android.R.drawable.ic_media_previous,
                "السابق",
                previousPendingIntent
            )
            .addAction(
                isPlaying ? android.R.drawable.ic_media_pause : android.R.drawable.ic_media_play,
                isPlaying ? "إيقاف" : "تشغيل",
                playPausePendingIntent
            )
            .addAction(
                android.R.drawable.ic_media_next,
                "التالي",
                nextPendingIntent
            );

        Notification notification = builder.build();
        startForeground(NOTIFICATION_ID, notification);
        Log.d(TAG, "تم عرض الإشعار");
    }

    private PendingIntent createActionPendingIntent(String action, int requestCode) {
        // استخدام static method مباشر - الطريقة الأبسط والأضمن
        Intent intent = new Intent();
        intent.setAction("STATIC_MEDIA_CONTROL");
        intent.putExtra("action", action);

        // إنشاء PendingIntent يستدعي static method
        return PendingIntent.getBroadcast(this, requestCode, intent, getPendingIntentFlags());
    }

    private int getPendingIntentFlags() {
        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }
        return flags;
    }

    private void handlePlayPause() {
        Log.d(TAG, "🔥 بدء معالجة تشغيل/إيقاف");

        try {
            // إرسال broadcast مباشر - أبسط وأضمن
            Intent broadcastIntent = new Intent("SIMPLE_MEDIA_CONTROL");
            broadcastIntent.putExtra("action", "PLAY_PAUSE");

            Log.d(TAG, "🚀 إرسال Broadcast");
            Log.d(TAG, "Action: SIMPLE_MEDIA_CONTROL");
            Log.d(TAG, "Extra: PLAY_PAUSE");

            sendBroadcast(broadcastIntent);
            Log.d(TAG, "✅ تم إرسال Broadcast بنجاح");

            // تبديل الحالة
            isPlaying = !isPlaying;
            Log.d(TAG, "🔄 تم تبديل الحالة إلى: " + isPlaying);

            // تحديث الإشعار
            showNotification();
            Log.d(TAG, "📱 تم تحديث الإشعار");

        } catch (Exception e) {
            Log.e(TAG, "❌ خطأ في معالجة تشغيل/إيقاف: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleNext() {
        Log.d(TAG, "معالجة التالي");

        Intent appIntent = new Intent(this, managerdb.class);
        appIntent.setAction("MEDIA_BUTTON");
        appIntent.putExtra("button_action", "NEXT");
        appIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(appIntent);
    }

    private void handlePrevious() {
        Log.d(TAG, "معالجة السابق");

        Intent appIntent = new Intent(this, managerdb.class);
        appIntent.setAction("MEDIA_BUTTON");
        appIntent.putExtra("button_action", "PREVIOUS");
        appIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(appIntent);
    }

    /**
     * تحديث معلومات الإشعار
     */
    public static void updateInfo(String title, String reciter, boolean playing) {
        currentTitle = title != null ? title : "القرآن الكريم";
        currentReciter = reciter != null ? reciter : "";
        isPlaying = playing;
    }
}

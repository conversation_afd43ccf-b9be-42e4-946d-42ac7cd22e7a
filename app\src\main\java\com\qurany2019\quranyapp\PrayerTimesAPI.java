package com.qurany2019.quranyapp;

import android.os.AsyncTask;
import android.util.Log;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

/**
 * خدمة حقيقية للحصول على أوقات الصلاة من API موثوق
 * يستخدم API Aladhan الموثوق عالمياً
 */
public class PrayerTimesAPI {

    private static final String TAG = "PrayerTimesAPI";
    private static final String API_BASE_URL = "http://api.aladhan.com/v1/timings";

    public interface PrayerTimesCallback {
        void onSuccess(PrayerTimesResponse response);
        void onError(String error);
    }

    public static class PrayerTimesResponse {
        public String fajr;
        public String sunrise;
        public String dhuhr;
        public String asr;
        public String maghrib;
        public String isha;
        public String cityName;
        public String countryName;
        public String hijriDate;
        public String nextPrayer;

        public PrayerTimesResponse(String fajr, String sunrise, String dhuhr, String asr,
                                 String maghrib, String isha, String cityName, String countryName) {
            this.fajr = formatTime(fajr);
            this.sunrise = formatTime(sunrise);
            this.dhuhr = formatTime(dhuhr);
            this.asr = formatTime(asr);
            this.maghrib = formatTime(maghrib);
            this.isha = formatTime(isha);
            this.cityName = cityName;
            this.countryName = countryName;
            this.nextPrayer = calculateNextPrayer();
        }

        private String formatTime(String time) {
            if (time == null || time.isEmpty()) return "غير متاح";

            try {
                // إزالة المنطقة الزمنية إذا كانت موجودة
                String cleanTime = time.split(" ")[0];

                // تحويل من 24 ساعة إلى 12 ساعة مع AM/PM
                SimpleDateFormat input = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
                SimpleDateFormat output = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

                // إرجاع الوقت بـ AM/PM الإنجليزية - سيتم تطبيق الترجمة في PrayerTimesActivity
                return output.format(input.parse(cleanTime));
            } catch (Exception e) {
                Log.e(TAG, "Error formatting time: " + time, e);
                return time;
            }
        }

        private String calculateNextPrayer() {
            Calendar now = Calendar.getInstance();
            String currentTime = String.format(Locale.ENGLISH, "%02d:%02d",
                now.get(Calendar.HOUR_OF_DAY), now.get(Calendar.MINUTE));

            // سيتم استخدام نظام الترجمة في الكود المستدعي
            String[] prayerNames = {"Fajr", "Sunrise", "Dhuhr", "Asr", "Maghrib", "Isha"};
            String[] prayerTimes = {
                extractTime(fajr), extractTime(sunrise), extractTime(dhuhr),
                extractTime(asr), extractTime(maghrib), extractTime(isha)
            };

            for (int i = 0; i < prayerTimes.length; i++) {
                if (currentTime.compareTo(prayerTimes[i]) < 0) {
                    return "الصلاة القادمة: " + prayerNames[i] + " - " +
                           (i == 0 ? fajr : i == 1 ? sunrise : i == 2 ? dhuhr :
                            i == 3 ? asr : i == 4 ? maghrib : isha);
                }
            }

            // إذا انتهت جميع الصلوات، فالقادمة هي فجر اليوم التالي
            return "Next Prayer: Fajr (tomorrow) - " + fajr;
        }

        private String extractTime(String formattedTime) {
            try {
                // استخراج الوقت من النص المنسق
                String time = formattedTime.replace("ص", "AM").replace("م", "PM");
                SimpleDateFormat input = new SimpleDateFormat("h:mm a", Locale.ENGLISH);
                SimpleDateFormat output = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
                return output.format(input.parse(time));
            } catch (Exception e) {
                return "00:00";
            }
        }
    }

    /**
     * الحصول على أوقات الصلاة حسب الإحداثيات
     */
    public static void getPrayerTimes(double latitude, double longitude, String cityName, PrayerTimesCallback callback) {
        new GetPrayerTimesTask(callback, cityName).execute(latitude, longitude);
    }

    private static class GetPrayerTimesTask extends AsyncTask<Double, Void, PrayerTimesResponse> {
        private final PrayerTimesCallback callback;
        private final String cityName;
        private String errorMessage;

        public GetPrayerTimesTask(PrayerTimesCallback callback, String cityName) {
            this.callback = callback;
            this.cityName = cityName;
        }

        @Override
        protected PrayerTimesResponse doInBackground(Double... params) {
            try {
                double latitude = params[0];
                double longitude = params[1];

                // الحصول على التاريخ الحالي
                Calendar calendar = Calendar.getInstance();
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy", Locale.getDefault());
                String date = dateFormat.format(calendar.getTime());

                // بناء URL مع معاملات دقيقة
                // method=4 = Umm Al-Qura University, Makkah (الأكثر دقة للمنطقة العربية)
                String urlString = String.format(Locale.US,
                    "%s/%s?latitude=%.6f&longitude=%.6f&method=4&school=0",
                    API_BASE_URL, date, latitude, longitude);

                Log.d(TAG, "Requesting prayer times from: " + urlString);

                // إنشاء الاتصال
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("User-Agent", "QuranApp/1.0");

                // قراءة الاستجابة
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    Log.d(TAG, "API Response: " + response.toString());

                    // تحليل JSON
                    return parseResponse(response.toString());
                } else {
                    errorMessage = "HTTP Error: " + responseCode;
                    return null;
                }

            } catch (Exception e) {
                Log.e(TAG, "Error getting prayer times", e);
                errorMessage = "خطأ في الاتصال: " + e.getMessage();
                return null;
            }
        }

        private PrayerTimesResponse parseResponse(String jsonResponse) {
            try {
                JSONObject root = new JSONObject(jsonResponse);
                JSONObject data = root.getJSONObject("data");
                JSONObject timings = data.getJSONObject("timings");

                String fajr = timings.getString("Fajr");
                String sunrise = timings.getString("Sunrise");
                String dhuhr = timings.getString("Dhuhr");
                String asr = timings.getString("Asr");
                String maghrib = timings.getString("Maghrib");
                String isha = timings.getString("Isha");

                return new PrayerTimesResponse(fajr, sunrise, dhuhr, asr, maghrib, isha,
                                             cityName, "");

            } catch (JSONException e) {
                Log.e(TAG, "JSON parsing error", e);
                errorMessage = "خطأ في تحليل البيانات";
                return null;
            }
        }

        @Override
        protected void onPostExecute(PrayerTimesResponse result) {
            if (result != null) {
                Log.d(TAG, "Prayer times parsed successfully for " + cityName);
                callback.onSuccess(result);
            } else {
                callback.onError(errorMessage != null ? errorMessage : "خطأ غير معروف");
            }
        }
    }
}

package com.qurany2019.quranyapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.LinearGradient;
import android.graphics.Shader;
import android.graphics.Typeface;
import androidx.core.app.NotificationCompat;
import androidx.media.app.NotificationCompat.MediaStyle;

/**
 * خدمة إشعارات الوسائط مع أزرار التحكم
 */
public class MediaNotificationService extends Service {

    private static final String CHANNEL_ID = "quran_media_channel";
    private static final int NOTIFICATION_ID = 1001;

    // أكشن الأزرار - محدثة لتتطابق مع managerdb.java
    public static final String ACTION_PLAY = "com.qurany2019.quranyapp.PLAY";
    public static final String ACTION_PAUSE = "com.qurany2019.quranyapp.PAUSE";
    public static final String ACTION_NEXT = "com.qurany2019.quranyapp.NEXT";
    public static final String ACTION_PREVIOUS = "com.qurany2019.quranyapp.PREVIOUS";
    public static final String ACTION_STOP = "com.qurany2019.quranyapp.STOP";

    public static String currentSurah = "القرآن الكريم";
    public static String currentReciter = "";
    public static boolean isPlaying = false;

    private BroadcastReceiver mediaActionReceiver;

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        // الأزرار ترسل مباشرة للتطبيق الرئيسي عبر BroadcastReceiver
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            android.util.Log.d("MEDIA_SERVICE", "تم استقبال أكشن: " + action);

            if ("SHOW_NOTIFICATION".equals(action)) {
                showMediaNotification();
            } else if ("STOP_NOTIFICATION".equals(action)) {
                stopForeground(true);
                stopSelf();
            } else if (ACTION_PLAY.equals(action) || ACTION_PAUSE.equals(action) ||
                      ACTION_NEXT.equals(action) || ACTION_PREVIOUS.equals(action) ||
                      ACTION_STOP.equals(action)) {
                // معالجة أوامر الوسائط مباشرة في الخدمة
                handleMediaAction(action);
            } else {
                // إذا لم يكن هناك أكشن محدد، اعرض الإشعار
                showMediaNotification();
            }
        } else {
            showMediaNotification();
        }
        return START_STICKY;
    }

    /**
     * معالجة أوامر الوسائط مباشرة في الخدمة
     */
    private void handleMediaAction(String action) {
        try {
            android.util.Log.d("MEDIA_SERVICE", "معالجة أكشن الوسائط: " + action);

            // إرسال broadcast للتطبيق الرئيسي
            Intent broadcastIntent = new Intent("com.qurany2019.quranyapp.MEDIA_CONTROL");
            broadcastIntent.putExtra("media_action", action);
            sendBroadcast(broadcastIntent);

            // تحديث حالة الإشعار
            if (ACTION_PLAY.equals(action)) {
                isPlaying = true;
            } else if (ACTION_PAUSE.equals(action)) {
                isPlaying = false;
            } else if (ACTION_STOP.equals(action)) {
                isPlaying = false;
                stopForeground(true);
                stopSelf();
                return;
            }

            // تحديث الإشعار
            showMediaNotification();

        } catch (Exception e) {
            android.util.Log.e("MEDIA_SERVICE", "خطأ في معالجة أكشن الوسائط: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        if (mediaActionReceiver != null) {
            unregisterReceiver(mediaActionReceiver);
        }
        super.onDestroy();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "تشغيل القرآن الكريم",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("إشعارات تشغيل القرآن الكريم");
            channel.setShowBadge(false);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    // تم حذف setupMediaActionReceiver لأن الأزرار ترسل مباشرة للتطبيق الرئيسي

    private void showMediaNotification() {
        // إنشاء PendingIntent لفتح التطبيق
        Intent contentIntent = new Intent(this, managerdb.class);
        contentIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);

        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }

        PendingIntent contentPendingIntent = PendingIntent.getActivity(
            this, 0, contentIntent, flags
        );

        // إنشاء أزرار التحكم
        PendingIntent playPausePendingIntent = createActionPendingIntent(
            isPlaying ? ACTION_PAUSE : ACTION_PLAY, 1
        );
        PendingIntent nextPendingIntent = createActionPendingIntent(ACTION_NEXT, 2);
        PendingIntent previousPendingIntent = createActionPendingIntent(ACTION_PREVIOUS, 3);
        PendingIntent stopPendingIntent = createActionPendingIntent(ACTION_STOP, 4);

        String title = currentSurah;
        String text = currentReciter.isEmpty() ? "القرآن الكريم" : currentReciter;

        // إنشاء bitmap للأيقونة الكبيرة
        android.graphics.Bitmap largeIcon = createLargeIcon();

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_media_play) // أيقونة تشغيل جميلة
            .setLargeIcon(largeIcon) // أيقونة كبيرة جميلة
            .setContentTitle("🎵 " + title) // إضافة إيموجي جميل
            .setContentText("📖 " + text) // إضافة إيموجي القرآن
            .setSubText("🕌 القرآن الكريم") // نص فرعي جميل مع إيموجي مسجد
            .setContentIntent(contentPendingIntent)
            .setOngoing(isPlaying)
            .setAutoCancel(false)
            .setShowWhen(false)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_HIGH) // أولوية عالية للظهور
            .setColor(0xFF2E7D32) // لون أخضر إسلامي جميل
            .setColorized(true) // تفعيل الألوان
            .setCategory(NotificationCompat.CATEGORY_TRANSPORT) // فئة الوسائط

            // إضافة أزرار التحكم الجميلة
            .addAction(android.R.drawable.ic_media_previous, "⏮️ السابق", previousPendingIntent)
            .addAction(
                isPlaying ? android.R.drawable.ic_media_pause : android.R.drawable.ic_media_play,
                isPlaying ? "⏸️ إيقاف" : "▶️ تشغيل",
                playPausePendingIntent
            )
            .addAction(android.R.drawable.ic_media_next, "⏭️ التالي", nextPendingIntent)

            // تنسيق إشعار الوسائط
            .setStyle(new androidx.media.app.NotificationCompat.MediaStyle()
                .setShowActionsInCompactView(0, 1, 2)
            );

        Notification notification = builder.build();
        startForeground(NOTIFICATION_ID, notification);
    }

    private PendingIntent createActionPendingIntent(String action, int requestCode) {
        // إنشاء Intent مباشر للخدمة نفسها - أبسط وأكثر فعالية
        Intent intent = new Intent(this, MediaNotificationService.class);
        intent.setAction(action);
        intent.putExtra("media_action", action);

        int flags = PendingIntent.FLAG_UPDATE_CURRENT;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            flags |= PendingIntent.FLAG_IMMUTABLE;
        }

        android.util.Log.d("MEDIA_NOTIFICATION", "إنشاء PendingIntent للخدمة: " + action);
        return PendingIntent.getService(this, requestCode, intent, flags);
    }

    /**
     * إنشاء أيقونة كبيرة جميلة للإشعار
     */
    private android.graphics.Bitmap createLargeIcon() {
        try {
            // إنشاء bitmap بحجم 256x256
            android.graphics.Bitmap bitmap = android.graphics.Bitmap.createBitmap(256, 256, android.graphics.Bitmap.Config.ARGB_8888);
            android.graphics.Canvas canvas = new android.graphics.Canvas(bitmap);

            // رسم خلفية متدرجة جميلة
            android.graphics.Paint backgroundPaint = new android.graphics.Paint();
            android.graphics.LinearGradient gradient = new android.graphics.LinearGradient(
                0, 0, 256, 256,
                new int[]{0xFF2E7D32, 0xFF4CAF50, 0xFF66BB6A}, // تدرج أخضر إسلامي
                null,
                android.graphics.Shader.TileMode.CLAMP
            );
            backgroundPaint.setShader(gradient);
            canvas.drawRect(0, 0, 256, 256, backgroundPaint);

            // رسم دائرة بيضاء في المنتصف
            android.graphics.Paint circlePaint = new android.graphics.Paint();
            circlePaint.setColor(0xFFFFFFFF);
            circlePaint.setAntiAlias(true);
            canvas.drawCircle(128, 128, 80, circlePaint);

            // رسم نص "قرآن" في المنتصف
            android.graphics.Paint textPaint = new android.graphics.Paint();
            textPaint.setColor(0xFF2E7D32);
            textPaint.setTextSize(48);
            textPaint.setAntiAlias(true);
            textPaint.setTextAlign(android.graphics.Paint.Align.CENTER);
            textPaint.setTypeface(android.graphics.Typeface.DEFAULT_BOLD);
            canvas.drawText("قرآن", 128, 140, textPaint);

            return bitmap;
        } catch (Exception e) {
            e.printStackTrace();
            // في حالة الخطأ، إرجاع null لاستخدام الأيقونة الافتراضية
            return null;
        }
    }

    public static void updateInfo(String surah, String reciter, boolean playing) {
        currentSurah = surah != null ? surah : "القرآن الكريم";
        currentReciter = reciter != null ? reciter : "";
        isPlaying = playing;
    }
}

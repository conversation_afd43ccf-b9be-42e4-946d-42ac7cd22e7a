<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#2E7D32"
                android:endColor="#1B5E20" />
            <corners android:radius="16dp" />
            <stroke
                android:width="2dp"
                android:color="@color/islamicGold" />
            <padding
                android:left="16dp"
                android:top="12dp"
                android:right="16dp"
                android:bottom="12dp" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#4CAF50"
                android:endColor="#2E7D32" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="@color/islamicGoldLight" />
            <padding
                android:left="16dp"
                android:top="12dp"
                android:right="16dp"
                android:bottom="12dp" />
        </shape>
    </item>
</selector>

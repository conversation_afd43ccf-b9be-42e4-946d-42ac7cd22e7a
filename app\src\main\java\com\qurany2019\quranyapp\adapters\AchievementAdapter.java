package com.qurany2019.quranyapp.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.qurany2019.quranyapp.R;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class AchievementAdapter extends RecyclerView.Adapter<AchievementAdapter.AchievementViewHolder> {
    
    private Context context;
    private List<AchievementEntity> achievementList;
    private SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy", Locale.getDefault());

    public AchievementAdapter(Context context, List<AchievementEntity> achievementList) {
        this.context = context;
        this.achievementList = achievementList;
    }

    @NonNull
    @Override
    public AchievementViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_achievement, parent, false);
        return new AchievementViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AchievementViewHolder holder, int position) {
        AchievementEntity achievement = achievementList.get(position);
        
        // تعيين النصوص باستخدام ملفات الترجمة
        String title = getLocalizedText(achievement.getTitle());
        String description = getLocalizedText(achievement.getDescription());

        holder.achievementTitle.setText(title);
        holder.achievementDescription.setText(description);
        holder.progressText.setText(achievement.getProgressText());

        // نقاط الإنجاز مع الترجمة
        String pointsText = achievement.getRewardPoints() + " " + context.getString(R.string.points_text);
        holder.rewardPoints.setText(pointsText);
        
        // تحديث شريط التقدم
        holder.achievementProgressBar.setMax(achievement.getTargetValue());
        holder.achievementProgressBar.setProgress(achievement.getCurrentValue());
        
        // تحديث مظهر البطاقة حسب الحالة
        updateAchievementAppearance(holder, achievement);
        
        // إعداد النقر
        holder.cardView.setOnClickListener(v -> {
            // يمكن إضافة تفاصيل الإنجاز هنا
        });
    }

    private void updateAchievementAppearance(AchievementViewHolder holder, AchievementEntity achievement) {
        if (achievement.isUnlocked()) {
            // إنجاز مفتوح
            holder.cardView.setCardBackgroundColor(context.getResources().getColor(R.color.achievement_unlocked));
            holder.achievementIcon.setImageResource(R.drawable.ic_achievement);
            holder.achievementIcon.setBackgroundTintList(context.getResources().getColorStateList(R.color.islamic_gold));
            holder.lockIcon.setVisibility(View.GONE);
            holder.statusIcon.setVisibility(View.VISIBLE);
            holder.statusIcon.setImageResource(R.drawable.ic_check_circle);
            holder.statusIcon.setImageTintList(context.getResources().getColorStateList(R.color.islamic_green));
            
            // عرض تاريخ الإنجاز
            if (achievement.getUnlockDate() > 0) {
                String dateStr = dateFormat.format(new Date(achievement.getUnlockDate()));
                holder.unlockDate.setText(dateStr);
                holder.unlockDate.setVisibility(View.VISIBLE);
            }
            
            // تغيير ألوان النصوص
            holder.achievementTitle.setTextColor(context.getResources().getColor(R.color.islamic_green_dark));
            holder.progressText.setTextColor(context.getResources().getColor(R.color.islamic_green));
            
        } else if (achievement.isCompleted()) {
            // إنجاز مكتمل لكن غير مفتوح
            holder.cardView.setCardBackgroundColor(context.getResources().getColor(R.color.achievement_ready));
            holder.achievementIcon.setImageResource(R.drawable.ic_achievement);
            holder.achievementIcon.setBackgroundTintList(context.getResources().getColorStateList(R.color.islamic_blue));
            holder.lockIcon.setVisibility(View.GONE);
            holder.statusIcon.setVisibility(View.VISIBLE);
            holder.statusIcon.setImageResource(R.drawable.ic_star);
            holder.statusIcon.setImageTintList(context.getResources().getColorStateList(R.color.islamic_gold));
            holder.unlockDate.setVisibility(View.GONE);
            
            // تغيير ألوان النصوص
            holder.achievementTitle.setTextColor(context.getResources().getColor(R.color.islamic_blue));
            holder.progressText.setTextColor(context.getResources().getColor(R.color.islamic_blue));
            
        } else {
            // إنجاز مقفل
            holder.cardView.setCardBackgroundColor(context.getResources().getColor(R.color.achievement_locked));
            holder.achievementIcon.setImageResource(R.drawable.ic_achievement);
            holder.achievementIcon.setBackgroundTintList(context.getResources().getColorStateList(R.color.islamic_gray));
            holder.lockIcon.setVisibility(View.VISIBLE);
            holder.statusIcon.setVisibility(View.GONE);
            holder.unlockDate.setVisibility(View.GONE);
            
            // تغيير ألوان النصوص
            holder.achievementTitle.setTextColor(context.getResources().getColor(R.color.islamic_gray));
            holder.progressText.setTextColor(context.getResources().getColor(R.color.islamic_gray));
        }
        
        // تحديث شريط التقدم حسب النوع
        updateProgressBarColor(holder, achievement);
    }

    private void updateProgressBarColor(AchievementViewHolder holder, AchievementEntity achievement) {
        int progressColor;
        
        if (achievement.isUnlocked()) {
            progressColor = R.color.islamic_green;
        } else if (achievement.isCompleted()) {
            progressColor = R.color.islamic_gold;
        } else {
            progressColor = R.color.islamic_blue;
        }
        
        holder.achievementProgressBar.setProgressTintList(
            context.getResources().getColorStateList(progressColor)
        );
    }

    @Override
    public int getItemCount() {
        return achievementList.size();
    }

    // دالة لتحويل معرف النص إلى النص المترجم
    private String getLocalizedText(String textOrStringId) {
        if (textOrStringId == null) return "";

        // إذا كان النص يبدأ بـ "achievement_" فهو معرف نص
        if (textOrStringId.startsWith("achievement_")) {
            try {
                // الحصول على معرف المورد
                int stringId = context.getResources().getIdentifier(textOrStringId, "string", context.getPackageName());
                if (stringId != 0) {
                    return context.getString(stringId);
                }
            } catch (Exception e) {
                // في حالة الخطأ، إرجاع النص كما هو
            }
        }

        // إرجاع النص كما هو إذا لم يكن معرف نص
        return textOrStringId;
    }

    static class AchievementViewHolder extends RecyclerView.ViewHolder {
        CardView cardView;
        ImageView achievementIcon;
        ImageView lockIcon;
        ImageView statusIcon;
        TextView achievementTitle;
        TextView achievementDescription;
        TextView progressText;
        TextView rewardPoints;
        TextView unlockDate;
        ProgressBar achievementProgressBar;

        public AchievementViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.achievementCardView);
            achievementIcon = itemView.findViewById(R.id.achievementIcon);
            lockIcon = itemView.findViewById(R.id.lockIcon);
            statusIcon = itemView.findViewById(R.id.statusIcon);
            achievementTitle = itemView.findViewById(R.id.achievementTitle);
            achievementDescription = itemView.findViewById(R.id.achievementDescription);
            progressText = itemView.findViewById(R.id.progressText);
            rewardPoints = itemView.findViewById(R.id.rewardPoints);
            unlockDate = itemView.findViewById(R.id.unlockDate);
            achievementProgressBar = itemView.findViewById(R.id.achievementProgressBar);
        }
    }
}

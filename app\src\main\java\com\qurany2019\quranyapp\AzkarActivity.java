package com.qurany2019.quranyapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.ContextCompat;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;

public class AzkarActivity extends AppCompatActivity {
    private AdView mAdView;
    private boolean isNightMode = false;
    private ImageView nightDayToggle;
    private ImageView notificationIcon;
    private TextView azkarTitle;
    private ScrollView mainScrollView;
    private View notificationStatusIndicator;
    private AzkarNotificationHelper notificationHelper;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق إعدادات اللغة المحفوظة
        applyLanguageSettings();

        setContentView(R.layout.activity_azkar);

        // إعداد شريط الأدوات
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(getString(R.string.azkar_title));

        // إعداد الإعلانات
        setupAds();

        // إعداد أقسام الأذكار
        setupAzkarSections();

        // إعداد الأيقونات
        setupIcons();

        // إعداد أزرار الإحصائيات والإنجازات
        setupStatisticsAndAchievements();
    }

    private void setupAds() {
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);
    }

    private void setupAzkarSections() {
        // بطاقة أذكار الصباح
        androidx.cardview.widget.CardView morningCard = findViewById(R.id.morningAzkarCard);
        morningCard.setOnClickListener(v -> {
            animateCardClick(morningCard);
            Intent intent = new Intent(this, AzkarDetailActivity.class);
            intent.putExtra("azkar_type", "morning");
            intent.putExtra("azkar_title", getString(R.string.azkar_morning));
            startActivity(intent);
        });

        // بطاقة أذكار المساء
        androidx.cardview.widget.CardView eveningCard = findViewById(R.id.eveningAzkarCard);
        eveningCard.setOnClickListener(v -> {
            animateCardClick(eveningCard);
            Intent intent = new Intent(this, AzkarDetailActivity.class);
            intent.putExtra("azkar_type", "evening");
            intent.putExtra("azkar_title", getString(R.string.azkar_evening));
            startActivity(intent);
        });

        // بطاقة أذكار الاستيقاظ
        androidx.cardview.widget.CardView wakeCard = findViewById(R.id.wakeAzkarCard);
        wakeCard.setOnClickListener(v -> {
            animateCardClick(wakeCard);
            Intent intent = new Intent(this, AzkarDetailActivity.class);
            intent.putExtra("azkar_type", "wake");
            intent.putExtra("azkar_title", getString(R.string.azkar_wake));
            startActivity(intent);
        });

        // بطاقة أذكار النوم
        androidx.cardview.widget.CardView sleepCard = findViewById(R.id.sleepAzkarCard);
        sleepCard.setOnClickListener(v -> {
            animateCardClick(sleepCard);
            Intent intent = new Intent(this, AzkarDetailActivity.class);
            intent.putExtra("azkar_type", "sleep");
            intent.putExtra("azkar_title", getString(R.string.azkar_sleep));
            startActivity(intent);
        });

        // بطاقة أذكار بعد الصلاة
        androidx.cardview.widget.CardView prayerCard = findViewById(R.id.prayerAzkarCard);
        prayerCard.setOnClickListener(v -> {
            animateCardClick(prayerCard);
            Intent intent = new Intent(this, AzkarDetailActivity.class);
            intent.putExtra("azkar_type", "prayer");
            intent.putExtra("azkar_title", getString(R.string.azkar_prayer));
            startActivity(intent);
        });
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    // دالة تطبيق إعدادات اللغة
    private void applyLanguageSettings() {
        android.content.SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = langPrefs.getBoolean("is_arabic", true);
        setLocale(isArabic ? "ar" : "en");
    }

    private void setLocale(String languageCode) {
        java.util.Locale locale = new java.util.Locale(languageCode);
        java.util.Locale.setDefault(locale);
        android.content.res.Configuration config = new android.content.res.Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    // إعداد الأيقونات
    private void setupIcons() {
        // الحصول على مراجع الأيقونات
        nightDayToggle = findViewById(R.id.nightDayToggle);
        notificationIcon = findViewById(R.id.notificationIcon);
        azkarTitle = findViewById(R.id.azkarTitle);
        mainScrollView = findViewById(R.id.mainScrollView);
        notificationStatusIndicator = findViewById(R.id.notificationStatusIndicator);

        // إنشاء مساعد الإشعارات
        notificationHelper = new AzkarNotificationHelper(this);

        // تحديد الوضع الحالي
        SharedPreferences prefs = getSharedPreferences("azkar_settings", MODE_PRIVATE);
        isNightMode = prefs.getBoolean("night_mode", false);

        // تطبيق الوضع الحالي
        applyNightMode();

        // تحديث حالة الإشعارات
        updateNotificationStatus();

        // إعداد أيقونة الوضع الليلي/النهاري
        nightDayToggle.setOnClickListener(v -> {
            animateIcon(nightDayToggle);
            toggleNightMode();
        });

        // إعداد أيقونة الإشعارات
        notificationIcon.setOnClickListener(v -> {
            animateIcon(notificationIcon);
            toggleNotificationStatus();
        });
    }

    // تبديل الوضع الليلي/النهاري
    private void toggleNightMode() {
        isNightMode = !isNightMode;

        // حفظ الوضع الجديد
        SharedPreferences prefs = getSharedPreferences("azkar_settings", MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("night_mode", isNightMode);
        editor.apply();

        // تطبيق الوضع الجديد
        applyNightMode();
    }

    // تطبيق الوضع الليلي/النهاري
    private void applyNightMode() {
        if (isNightMode) {
            // الوضع الليلي
            nightDayToggle.setImageResource(R.drawable.ic_sun);
            nightDayToggle.setColorFilter(ContextCompat.getColor(this, R.color.azkar_icon_tint_dark));
            azkarTitle.setTextColor(ContextCompat.getColor(this, R.color.azkar_header_text_dark));
            mainScrollView.setBackgroundColor(ContextCompat.getColor(this, R.color.azkar_page_bg_dark));
        } else {
            // الوضع النهاري
            nightDayToggle.setImageResource(R.drawable.ic_moon);
            nightDayToggle.setColorFilter(ContextCompat.getColor(this, R.color.azkar_icon_tint_light));
            azkarTitle.setTextColor(ContextCompat.getColor(this, R.color.azkar_header_text_light));
            mainScrollView.setBackgroundColor(ContextCompat.getColor(this, R.color.azkar_page_bg_light));
        }
    }

    // تحريك الأيقونة عند الضغط
    private void animateIcon(ImageView icon) {
        icon.animate()
            .scaleX(1.2f)
            .scaleY(1.2f)
            .setDuration(150)
            .withEndAction(() -> icon.animate()
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(150)
                .start())
            .start();
    }

    // تحريك البطاقة عند الضغط
    private void animateCardClick(androidx.cardview.widget.CardView card) {
        card.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .withEndAction(() -> card.animate()
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(100)
                .start())
            .start();
    }

    // تبديل حالة الإشعارات
    private void toggleNotificationStatus() {
        boolean newState = notificationHelper.toggleNotifications();
        updateNotificationStatus();

        String message = notificationHelper.getNotificationStatusMessage();
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();

        // إذا تم تفعيل الإشعارات، قم بتفعيل التذكير الذكي
        if (newState) {
            notificationHelper.activateSmartReminder();
        }
    }

    // تحديث المظهر البصري لحالة الإشعارات
    private void updateNotificationStatus() {
        boolean enabled = notificationHelper.areNotificationsEnabled();

        if (enabled) {
            // الإشعارات مفعلة - لون أحمر مع مؤشر أخضر
            notificationIcon.setColorFilter(ContextCompat.getColor(this, R.color.azkar_notification_icon_active));
            notificationStatusIndicator.setBackgroundResource(R.drawable.notification_status_indicator);
            notificationStatusIndicator.setVisibility(View.VISIBLE);
        } else {
            // الإشعارات معطلة - لون رمادي مع مؤشر أحمر
            notificationIcon.setColorFilter(ContextCompat.getColor(this, R.color.azkar_notification_icon_inactive));
            notificationStatusIndicator.setBackgroundResource(R.drawable.notification_status_inactive);
            notificationStatusIndicator.setVisibility(View.VISIBLE);
        }
    }

    // إعداد أزرار الإحصائيات والإنجازات
    private void setupStatisticsAndAchievements() {
        // زر الإحصائيات
        ImageView statisticsButton = findViewById(R.id.statisticsButton);
        if (statisticsButton != null) {
            statisticsButton.setOnClickListener(v -> {
                animateIcon(statisticsButton);
                Intent intent = new Intent(this, AzkarStatisticsActivity.class);
                startActivity(intent);
            });
        }

        // زر الإنجازات
        ImageView achievementsButton = findViewById(R.id.achievementsButton);
        if (achievementsButton != null) {
            achievementsButton.setOnClickListener(v -> {
                animateIcon(achievementsButton);
                Intent intent = new Intent(this, AchievementsActivity.class);
                startActivity(intent);
            });
        }
    }

    // الحصول على رسالة التأكيد
    private String getConfirmationMessage(String azkarType) {
        switch (azkarType) {
            case AzkarNotificationHelper.AZKAR_MORNING:
                return "✅ تم تفعيل تذكير أذكار الصباح";
            case AzkarNotificationHelper.AZKAR_EVENING:
                return "✅ تم تفعيل تذكير أذكار المساء";
            case AzkarNotificationHelper.AZKAR_SLEEP:
                return "✅ تم تفعيل تذكير أذكار النوم";
            case AzkarNotificationHelper.AZKAR_WAKE:
                return "✅ تم تفعيل تذكير أذكار الاستيقاظ";
            default:
                return "✅ تم تفعيل التذكير الذكي للأذكار";
        }
    }
}

package com.qurany2019.quranyapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * BroadcastReceiver للتحكم المباشر في MediaPlayer عبر static methods
 */
public class StaticMediaReceiver extends BroadcastReceiver {

    private static final String TAG = "StaticMediaReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "🎯🎯🎯 StaticMediaReceiver.onReceive() تم استدعاؤه!");

        if (intent != null) {
            Log.d(TAG, "Intent Action: " + intent.getAction());
            Log.d(TAG, "Intent Extras: " + intent.getExtras());

            if ("STATIC_MEDIA_CONTROL".equals(intent.getAction())) {
                String action = intent.getStringExtra("action");
                Log.d(TAG, "الأكشن المستخرج: " + action);

                if ("PLAY_PAUSE".equals(action)) {
                    Log.d(TAG, "🔥 معالجة PLAY_PAUSE - استدعاء static method");

                    try {
                        // استدعاء static method مباشر
                        managerdb.staticPlayPause();
                        Log.d(TAG, "✅ تم استدعاء staticPlayPause بنجاح");
                    } catch (Exception e) {
                        Log.e(TAG, "❌ خطأ في استدعاء staticPlayPause: " + e.getMessage());
                        e.printStackTrace();
                    }

                } else if ("NEXT".equals(action)) {
                    Log.d(TAG, "🔥 معالجة NEXT");
                    // يمكن إضافة static methods أخرى لاحقاً

                } else if ("PREVIOUS".equals(action)) {
                    Log.d(TAG, "🔥 معالجة PREVIOUS");
                    // يمكن إضافة static methods أخرى لاحقاً
                }
            } else {
                Log.e(TAG, "❌ Action غير متطابق: " + intent.getAction());
            }
        } else {
            Log.e(TAG, "❌ Intent is null!");
        }
    }
}

package com.qurany2019.quranyapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.util.Log;

/**
 * مستقبل إعادة تشغيل الجهاز
 * يعيد جدولة إشعارات أوقات الصلاة بعد إعادة تشغيل الجهاز
 */
public class BootReceiver extends BroadcastReceiver {
    
    private static final String TAG = "BootReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        
        if (Intent.ACTION_BOOT_COMPLETED.equals(action) ||
            Intent.ACTION_MY_PACKAGE_REPLACED.equals(action) ||
            Intent.ACTION_PACKAGE_REPLACED.equals(action)) {
            
            Log.d(TAG, "Device boot completed or app updated, rescheduling prayer notifications");
            
            // التحقق من تفعيل الإشعارات
            SharedPreferences prefs = context.getSharedPreferences("prayer_settings", Context.MODE_PRIVATE);
            boolean notificationsEnabled = prefs.getBoolean("notifications_enabled", true);
            
            if (!notificationsEnabled) {
                Log.d(TAG, "Notifications are disabled, skipping rescheduling");
                return;
            }
            
            // محاولة إعادة جدولة الإشعارات باستخدام آخر أوقات صلاة محفوظة
            rescheduleNotifications(context);
        }
    }
    
    /**
     * إعادة جدولة إشعارات أوقات الصلاة
     */
    private void rescheduleNotifications(Context context) {
        try {
            SharedPreferences prefs = context.getSharedPreferences("prayer_times", Context.MODE_PRIVATE);
            
            // استرجاع آخر أوقات صلاة محفوظة
            String[] prayerTimes = new String[6];
            prayerTimes[0] = prefs.getString("fajr", "");
            prayerTimes[1] = prefs.getString("sunrise", "");
            prayerTimes[2] = prefs.getString("dhuhr", "");
            prayerTimes[3] = prefs.getString("asr", "");
            prayerTimes[4] = prefs.getString("maghrib", "");
            prayerTimes[5] = prefs.getString("isha", "");
            
            String cityName = prefs.getString("city_name", "موقعك");
            
            // التحقق من وجود أوقات صلاة محفوظة
            boolean hasValidTimes = false;
            for (String time : prayerTimes) {
                if (time != null && !time.isEmpty()) {
                    hasValidTimes = true;
                    break;
                }
            }
            
            if (hasValidTimes) {
                // إعادة جدولة الإشعارات
                PrayerNotificationService notificationService = new PrayerNotificationService(context);
                notificationService.schedulePrayerNotifications(prayerTimes, cityName);
                
                Log.d(TAG, "Prayer notifications rescheduled successfully for " + cityName);
            } else {
                Log.w(TAG, "No valid prayer times found, cannot reschedule notifications");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error rescheduling prayer notifications", e);
        }
    }
}

plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services'
}

android {
    namespace 'com.qurany2019.quranyapp'
    compileSdk 34

    defaultConfig {
        applicationId "com.qurany2019.quranyapp"
        minSdk 21
        targetSdk 34
        versionCode 6
        versionName "5.2"

        manifestPlaceholders = [
                onesignal_app_id               : "************************************",
                onesignal_google_project_number: "REMOTE"
        ]
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    buildFeatures {
        viewBinding true
    }
}

dependencies {
    // واجهات
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    // مكتبات إشعارات الوسائط
    implementation 'androidx.media:media:1.6.0'

    // Firebase
    implementation platform('com.google.firebase:firebase-bom:32.0.0')
    implementation 'com.google.firebase:firebase-analytics'

    // OneSignal - الإشعارات ✅
    implementation 'com.onesignal:OneSignal:4.8.7'

    // AdMob
    implementation 'com.google.android.gms:play-services-ads:23.0.0'

    // Google Location Services للقبلة وأوقات الصلاة
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    // implementation 'com.google.android.gms:play-services-maps:18.1.0' // REMOVED: Causes AppsFilter scanning

    // HTTP Client لطلبات أوقات الصلاة
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // Volley for Google APIs
    implementation 'com.android.volley:volley:1.2.1'

    // PDF Viewer من JitPack
    implementation 'com.github.mhiew:android-pdf-viewer:3.2.0-beta.3'

    // مكتبات الانيميشن والتأثيرات
    implementation 'com.airbnb.android:lottie:6.1.0'
    implementation 'com.github.bumptech.glide:glide:4.15.1'
    implementation 'androidx.dynamicanimation:dynamicanimation:1.0.0'

    // مكتبات التصميم العصري الإضافية
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.0'

    // Room Database للنظام الشامل
    implementation 'androidx.room:room-runtime:2.5.0'
    implementation 'androidx.room:room-ktx:2.5.0'
    annotationProcessor 'androidx.room:room-compiler:2.5.0'

    // ViewModel و LiveData للنظام المتقدم
    implementation 'androidx.lifecycle:lifecycle-viewmodel:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-livedata:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-common-java8:2.6.2'

    // WorkManager للتذكيرات المجدولة
    implementation 'androidx.work:work-runtime:2.8.1'

    // Guava لحل مشكلة ListenableFuture
    implementation 'com.google.guava:guava:31.1-android'

    // Charts للإحصائيات البصرية
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
}

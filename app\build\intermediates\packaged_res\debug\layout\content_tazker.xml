<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/islamic_gradient_bg"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context="com.qurany2019.quranyapp.Tazker"
    tools:showIn="@layout/activity_tazker">

<LinearLayout
    android:id="@+id/content_tazker"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layoutDirection="locale"
    android:padding="16dp">




    <!-- Current Dhikr Display -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="6dp"
        android:backgroundTint="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/current_dhikr_label"
                android:textSize="14sp"
                android:textColor="@color/islamic_green"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/arabic_font" />

            <TextView
                android:id="@+id/txtzekr"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/default_dhikr_text"
                android:textSize="28sp"
                android:textStyle="bold"
                android:textColor="@color/islamic_green_dark"
                android:gravity="center"
                android:fontFamily="@font/arabic_font"
                android:layout_marginBottom="16dp" />

            <!-- Navigation Buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <Button
                    android:id="@+id/btnPrev"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="@drawable/ripple_navigation"
                    android:text="‹"
                    android:textSize="24sp"
                    android:textColor="@color/white"
                    android:onClick="prev"
                    android:layout_marginEnd="20dp"
                    android:elevation="4dp"
                    android:stateListAnimator="@null" />

                <Button
                    android:id="@+id/btnNext"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="@drawable/ripple_navigation"
                    android:text="›"
                    android:textSize="24sp"
                    android:textColor="@color/white"
                    android:onClick="next"
                    android:elevation="4dp"
                    android:stateListAnimator="@null" />

            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Counter Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="8dp"
        android:backgroundTint="@color/islamic_gold_light">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="30dp"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/counter_label"
                android:textSize="16sp"
                android:textColor="@color/islamic_green_dark"
                android:layout_marginBottom="20dp"
                android:fontFamily="@font/arabic_font" />

            <!-- Main Counter Button -->
            <FrameLayout
                android:layout_width="220dp"
                android:layout_height="220dp"
                android:layout_marginBottom="20dp">

                <!-- Outer Ring -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/islamic_counter_ring" />

                <!-- Inner Counter Button -->
                <Button
                    android:id="@+id/counter"
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:layout_gravity="center"
                    android:background="@drawable/ripple_counter"
                    android:text="0"
                    android:textSize="48sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:onClick="count"
                    android:fontFamily="@font/arabic_font"
                    android:elevation="8dp"
                    android:stateListAnimator="@null"
                    android:shadowColor="@color/islamic_green_dark"
                    android:shadowDx="2"
                    android:shadowDy="2"
                    android:shadowRadius="4" />

            </FrameLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/tap_to_count"
                android:textSize="14sp"
                android:textColor="@color/islamic_green"
                android:fontFamily="@font/arabic_font" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Reset and Settings Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="20dp"
        android:layoutDirection="locale">

        <Button
            android:id="@+id/btnReset"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:layout_marginEnd="10dp"
            android:background="@drawable/ripple_effect"
            android:text="@string/reset_button"
            android:textColor="@color/islamic_green_dark"
            android:textSize="16sp"
            android:fontFamily="@font/arabic_font"
            android:onClick="resetCounter"
            android:elevation="2dp"
            android:stateListAnimator="@null" />

        <Button
            android:id="@+id/btnTarget"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            android:background="@drawable/ripple_effect"
            android:text="@string/set_target_button"
            android:textColor="@color/islamic_green_dark"
            android:textSize="16sp"
            android:fontFamily="@font/arabic_font"
            android:onClick="setTarget"
            android:elevation="2dp"
            android:stateListAnimator="@null" />

    </LinearLayout>

    <!-- Progress Section -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        android:backgroundTint="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/today_statistics"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/islamic_green_dark"
                android:layout_marginBottom="16dp"
                android:fontFamily="@font/arabic_font" />

            <!-- إجمالي التسبيحات -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/total_dhikr_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvTotalCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background"
                    android:padding="8dp"
                    android:minWidth="40dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- الهدف اليومي -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/daily_target_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvDailyTarget"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="100"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background"
                    android:padding="8dp"
                    android:minWidth="40dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- إحصائيات كل ذكر -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dhikr_details_label"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/islamic_green_dark"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/arabic_font" />

            <!-- استغفر الله -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/astaghfirullah_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvCount0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background_small"
                    android:padding="6dp"
                    android:minWidth="35dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- سبحان الله -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/subhan_allah_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvCount1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background_small"
                    android:padding="6dp"
                    android:minWidth="35dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- الحمد لله -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/alhamdulillah_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvCount2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background_small"
                    android:padding="6dp"
                    android:minWidth="35dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- الله اكبر -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/allahu_akbar_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvCount3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background_small"
                    android:padding="6dp"
                    android:minWidth="35dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- لا اله الا الله -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layoutDirection="locale">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/la_ilaha_illa_allah_label"
                    android:textSize="14sp"
                    android:textColor="@color/islamic_green"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/tvCount4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_green_dark"
                    android:fontFamily="@font/arabic_font"
                    android:background="@drawable/number_background_small"
                    android:padding="6dp"
                    android:minWidth="35dp"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>
</ScrollView>


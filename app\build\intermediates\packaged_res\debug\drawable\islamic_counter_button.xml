<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="90dp"
                android:centerColor="#1B5E20"
                android:endColor="#0D4E14" />
            <stroke
                android:width="4dp"
                android:color="@color/islamic_gold_light" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="90dp"
                android:centerColor="#2E7D32"
                android:endColor="#1B5E20" />
            <stroke
                android:width="3dp"
                android:color="@color/islamicGold" />
        </shape>
    </item>
</selector>

<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    tools:context=".AyaList">

    <!-- AppBar مع تأثير Parallax -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsingToolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="@color/colorPrimary"
            app:expandedTitleGravity="center"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:titleEnabled="true">

            <!-- خلفية متدرجة مع أيقونة -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/header_gradient"
                app:layout_collapseMode="parallax">

                <!-- أيقونة القرآن مع انيميشن -->
                <ImageView
                    android:id="@+id/quranIcon"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="40dp"
                    android:src="@drawable/ic_quran_modern"
                    android:tint="@android:color/white" />

                <!-- اسم القارئ -->
                <TextView
                    android:id="@+id/reciterNameText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/quranIcon"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="8dp"
                    android:text="القارئ"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- عدد السور -->
                <TextView
                    android:id="@+id/surahCountText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/reciterNameText"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="4dp"
                    android:text="114 سورة"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:alpha="0.8" />

            </RelativeLayout>

            <!-- Toolbar -->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:navigationIcon="@drawable/ic_arrow_back"
                app:title=""
                app:titleTextColor="@android:color/white" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <!-- المحتوى الرئيسي -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- شريط البحث العصري -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="25dp"
                app:cardElevation="4dp"
                app:strokeWidth="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="8dp"
                        android:src="@drawable/ic_search"
                        android:tint="@color/colorPrimary" />

                    <EditText
                        android:id="@+id/searchEditText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        android:background="@null"
                        android:hint="ابحث عن سورة..."
                        android:textSize="16sp"
                        android:textColorHint="@color/colorPrimary"
                        android:alpha="0.7" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- قائمة السور -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/surahRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_surah_modern" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- شريط التنزيل العائم -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/downloadProgressCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp"
        app:layout_anchor="@id/appBarLayout"
        app:layout_anchorGravity="bottom">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_download"
                    android:tint="@color/colorPrimary" />

                <TextView
                    android:id="@+id/downloadingText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:text="جاري تنزيل السورة..."
                    android:textSize="14sp"
                    android:textColor="@color/colorPrimary" />

                <TextView
                    android:id="@+id/progressPercentage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0%"
                    android:textSize="12sp"
                    android:textColor="@color/colorPrimary"
                    android:textStyle="bold" />

            </LinearLayout>

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/modernProgressBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:indicatorColor="@color/colorPrimary"
                app:trackColor="@color/colorPrimary"
                app:trackCornerRadius="4dp"
                android:alpha="0.3" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- إعلان في الأسفل -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:cardElevation="8dp"
        app:cardCornerRadius="0dp">

        <com.google.android.gms.ads.AdView
            android:id="@+id/adView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:adSize="BANNER"
            app:adUnitId="ca-app-pub-3940256099942544/6300978111" />

    </com.google.android.material.card.MaterialCardView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

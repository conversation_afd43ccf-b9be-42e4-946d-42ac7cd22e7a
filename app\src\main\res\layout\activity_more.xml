<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backgroundPrimary">

    <!-- الهيدر العلوي -->
    <LinearLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/header_gradient_bg"
        android:orientation="vertical"
        android:padding="20dp"
        android:elevation="4dp">

        <!-- زر الرجوع والعنوان -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:id="@+id/backButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="@drawable/back_button_bg"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_arrow_back"
                    android:tint="@color/white" />

            </LinearLayout>

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/more_page_title"
                android:textSize="24sp"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:fontFamily="@font/noto"
                android:gravity="center"
                android:layout_marginEnd="40dp" />

        </LinearLayout>

        <!-- وصف قصير -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/more_page_description"
            android:textSize="14sp"
            android:textColor="@color/white"
            android:fontFamily="@font/noto"
            android:gravity="center"
            android:layout_marginTop="8dp"
            android:alpha="0.9" />

    </LinearLayout>

    <!-- المحتوى الرئيسي -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/headerLayout"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- قسم الإعدادات والتطبيق -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/settings_and_app_section"
                android:textSize="16sp"
                android:textColor="@color/textPrimary"
                android:textStyle="bold"
                android:fontFamily="@font/noto"
                android:layout_marginBottom="12dp"
                android:layout_marginTop="8dp" />

            <!-- الإعدادات -->
            <LinearLayout
                android:id="@+id/settingsItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_settings"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/settings_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- عن التطبيق -->
            <LinearLayout
                android:id="@+id/aboutItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_info"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/about_app_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- مشاركة التطبيق -->
            <LinearLayout
                android:id="@+id/shareItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_share"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/share_app_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- تقييم التطبيق -->
            <LinearLayout
                android:id="@+id/rateItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_star"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/rate_app_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- قسم الأدوات الإسلامية -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/islamic_features_section"
                android:textSize="16sp"
                android:textColor="@color/textPrimary"
                android:textStyle="bold"
                android:fontFamily="@font/noto"
                android:layout_marginBottom="12dp"
                android:layout_marginTop="8dp" />

            <!-- السبحة الإلكترونية -->
            <LinearLayout
                android:id="@+id/tasbihItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_prayer_beads"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/electronic_tasbih_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- حصن المسلم -->
            <LinearLayout
                android:id="@+id/hisnItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/nav_azkar"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/hisn_muslim_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>



            <!-- أوقات الصلاة -->
            <LinearLayout
                android:id="@+id/prayerTimesItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_clock_islamic"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/prayer_times_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- قراء القرآن -->
            <LinearLayout
                android:id="@+id/readersItem"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/more_item_bg"
                android:orientation="horizontal"
                android:padding="16dp"
                android:layout_marginBottom="16dp"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/nav_listen"
                    android:tint="@color/islamicGreenMedium" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/quran_readers_title"
                    android:textSize="16sp"
                    android:textColor="@color/textPrimary"
                    android:fontFamily="@font/noto"
                    android:layout_marginStart="16dp" />

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_arrow_forward"
                    android:tint="@color/textSecondary" />

            </LinearLayout>

            <!-- معلومات التطبيق -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/app_info_bg"
                android:orientation="vertical"
                android:padding="20dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="قرآني"
                    android:textSize="20sp"
                    android:textColor="@color/islamicGreenDark"
                    android:textStyle="bold"
                    android:fontFamily="@font/noto"
                    android:gravity="center" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/app_description"
                    android:textSize="14sp"
                    android:textColor="@color/textSecondary"
                    android:fontFamily="@font/noto"
                    android:gravity="center"
                    android:layout_marginTop="4dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/app_version"
                    android:textSize="12sp"
                    android:textColor="@color/textSecondary"
                    android:fontFamily="@font/noto"
                    android:gravity="center"
                    android:layout_marginTop="8dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</RelativeLayout>

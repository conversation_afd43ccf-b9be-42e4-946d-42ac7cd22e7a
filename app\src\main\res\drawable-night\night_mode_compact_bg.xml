<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط في الوضع الليلي -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <solid android:color="#FF6F00" />
            <stroke android:width="1dp" android:color="#FFC107" />
        </shape>
    </item>
    
    <!-- الحالة العادية في الوضع الليلي -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <solid android:color="#FF9800" />
            <stroke android:width="1dp" android:color="#FFD700" />
        </shape>
    </item>
</selector>

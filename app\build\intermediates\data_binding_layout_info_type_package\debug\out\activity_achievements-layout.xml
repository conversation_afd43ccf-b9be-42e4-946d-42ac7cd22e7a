<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_achievements" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_achievements.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_achievements_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="15" startOffset="8" endLine="22" endOffset="47"/></Target><Target id="@+id/totalAchievements" view="TextView"><Expressions/><location startLine="79" startOffset="28" endLine="87" endOffset="72"/></Target><Target id="@+id/unlockedAchievements" view="TextView"><Expressions/><location startLine="107" startOffset="28" endLine="115" endOffset="72"/></Target><Target id="@+id/totalPoints" view="TextView"><Expressions/><location startLine="135" startOffset="28" endLine="143" endOffset="72"/></Target><Target id="@+id/achievementProgressBar" view="ProgressBar"><Expressions/><location startLine="158" startOffset="20" endLine="165" endOffset="84"/></Target><Target id="@+id/progressText" view="TextView"><Expressions/><location startLine="167" startOffset="20" endLine="176" endOffset="56"/></Target><Target id="@+id/achievementTabs" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="183" startOffset="12" endLine="193" endOffset="61"/></Target><Target id="@+id/achievementsRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="196" startOffset="12" endLine="201" endOffset="59"/></Target></Targets></Layout>
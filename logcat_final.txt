05-30 22:49:03.803 31777 31796 V WindowManagerShell: Transition requested (#365): android.os.BinderProxy@66f3e7f TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=2 lastActiveTime=48329572 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@7a27e4c} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{d731e95 com.qurany2019.quranyapp.Splash} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 365 }
05-30 22:49:03.882 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf7998) locale list changing from [] to [en-US]
05-30 22:49:03.898 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaff378) locale list changing from [] to [en-US]
05-30 22:49:03.982   723   866 D CoreBackPreview: Window{1f2bf49 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@5b0756f, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:49:04.110   723   746 I ActivityTaskManager: Displayed com.qurany2019.quranyapp/.Splash for user 0: +387ms
05-30 22:49:04.110   723   746 V WindowManager: Sent Transition (#365) createdAt=05-30 22:49:03.749 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=2 lastActiveTime=48329572 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{47c63f3 Task{9bfab68 #506 type=standard A=10220:com.qurany2019.quranyapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{62dab26 com.qurany2019.quranyapp.Splash} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 365 }
05-30 22:49:04.110   723   746 V WindowManager:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x98cca68 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ff000000 component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}},
05-30 22:49:04.110   723   746 V WindowManager:         {m=TO_BACK f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0x5fe7277 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ff000000 component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}}
05-30 22:49:04.110 31777 31796 V WindowManagerShell:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x9939baa sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ff000000 component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}},
05-30 22:49:04.110 31777 31796 V WindowManagerShell:         {m=TO_BACK f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0xcc3439b sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ff000000 component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}}
05-30 22:49:04.111 31777 31796 V WindowManagerShell: Transition doesn't have explicit remote, search filters for match for {id=365 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x9939baa sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ff000000 component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}},{m=TO_BACK f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0xcc3439b sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ff000000 component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:49:04.112 31777 31796 V WindowManagerShell: start default transition animation, info = {id=365 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x9939baa sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ff000000 component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}},{m=TO_BACK f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0xcc3439b sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ff000000 component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:49:04.269   723   842 I ImeTracker: com.qurany2019.quranyapp:ed023e82: onRequestHide at ORIGIN_SERVER reason HIDE_UNSPECIFIED_WINDOW fromUser false
05-30 22:49:04.299 32012 32012 I ImeTracker: com.qurany2019.quranyapp:ed023e82: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:49:04.340  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:49:04.349   723   866 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{4c21273 com.Zumbla.Burst2025/10246} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{954d5a9 com.android.microdroid.empty_payload/10194} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{beea72e com.app.yoursingleradio/10216} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{72162cf com.yourcompany.missingword/10219} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{6c545c com.demo.islamicprayer/10225} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{afc2365 com.alwan.kids2025/10223} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{542083a com.pdfrader.viewr.ahed/10215} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.858   723   781 I AppsFilter: interaction: PackageSetting{27b5ceb com.demo.chatai/10217} -> PackageSetting{ced0cb9 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:49:04.869  1156  1201 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.qurany2019.quranyapp], user=UserHandle{0}
05-30 22:49:04.872   723   930 D ShortcutService: changing package: com.qurany2019.quranyapp userId=0
05-30 22:49:04.891  1589 32384 I AiAiEcho: AppIndexer Package:[com.qurany2019.quranyapp] UserProfile:[0] Enabled:[true].
05-30 22:49:04.895  1589 32384 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.qurany2019.quranyapp], userId:[0], reason:[package is updated.].
05-30 22:49:04.960   723  1679 I ActivityTaskManager: START u0 {xflg=0x4 cmp=com.qurany2019.quranyapp/.MainActivity} with LAUNCH_MULTIPLE from uid 10220 (sr=123219575) (BAL_ALLOW_VISIBLE_WINDOW) result code=0
05-30 22:49:04.961 31777 31796 V WindowManagerShell: Transition requested (#366): android.os.BinderProxy@efb704e TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=3 lastActiveTime=48330739 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@79ac56f} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{7e8127c com.qurany2019.quranyapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 366 }
05-30 22:49:04.978 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf0f58) locale list changing from [] to [en-US]
05-30 22:49:04.980 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf1278) locale list changing from [] to [en-US]
05-30 22:49:05.004 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf1278) locale list changing from [en-US] to [ar]
05-30 22:49:05.282   723  1832 D CoreBackPreview: Window{46ae21 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@ffe85cd, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:49:05.430   723   746 I ActivityTaskManager: Displayed com.qurany2019.quranyapp/.MainActivity for user 0: +476ms
05-30 22:49:05.430   723   746 V WindowManager: Sent Transition (#366) createdAt=05-30 22:49:04.952 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=3 lastActiveTime=48330739 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{47c63f3 Task{9bfab68 #506 type=standard A=10220:com.qurany2019.quranyapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{4289ad7 com.qurany2019.quranyapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 366 }
05-30 22:49:05.430   723   746 V WindowManager:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{118461081 u0 com.qurany2019.quranyapp/.MainActivity)/@0xb6ffc71 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},
05-30 22:49:05.430   723   746 V WindowManager:         {m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x98cca68 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}
05-30 22:49:05.430 31777 31796 V WindowManagerShell:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{118461081 u0 com.qurany2019.quranyapp/.MainActivity)/@0xb59d705 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},
05-30 22:49:05.430 31777 31796 V WindowManagerShell:         {m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x7a2435a sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}
05-30 22:49:05.431 31777 31796 V WindowManagerShell: Transition doesn't have explicit remote, search filters for match for {id=366 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{118461081 u0 com.qurany2019.quranyapp/.MainActivity)/@0xb59d705 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},{m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x7a2435a sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:49:05.431 31777 31796 V WindowManagerShell: start default transition animation, info = {id=366 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{118461081 u0 com.qurany2019.quranyapp/.MainActivity)/@0xb59d705 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},{m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{123219575 u0 com.qurany2019.quranyapp/.Splash)/@0x7a2435a sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:49:05.548   723   842 I ImeTracker: com.qurany2019.quranyapp:8fe4c387: onRequestHide at ORIGIN_SERVER reason HIDE_UNSPECIFIED_WINDOW fromUser false
05-30 22:49:05.549 32012 32012 I ImeTracker: com.qurany2019.quranyapp:8fe4c387: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:49:05.552  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:49:05.553   723  1832 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:49:05.926   723  1832 D CoreBackPreview: Window{1f2bf49 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash}: Setting back callback null
05-30 22:49:06.782 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdb012b8) locale list changing from [] to [en-US]
05-30 22:49:06.785 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae9ed8) locale list changing from [] to [en-US]
05-30 22:49:06.786 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaffff8) locale list changing from [] to [en-US]

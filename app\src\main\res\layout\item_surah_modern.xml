<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="6dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- رقم السورة مع تصميم دائري -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:cardCornerRadius="24dp"
            app:cardElevation="2dp"
            app:cardBackgroundColor="@color/colorPrimary">

            <TextView
                android:id="@+id/surahNumber"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="1"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

        </com.google.android.material.card.MaterialCardView>

        <!-- معلومات السورة -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <!-- اسم السورة -->
            <TextView
                android:id="@+id/surahNameArabic"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الفاتحة"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/textColorPrimary"
                tools:text="الفاتحة" />

            <!-- حالة السورة -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <ImageView
                    android:id="@+id/statusIcon"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/ic_cloud_download"
                    android:tint="@color/colorAccent" />

                <TextView
                    android:id="@+id/statusText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:text="اضغط للتنزيل"
                    android:textSize="12sp"
                    android:textColor="@color/textColorSecondary"
                    tools:text="اضغط للتنزيل" />

            </LinearLayout>

        </LinearLayout>

        <!-- أزرار العمليات -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- زر التنزيل -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/downloadButton"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="8dp"
                app:icon="@drawable/ic_download"
                app:iconGravity="textStart"
                app:iconSize="24dp"
                app:iconTint="@color/colorPrimary"
                app:backgroundTint="@color/colorPrimary"
                app:iconTintMode="src_in"
                android:alpha="0.1" />

            <!-- زر التشغيل -->
            <com.google.android.material.floatingactionbutton.FloatingActionButton
                android:id="@+id/playButton"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/ic_play_arrow"
                app:backgroundTint="@color/colorPrimary"
                app:tint="@android:color/white"
                app:fabSize="mini"
                app:elevation="4dp"
                app:borderWidth="0dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- شريط تقدم التنزيل (مخفي افتراضياً) -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/itemProgressBar"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_gravity="bottom"
        android:visibility="gone"
        app:indicatorColor="@color/colorPrimary"
        app:trackColor="@color/colorPrimary"
        android:alpha="0.3" />

</com.google.android.material.card.MaterialCardView>

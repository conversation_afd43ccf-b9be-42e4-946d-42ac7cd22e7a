// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSurahModernBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton downloadButton;

  @NonNull
  public final LinearProgressIndicator itemProgressBar;

  @NonNull
  public final FloatingActionButton playButton;

  @NonNull
  public final ImageView statusIcon;

  @NonNull
  public final TextView statusText;

  @NonNull
  public final TextView surahNameArabic;

  @NonNull
  public final TextView surahNumber;

  private ItemSurahModernBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton downloadButton, @NonNull LinearProgressIndicator itemProgressBar,
      @NonNull FloatingActionButton playButton, @NonNull ImageView statusIcon,
      @NonNull TextView statusText, @NonNull TextView surahNameArabic,
      @NonNull TextView surahNumber) {
    this.rootView = rootView;
    this.downloadButton = downloadButton;
    this.itemProgressBar = itemProgressBar;
    this.playButton = playButton;
    this.statusIcon = statusIcon;
    this.statusText = statusText;
    this.surahNameArabic = surahNameArabic;
    this.surahNumber = surahNumber;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSurahModernBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSurahModernBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_surah_modern, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSurahModernBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.downloadButton;
      MaterialButton downloadButton = ViewBindings.findChildViewById(rootView, id);
      if (downloadButton == null) {
        break missingId;
      }

      id = R.id.itemProgressBar;
      LinearProgressIndicator itemProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (itemProgressBar == null) {
        break missingId;
      }

      id = R.id.playButton;
      FloatingActionButton playButton = ViewBindings.findChildViewById(rootView, id);
      if (playButton == null) {
        break missingId;
      }

      id = R.id.statusIcon;
      ImageView statusIcon = ViewBindings.findChildViewById(rootView, id);
      if (statusIcon == null) {
        break missingId;
      }

      id = R.id.statusText;
      TextView statusText = ViewBindings.findChildViewById(rootView, id);
      if (statusText == null) {
        break missingId;
      }

      id = R.id.surahNameArabic;
      TextView surahNameArabic = ViewBindings.findChildViewById(rootView, id);
      if (surahNameArabic == null) {
        break missingId;
      }

      id = R.id.surahNumber;
      TextView surahNumber = ViewBindings.findChildViewById(rootView, id);
      if (surahNumber == null) {
        break missingId;
      }

      return new ItemSurahModernBinding((MaterialCardView) rootView, downloadButton,
          itemProgressBar, playButton, statusIcon, statusText, surahNameArabic, surahNumber);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="content_tazker" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\content_tazker.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/content_tazker_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="484" endOffset="12"/></Target><Target id="@+id/content_tazker" view="LinearLayout"><Expressions/><location startLine="11" startOffset="0" endLine="483" endOffset="14"/></Target><Target id="@+id/txtzekr" view="TextView"><Expressions/><location startLine="47" startOffset="12" endLine="57" endOffset="52"/></Target><Target id="@+id/btnPrev" view="Button"><Expressions/><location startLine="66" startOffset="16" endLine="77" endOffset="55"/></Target><Target id="@+id/btnNext" view="Button"><Expressions/><location startLine="79" startOffset="16" endLine="89" endOffset="55"/></Target><Target id="@+id/counter" view="Button"><Expressions/><location startLine="134" startOffset="16" endLine="151" endOffset="46"/></Target><Target id="@+id/btnReset" view="Button"><Expressions/><location startLine="175" startOffset="8" endLine="188" endOffset="47"/></Target><Target id="@+id/btnTarget" view="Button"><Expressions/><location startLine="190" startOffset="8" endLine="203" endOffset="47"/></Target><Target id="@+id/tvTotalCount" view="TextView"><Expressions/><location startLine="250" startOffset="16" endLine="262" endOffset="46"/></Target><Target id="@+id/tvDailyTarget" view="TextView"><Expressions/><location startLine="284" startOffset="16" endLine="296" endOffset="46"/></Target><Target id="@+id/tvCount0" view="TextView"><Expressions/><location startLine="329" startOffset="16" endLine="341" endOffset="46"/></Target><Target id="@+id/tvCount1" view="TextView"><Expressions/><location startLine="363" startOffset="16" endLine="375" endOffset="46"/></Target><Target id="@+id/tvCount2" view="TextView"><Expressions/><location startLine="397" startOffset="16" endLine="409" endOffset="46"/></Target><Target id="@+id/tvCount3" view="TextView"><Expressions/><location startLine="431" startOffset="16" endLine="443" endOffset="46"/></Target><Target id="@+id/tvCount4" view="TextView"><Expressions/><location startLine="464" startOffset="16" endLine="476" endOffset="46"/></Target></Targets></Layout>
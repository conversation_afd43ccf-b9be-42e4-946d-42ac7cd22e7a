<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_azkar" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\item_azkar.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView" rootNodeViewId="@+id/azkarCardView"><Targets><Target id="@+id/azkarCardView" tag="layout/item_azkar_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="126" endOffset="35"/></Target><Target id="@+id/azkarTitle" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="32" endOffset="39"/></Target><Target id="@+id/azkarContent" view="TextView"><Expressions/><location startLine="35" startOffset="8" endLine="44" endOffset="48"/></Target><Target id="@+id/azkarProgressBar" view="ProgressBar"><Expressions/><location startLine="47" startOffset="8" endLine="54" endOffset="72"/></Target><Target id="@+id/azkarCount" view="TextView"><Expressions/><location startLine="64" startOffset="12" endLine="73" endOffset="42"/></Target><Target id="@+id/azkarStatus" view="TextView"><Expressions/><location startLine="76" startOffset="12" endLine="84" endOffset="41"/></Target><Target id="@+id/resetButton" view="Button"><Expressions/><location startLine="96" startOffset="12" endLine="106" endOffset="41"/></Target><Target id="@+id/incrementButton" view="Button"><Expressions/><location startLine="109" startOffset="12" endLine="120" endOffset="42"/></Target></Targets></Layout>
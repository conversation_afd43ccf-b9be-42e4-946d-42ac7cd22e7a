package com.qurany2019.quranyapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * مستقبل إشعارات أوقات الصلاة
 * يتم استدعاؤه عند حلول وقت الصلاة
 */
public class PrayerNotificationReceiver extends BroadcastReceiver {
    
    private static final String TAG = "PrayerNotificationReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "Prayer notification received");
        
        // استخراج البيانات من Intent
        String prayerName = intent.getStringExtra("prayer_name");
        String prayerIcon = intent.getStringExtra("prayer_icon");
        String cityName = intent.getStringExtra("city_name");
        int minutesBefore = intent.getIntExtra("minutes_before", 0);
        String prayerTime = intent.getStringExtra("prayer_time");
        
        if (prayerName == null) {
            Log.e(TAG, "Prayer name is null");
            return;
        }
        
        // إنشاء خدمة الإشعارات
        PrayerNotificationService notificationService = new PrayerNotificationService(context);
        
        // التحقق من تفعيل الإشعارات
        if (!notificationService.areNotificationsEnabled()) {
            Log.d(TAG, "Notifications are disabled");
            return;
        }
        
        // إنشاء عنوان ونص الإشعار
        String title;
        String message;
        
        if (minutesBefore > 0) {
            // إشعار قبل الصلاة
            title = String.format("%s تنبيه: اقتراب صلاة %s", prayerIcon, prayerName);
            message = String.format("ستحل صلاة %s خلال %d دقائق في %s\nالوقت: %s", 
                prayerName, minutesBefore, cityName != null ? cityName : "موقعك", prayerTime);
        } else {
            // إشعار وقت الصلاة
            title = String.format("%s حان وقت صلاة %s", prayerIcon, prayerName);
            message = String.format("حان الآن وقت صلاة %s في %s\nالوقت: %s", 
                prayerName, cityName != null ? cityName : "موقعك", prayerTime);
        }
        
        // إرسال الإشعار
        int notificationId = prayerName.hashCode() + minutesBefore;
        notificationService.showNotification(title, message, notificationId);
        
        Log.d(TAG, String.format("Notification sent for %s (%d minutes before)", prayerName, minutesBefore));
    }
}

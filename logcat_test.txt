05-30 22:39:05.962   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:06.016   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:06.918   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x1660efa sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:07.137   723   842 I ImeTracker: com.qurany2019.quranyapp:68e498df: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:07.193 27769 27769 I ImeTracker: com.qurany2019.quranyapp:68e498df: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:07.331  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:07.369   723  1257 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:07.949   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:07.995   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:09.043   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x5903b03 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:09.144   723   842 I ImeTracker: com.qurany2019.quranyapp:e30d6b72: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:09.150  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:09.153   723  1813 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:09.158 27769 27769 I ImeTracker: com.qurany2019.quranyapp:e30d6b72: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:09.951   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:09.993   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:10.413   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xc4376dc sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:10.504   723   842 I ImeTracker: com.qurany2019.quranyapp:9e560f0e: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:10.516 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9e560f0e: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:10.530  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:10.536   723   866 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:11.256 27769 27783 I y2019.quranyapp: Background concurrent mark compact GC freed 6663KB AllocSpace bytes, 36(1628KB) LOS objects, 49% free, 8314KB/16MB, paused 1.097ms,6.158ms total 67.596ms
05-30 22:39:12.004   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x37b9b21 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:12.038   723   842 I ImeTracker: com.qurany2019.quranyapp:ad894cfd: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:12.039 27769 27769 I ImeTracker: com.qurany2019.quranyapp:ad894cfd: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:12.047  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:12.051   723  1862 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:14.404   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.451   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.453   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.465   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.964   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:15.010   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:15.356   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd77fad0 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:15.502   723   842 I ImeTracker: com.qurany2019.quranyapp:aa1fbd8b: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:15.507  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:15.509   723   948 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:15.510 27769 27769 I ImeTracker: com.qurany2019.quranyapp:aa1fbd8b: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:16.906   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xad696fa sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:16.939   723   842 I ImeTracker: com.qurany2019.quranyapp:b0834f63: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:16.946  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:16.948   723  1693 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:16.974 27769 27769 I ImeTracker: com.qurany2019.quranyapp:b0834f63: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:17.946   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:17.987   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:18.449   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xc08e898 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:18.535   723   842 I ImeTracker: com.qurany2019.quranyapp:c6cab6ed: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:18.556  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:18.562   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:18.609 27769 27769 I ImeTracker: com.qurany2019.quranyapp:c6cab6ed: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:20.038   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd428cf6 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:20.113   723   842 I ImeTracker: com.qurany2019.quranyapp:cc04d17: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:20.118 27769 27769 I ImeTracker: com.qurany2019.quranyapp:cc04d17: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:20.122  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:20.124   723   948 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:20.945   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:20.983   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:21.470   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x7cc6e82 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:21.520   723   842 I ImeTracker: com.qurany2019.quranyapp:188cb7c3: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:21.532  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:21.533   723  1095 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:21.547 27769 27769 I ImeTracker: com.qurany2019.quranyapp:188cb7c3: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:22.931 27769 27783 I y2019.quranyapp: Background concurrent mark compact GC freed 5961KB AllocSpace bytes, 44(2096KB) LOS objects, 49% free, 8614KB/16MB, paused 9.536ms,3.098ms total 83.711ms
05-30 22:39:23.034   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xda5c297 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:23.169   723   842 I ImeTracker: com.qurany2019.quranyapp:9789688a: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:23.186  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:23.188   723  1693 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:23.208 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9789688a: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:23.948   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:23.985   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:24.461   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x2c21477 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:24.618   723   842 I ImeTracker: com.qurany2019.quranyapp:1b63baee: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:24.627  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:24.632   723  1832 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:24.641 27769 27769 I ImeTracker: com.qurany2019.quranyapp:1b63baee: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:26.123   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xa0b6d99 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:26.286   723   842 I ImeTracker: com.qurany2019.quranyapp:9aa3bbbf: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:26.294  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:26.294 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9aa3bbbf: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:26.297   723  1907 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:26.753   723   749 W ProcessStats: Tracking association SourceState{9180c9c com.qurany2019.quranyapp/10220 Top #19714} whose proc state 1 is better than process ProcessState{5d0d6cb com.google.android.gms/10147 pkg=com.google.android.gms} proc state 2 (435 skipped)
05-30 22:39:26.952   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:27.016   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:27.573   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x726e655 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:27.649   723   842 I ImeTracker: com.qurany2019.quranyapp:9cbab044: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:27.657  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:27.659   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:27.719 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9cbab044: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:28.955   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:29.247   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xb6d4a00 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:29.341   723   842 I ImeTracker: com.qurany2019.quranyapp:8d912d6a: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:29.347  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:29.348   723  1812 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:29.374 27769 27769 I ImeTracker: com.qurany2019.quranyapp:8d912d6a: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:29.956   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:29.994   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:30.293   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xab4a50c sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:30.358   723   842 I ImeTracker: com.qurany2019.quranyapp:1f330a2c: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:30.364  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:30.366   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:30.388 27769 27769 I ImeTracker: com.qurany2019.quranyapp:1f330a2c: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:32.077   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd36e200 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:32.108   723   842 I ImeTracker: com.qurany2019.quranyapp:1b890a50: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:32.112  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:32.116   723  1913 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:32.126 27769 27769 I ImeTracker: com.qurany2019.quranyapp:1b890a50: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:32.961   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:33.003   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:33.382   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xdd62c60 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:33.515   723   842 I ImeTracker: com.qurany2019.quranyapp:7bfc95a4: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:33.522  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:33.529   723  1239 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:33.538 27769 27769 I ImeTracker: com.qurany2019.quranyapp:7bfc95a4: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:35.023   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x2d12d57 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:35.099   723   842 I ImeTracker: com.qurany2019.quranyapp:43193036: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:35.113  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:35.116   723  1862 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:35.183 27769 27769 I ImeTracker: com.qurany2019.quranyapp:43193036: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:36.469   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd5148e5 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:36.712   723   842 I ImeTracker: com.qurany2019.quranyapp:4cc79df7: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:36.717  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:36.719   723  1095 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:36.730 27769 27769 I ImeTracker: com.qurany2019.quranyapp:4cc79df7: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:51.162   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd4319bc sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:51.314   723   842 I ImeTracker: com.qurany2019.quranyapp:da7ee548: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:51.317  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:51.318   723  1833 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:51.326 27769 27769 I ImeTracker: com.qurany2019.quranyapp:da7ee548: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:59.035   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x689eea3 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:59.090   723   842 I ImeTracker: com.qurany2019.quranyapp:7e6dd950: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:59.091 27769 27769 I ImeTracker: com.qurany2019.quranyapp:7e6dd950: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:59.096  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:59.104   723  1921 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:40:03.988 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdafd118) locale list changing from [] to [en-US]
05-30 22:40:03.991 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdaf82f8) locale list changing from [] to [en-US]
05-30 22:40:03.993 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdaf63b8) locale list changing from [] to [en-US]
05-30 22:40:20.248   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xa94f2b0 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:40:20.290   723   842 I ImeTracker: com.qurany2019.quranyapp:27709d46: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:40:20.325  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:40:20.336   723  1829 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:40:20.392 27769 27769 I ImeTracker: com.qurany2019.quranyapp:27709d46: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:40:39.002   723   739 W ActivityTaskManager: Permission Denial: starting Intent { flg=0x10000000 xflg=0x4 cmp=com.qurany2019.quranyapp/.MainActivity } from null (pid=30373, uid=2000) not exported from uid 10220
05-30 22:40:39.007   723   739 I ActivityTaskManager: START u0 {flg=0x10000000 xflg=0x4 cmp=com.qurany2019.quranyapp/.MainActivity} with LAUNCH_MULTIPLE from uid 2000 result code=0
05-30 22:40:39.012   723   749 W ProcessStats: Tracking association SourceState{cb635f4 com.qurany2019.quranyapp/10220 Top #20090} whose proc state 1 is better than process ProcessState{8cc1fab com.google.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/10220 pkg=com.qurany2019.quranyapp} proc state 2 (51 skipped)
05-30 22:40:40.188   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x97353b5 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:40:40.379   723   842 I ImeTracker: com.qurany2019.quranyapp:4a28efb9: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:40:40.406  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:40:40.411 27769 27769 I ImeTracker: com.qurany2019.quranyapp:4a28efb9: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:40:40.418   723   794 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:40:48.056   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:40:48.089   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:40:48.146 30494 30494 W Monkey  : args: [-p, com.qurany2019.quranyapp, -c, android.intent.category.LAUNCHER, 1]
05-30 22:40:48.250 30494 30494 W Monkey  :  arg: "com.qurany2019.quranyapp"
05-30 22:40:48.251 30494 30494 W Monkey  : data="com.qurany2019.quranyapp"
05-30 22:40:48.445   723  1831 I ActivityTaskManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash} with LAUNCH_MULTIPLE from uid 2000 (BAL_ALLOW_PERMISSION) result code=0
05-30 22:40:48.445 30496 30515 V WindowManagerShell: Transition requested (#320): android.os.BinderProxy@209fc14 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=394 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { flg=0x10000000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=2 lastActiveTime=47834224 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@4869cbd} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{84082b2 com.qurany2019.quranyapp.Splash} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=true locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(291, 628 - 790, 1708) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 320 }
05-30 22:40:48.446 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdaf63b8) locale list changing from [] to [en-US]
05-30 22:40:48.469 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdafd118) locale list changing from [] to [en-US]
05-30 22:40:48.562   723  1679 D CoreBackPreview: Window{e803a6c u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@13c347a, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:40:48.725   723   746 I ActivityTaskManager: Displayed com.qurany2019.quranyapp/.Splash for user 0: +263ms
05-30 22:40:48.726   723   746 V WindowManager: Sent Transition (#320) createdAt=05-30 22:40:48.429 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=394 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { flg=0x10000000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=2 lastActiveTime=47834224 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{ae98106 com.qurany2019.quranyapp.Splash} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=true locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(291, 628 - 790, 1708) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 320 }
05-30 22:40:48.726   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x7d8f893 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:40:48.822   723   842 I ImeTracker: com.qurany2019.quranyapp:d2b51b40: onRequestHide at ORIGIN_SERVER reason HIDE_UNSPECIFIED_WINDOW fromUser false
05-30 22:40:48.822 27769 27769 I ImeTracker: com.qurany2019.quranyapp:d2b51b40: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:40:48.830  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:40:48.834   723  1690 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:40:49.519   723  1690 I ActivityTaskManager: START u0 {xflg=0x4 cmp=com.qurany2019.quranyapp/.MainActivity} with LAUNCH_MULTIPLE from uid 10220 (sr=248915051) (BAL_ALLOW_VISIBLE_WINDOW) result code=0
05-30 22:40:49.519 30496 30515 V WindowManagerShell: Transition requested (#321): android.os.BinderProxy@296fb7f TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=394 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { flg=0x10000000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=3 lastActiveTime=47835298 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@e23f74c} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{b71a395 com.qurany2019.quranyapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 321 }
05-30 22:40:49.545 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdae88f8) locale list changing from [ar] to [en-US]
05-30 22:40:49.588 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdaebe18) locale list changing from [] to [en-US]
05-30 22:40:49.590 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdae9ed8) locale list changing from [] to [en-US]
05-30 22:40:49.592 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdafb4f8) locale list changing from [] to [en-US]
05-30 22:40:49.609 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdafb4f8) locale list changing from [en-US] to [ar]
05-30 22:40:50.178   723  1239 D CoreBackPreview: Window{b521970 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@9b7322b, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:40:50.328   723   746 I ActivityTaskManager: Displayed com.qurany2019.quranyapp/.MainActivity for user 0: +806ms
05-30 22:40:50.329   723   746 V WindowManager: Sent Transition (#321) createdAt=05-30 22:40:49.513 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=394 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { flg=0x10000000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=3 lastActiveTime=47835298 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{a041a59 com.qurany2019.quranyapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 321 }
05-30 22:40:50.329   723   746 V WindowManager:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{260297154 u0 com.qurany2019.quranyapp/.MainActivity)/@0x68dbba3 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},
05-30 22:40:50.329   723   746 V WindowManager:         {m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{248915051 u0 com.qurany2019.quranyapp/.Splash)/@0x6c4ed2 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}
05-30 22:40:50.333 30496 30515 V WindowManagerShell:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{260297154 u0 com.qurany2019.quranyapp/.MainActivity)/@0x19f5464 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},
05-30 22:40:50.333 30496 30515 V WindowManagerShell:         {m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{248915051 u0 com.qurany2019.quranyapp/.Splash)/@0xf41d2c9 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}
05-30 22:40:50.354 30496 30515 V WindowManagerShell: Transition doesn't have explicit remote, search filters for match for {id=321 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{260297154 u0 com.qurany2019.quranyapp/.MainActivity)/@0x19f5464 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},{m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{248915051 u0 com.qurany2019.quranyapp/.Splash)/@0xf41d2c9 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:40:50.360 30496 30515 V WindowManagerShell: start default transition animation, info = {id=321 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{260297154 u0 com.qurany2019.quranyapp/.MainActivity)/@0x19f5464 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},{m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{248915051 u0 com.qurany2019.quranyapp/.Splash)/@0xf41d2c9 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:40:50.372 27769 27783 I y2019.quranyapp: Background concurrent mark compact GC freed 7119KB AllocSpace bytes, 43(1564KB) LOS objects, 49% free, 9924KB/19MB, paused 2.837ms,9.294ms total 152.075ms
05-30 22:40:50.493   723   842 I ImeTracker: com.qurany2019.quranyapp:e95dc952: onRequestHide at ORIGIN_SERVER reason HIDE_UNSPECIFIED_WINDOW fromUser false
05-30 22:40:50.494 27769 27769 I ImeTracker: com.qurany2019.quranyapp:e95dc952: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:40:50.505  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:40:50.507   723   948 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:40:50.845   723  1833 D CoreBackPreview: Window{e803a6c u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash}: Setting back callback null
05-30 22:40:52.171 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdaff9b8) locale list changing from [] to [en-US]
05-30 22:40:52.174 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdb00958) locale list changing from [] to [en-US]
05-30 22:40:52.176 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdafa238) locale list changing from [] to [en-US]
05-30 22:40:57.074   723   749 W ProcessStats: Tracking association SourceState{cb635f4 com.qurany2019.quranyapp/10220 Top #20200} whose proc state 1 is better than process ProcessState{8cc1fab com.google.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/10220 pkg=com.qurany2019.quranyapp} proc state 2 (47 skipped)
05-30 22:40:57.721   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:40:57.827   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:40:58.660   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x3590c90 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:40:58.738   723   842 I ImeTracker: com.qurany2019.quranyapp:eed6956: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:40:58.782  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:40:58.806   723  1693 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:40:58.848 27769 27769 I ImeTracker: com.qurany2019.quranyapp:eed6956: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:40:59.724   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:40:59.764   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:41:00.330   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x712e420 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:00.380 27769 27783 I y2019.quranyapp: Background concurrent mark compact GC freed 5887KB AllocSpace bytes, 42(3464KB) LOS objects, 49% free, 10052KB/19MB, paused 1.209ms,5.499ms total 46.151ms
05-30 22:41:00.465   723   842 I ImeTracker: com.qurany2019.quranyapp:4f9dba28: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:00.468  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:00.469   723  1239 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:00.470 27769 27769 I ImeTracker: com.qurany2019.quranyapp:4f9dba28: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:01.728   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:41:01.889   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x2535855 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:02.056   723   842 I ImeTracker: com.qurany2019.quranyapp:34562397: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:02.058  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:02.062   723  1239 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:02.080 27769 27769 I ImeTracker: com.qurany2019.quranyapp:34562397: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:02.733   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:41:02.762   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:41:03.396   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x25ee4d8 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:03.445   723   842 I ImeTracker: com.qurany2019.quranyapp:830113c0: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:03.448 27769 27769 I ImeTracker: com.qurany2019.quranyapp:830113c0: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:03.449  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:03.452   723  1690 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:04.682   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xedaa9d sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:04.755   723   842 I ImeTracker: com.qurany2019.quranyapp:6e6b324: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:04.773  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:04.775   723   866 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:04.825 27769 27769 I ImeTracker: com.qurany2019.quranyapp:6e6b324: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:06.051   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x4caec6e sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:06.286   723   842 I ImeTracker: com.qurany2019.quranyapp:ccbd024c: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:06.302  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:06.304   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:06.319 27769 27769 I ImeTracker: com.qurany2019.quranyapp:ccbd024c: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:06.724   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:41:06.769   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:41:07.082   723  1094 W ProcessStats: Tracking association SourceState{9180c9c com.qurany2019.quranyapp/10220 Top #20355} whose proc state 1 is better than process ProcessState{5d0d6cb com.google.android.gms/10147 pkg=com.google.android.gms} proc state 2 (322 skipped)
05-30 22:41:07.615   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x78bc61d sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:07.653   723   842 I ImeTracker: com.qurany2019.quranyapp:fde837dc: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:07.660  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:07.661   723  1065 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:07.730 27769 27769 I ImeTracker: com.qurany2019.quranyapp:fde837dc: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:16.106   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xf115abe sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:16.264   723   842 I ImeTracker: com.qurany2019.quranyapp:de29fcb3: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:16.271  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:16.273   723  1679 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:16.292 27769 27769 I ImeTracker: com.qurany2019.quranyapp:de29fcb3: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:23.437   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_BACK f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xf115abe sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:23.459   723   749 W ProcessStats: Tracking association SourceState{cb635f4 com.qurany2019.quranyapp/10220 Top #20449} whose proc state 1 is better than process ProcessState{8cc1fab com.google.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/10220 pkg=com.qurany2019.quranyapp} proc state 2 (103 skipped)
05-30 22:41:23.474   723   749 E TransitionController:    ChangeInfo{51a91b2 container=Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp} flags=0x10}
05-30 22:41:23.474   723   749 E TransitionController:    ChangeInfo{e0aec80 container=ActivityRecord{260297154 u0 com.qurany2019.quranyapp/.MainActivity t394} flags=0x0}
05-30 22:41:24.538   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x18d2402 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:41:24.605   723   842 I ImeTracker: com.qurany2019.quranyapp:d05d803b: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:41:24.609  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:41:24.611   723  1831 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:41:24.702 27769 27769 I ImeTracker: com.qurany2019.quranyapp:d05d803b: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:41:53.752 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdae9ed8) locale list changing from [] to [en-US]
05-30 22:41:53.754 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdaff9b8) locale list changing from [] to [en-US]
05-30 22:41:53.756 27769 27769 I y2019.quranyapp: AssetManager2(0x7de4bdafa878) locale list changing from [] to [en-US]
05-30 22:42:40.369   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:42:40.405   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:42:41.387   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x7b8e9ea sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:42:41.538   723   842 I ImeTracker: com.qurany2019.quranyapp:a9f292c6: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:42:41.585 27769 27769 I ImeTracker: com.qurany2019.quranyapp:a9f292c6: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:42:41.596  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:42:41.635   723  1913 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:42:43.805   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x51092c6 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:42:43.955   723   842 I ImeTracker: com.qurany2019.quranyapp:ca818a96: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:42:43.962  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:42:43.963   723  1913 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:42:43.965 27769 27769 I ImeTracker: com.qurany2019.quranyapp:ca818a96: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:42:50.982   723   749 W ProcessStats: Tracking association SourceState{cb635f4 com.qurany2019.quranyapp/10220 Top #20597} whose proc state 1 is better than process ProcessState{8cc1fab com.google.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/10220 pkg=com.qurany2019.quranyapp} proc state 2 (112 skipped)
05-30 22:42:51.624   723   792 D InstallDependencyHelper: No missing dependency for com.qurany2019.quranyapp
05-30 22:42:51.662   723   781 I PackageManager: Update package com.qurany2019.quranyapp code path from /data/app/~~Sik_AwneQ5S5giJvP5RCOg==/com.qurany2019.quranyapp-uG72PJl-ch2tonZnWf9y1g== to /data/app/vmdl527697649.tmp; Retain data and using new
05-30 22:42:51.741   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:42:51.779   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{f565e77 com.Zumbla.Burst2025/10246} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{2585dd9 com.android.microdroid.empty_payload/10194} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{8a65afa com.app.yoursingleradio/10216} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{1f0199 com.yourcompany.missingword/10219} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{83c0df com.demo.islamicprayer/10225} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{50b5da com.alwan.kids2025/10223} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{aa636c2 com.pdfrader.viewr.ahed/10215} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{9ecebe1 com.demo.chatai/10217} -> PackageSetting{2ffbde com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{f565e77 com.Zumbla.Burst2025/10246} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{2585dd9 com.android.microdroid.empty_payload/10194} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{8a65afa com.app.yoursingleradio/10216} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.968   723   781 I AppsFilter: interaction: PackageSetting{1f0199 com.yourcompany.missingword/10219} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.969   723   781 I AppsFilter: interaction: PackageSetting{83c0df com.demo.islamicprayer/10225} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.969   723   781 I AppsFilter: interaction: PackageSetting{50b5da com.alwan.kids2025/10223} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.969   723   781 I AppsFilter: interaction: PackageSetting{aa636c2 com.pdfrader.viewr.ahed/10215} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:51.969   723   781 I AppsFilter: interaction: PackageSetting{9ecebe1 com.demo.chatai/10217} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.022   723   781 I PackageManager: installation completed for package:com.qurany2019.quranyapp. Final code path: /data/app/~~ltqX0XwRB7XC9-gwzLUUkw==/com.qurany2019.quranyapp-dz_ZMNrDyHP2-pifIdo4QQ==
05-30 22:42:52.022   723   781 I AppsFilter: interaction: PackageSetting{4c21273 com.Zumbla.Burst2025/10246} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.022   723   781 I AppsFilter: interaction: PackageSetting{954d5a9 com.android.microdroid.empty_payload/10194} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.022   723   781 I AppsFilter: interaction: PackageSetting{beea72e com.app.yoursingleradio/10216} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.022   723   781 I AppsFilter: interaction: PackageSetting{72162cf com.yourcompany.missingword/10219} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.022   723   781 I AppsFilter: interaction: PackageSetting{6c545c com.demo.islamicprayer/10225} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.023   723   781 I AppsFilter: interaction: PackageSetting{afc2365 com.alwan.kids2025/10223} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.023   723   781 I AppsFilter: interaction: PackageSetting{542083a com.pdfrader.viewr.ahed/10215} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.023   723   781 I AppsFilter: interaction: PackageSetting{27b5ceb com.demo.chatai/10217} -> PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:42:52.023   723   755 I ActivityManager: Force stopping com.qurany2019.quranyapp appid=10220 user=-1: installPackageLI
05-30 22:42:52.029   723   755 I ActivityManager: Killing 27769:com.qurany2019.quranyapp/u0a220 (adj 0): stop com.qurany2019.quranyapp due to installPackageLI
05-30 22:42:52.051   723   755 W ActivityTaskManager: Failed for dead process. ClientTransactionItem=ConfigurationChangeItem{deviceId=0, config{1.0 310mcc260mnc [en_US] ldltr sw411dp w411dp h914dp 420dpi nrml long port finger qwerty/v/v dpad/v winConfig={ mBounds=Rect(0, 0 - 1080, 2400) mAppBounds=Rect(0, 0 - 1080, 2400) mMaxBounds=Rect(0, 0 - 1080, 2400) mDisplayRotation=ROTATION_0 mWindowingMode=fullscreen mActivityType=undefined mAlwaysOnTop=undefined mRotation=ROTATION_0} s.72 fontWeightAdjustment=0}} owner=ProcessRecord{d5bbe34 27769:com.qurany2019.quranyapp/u0a220}
05-30 22:42:52.054   723   755 W ActivityTaskManager: Force removing ActivityRecord{260297154 u0 com.qurany2019.quranyapp/.MainActivity t394 f}}: app died, no saved state
05-30 22:42:52.055   723   811 W UsageStatsService: Unexpected activity event reported! (com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity event : 23 instanceId : 51747795)
05-30 22:42:52.061   723   755 W ActivityTaskManager: Force removing ActivityRecord{242290802 u0 com.qurany2019.quranyapp/.MainActivity t394 f}}: app died, no saved state
05-30 22:42:52.062   723   811 W UsageStatsService: Unexpected activity event reported! (com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity event : 23 instanceId : 29082046)
05-30 22:42:52.072   723  1693 I ImeTracker: com.qurany2019.quranyapp:be3f8869: onRequestHide at ORIGIN_SERVER reason HIDE_REMOVE_CLIENT fromUser false
05-30 22:42:52.072   723  1693 I ImeTracker: com.qurany2019.quranyapp:be3f8869: onCancelled at PHASE_SERVER_SHOULD_HIDE
05-30 22:42:52.074   723   880 D ConnectivityService: releasing NetworkRequest [ REQUEST id=733, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ] (release request)
05-30 22:42:52.075   723   880 D ConnectivityService: releasing NetworkRequest [ REQUEST id=735, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ] (release request)
05-30 22:42:52.075   723   880 D ConnectivityService: releasing NetworkRequest [ REQUEST id=738, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ] (release request)
05-30 22:42:52.076   723   880 D ConnectivityService: releasing NetworkRequest [ REQUEST id=740, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ] (release request)
05-30 22:42:52.076   723  1812 V ActivityManager: Got obituary of 27769:com.qurany2019.quranyapp
05-30 22:42:52.081   723   781 I ActivityManager: Force stopping com.qurany2019.quranyapp appid=10220 user=0: pkg removed
05-30 22:42:52.213  1144  1373 I SatelliteAppTracker: onPackageUpdateFinished : com.qurany2019.quranyapp
05-30 22:42:52.213  1144  1373 I SatelliteAppTracker: onPackageModified : com.qurany2019.quranyapp
05-30 22:42:52.215  1156  1201 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.qurany2019.quranyapp], user=UserHandle{0}
05-30 22:42:52.227   723   937 I SdkSandboxManager: No SDKs used. Skipping SDK data reconcilation for CallingInfo{mUid=10220, mPackageName='com.qurany2019.quranyapp, mAppProcessToken='null'}
05-30 22:42:52.227   723   930 D ShortcutService: replacing package: com.qurany2019.quranyapp userId=0
05-30 22:42:52.236  1156  1156 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.239  1519  1519 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.239  1413  1413 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.240  1292  1292 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.241 21503 21503 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.242  5569  5569 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.242  1931  1931 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.243  1601  1601 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.245  2563  2563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.247 22480 22480 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.250  3087  3087 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.250  1438  1438 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.251  3739  3739 I SafetyLabelChangedBroadcastReceiver: received broadcast packageName: com.qurany2019.quranyapp, current user: UserHandle{0}, packageChangeEvent: UPDATE, intent user: UserHandle{0}
05-30 22:42:52.257  1674  1674 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.258  1589 31836 I AiAiEcho: AppIndexer Package:[com.qurany2019.quranyapp] UserProfile:[0] Enabled:[true].
05-30 22:42:52.260  3739  3739 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.260  1563  1563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.262  1589  1589 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.263  1632  1632 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.264  1129  1129 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.264  1589 31836 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.qurany2019.quranyapp], userId:[0], reason:[package is updated.].
05-30 22:42:52.265  1096  1096 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.266  1169  1169 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.275   723   856 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:42:52.294  6018  6018 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.294   723   937 I SdkSandboxManager: No SDKs used. Skipping SDK data reconcilation for CallingInfo{mUid=10220, mPackageName='com.qurany2019.quranyapp, mAppProcessToken='null'}
05-30 22:42:52.302  1144  1144 D CarrierSvcBindHelper: onPackageUpdateFinished: com.qurany2019.quranyapp
05-30 22:42:52.314  1931  1931 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.317  1413  1413 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.318   723   723 I Telecom : CarModeTracker: Package com.qurany2019.quranyapp is not tracked.: SSH.oR@AtI
05-30 22:42:52.319  1438  1438 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.321  1156  1156 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.320 21503 21503 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.325  3739  3739 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.325 22480 22480 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.329  5569  5569 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.330  1563  1563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.330  1589  1589 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.331  1144  1144 D CarrierSvcBindHelper: onPackageModified: com.qurany2019.quranyapp
05-30 22:42:52.340  1674  1674 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.342  1169  1169 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.343  4248  4248 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.344  1601  1601 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.344  3087  3087 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.345  6018  6018 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.345  1632  1632 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.347  4248  4248 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.347  2563  2563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.358  1096  1096 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.358  1129  1129 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.365  1519  1519 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.369  1292  1292 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.407  9010  9010 I Finsky:background: [2] www.d(66): IQ: onPackageRemoved com.qurany2019.quranyapp, replacing=true, uninstallingFromPlay=false
05-30 22:42:52.417 31843 31843 I y2019.quranyapp: Late-enabling -Xcheck:jni
05-30 22:42:52.430   723   756 I ActivityManager: Start proc 31843:com.qurany2019.quranyapp/u0a220 for broadcast {com.qurany2019.quranyapp/com.onesignal.UpgradeReceiver}
05-30 22:42:52.454  1144  1368 D ImsResolver: maybeAddedImsService, packageName: com.qurany2019.quranyapp
05-30 22:42:52.455  1144  1368 V ImsResolver: searchForImsServices: package=com.qurany2019.quranyapp, users=[UserHandle{0}]
05-30 22:42:52.456  1144  1368 V ImsResolver: searchForImsServices: package=com.qurany2019.quranyapp, users=[UserHandle{0}]
05-30 22:42:52.484 31843 31843 I y2019.quranyapp: Using CollectorTypeCMC GC.
05-30 22:42:52.498 31843 31843 W y2019.quranyapp: Unexpected CPU variant for x86: x86_64.
05-30 22:42:52.498 31843 31843 W y2019.quranyapp: Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, alderlake, default
05-30 22:42:52.520  1144  1144 W VvmPkgInstalledRcvr: carrierVvmPkgAdded: carrier vvm packages doesn't contain com.qurany2019.quranyapp
05-30 22:42:52.520  1144  1144 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.527  1144  1144 D SatelliteController: packageStateChanged: package:com.qurany2019.quranyapp DefaultSmsPackageName:com.google.android.apps.messaging
05-30 22:42:52.539  1144  1144 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.555  9010  9010 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.564  9061  9135 I Finsky  : [65] neq.a(47): AIM: AppInfoManager-Perf > OnDeviceAppInfo > cacheHitCount=0, cacheMissCount=1. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:42:52.600  9010  9010 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.717  9061  9133 I Finsky  : [63] nel.a(218): AIM: AppInfoManager-Perf > ItemModel > CacheSize=0, cacheHitCount=0, cacheMissCount=1, total appsWithNoServerDataCount=0. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:42:52.758   723   723 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.781   723   723 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.822  9061  9108 I Finsky  : [58] neq.a(47): AIM: AppInfoManager-Perf > OnDeviceAppInfo > cacheHitCount=0, cacheMissCount=1. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:42:52.905  9061  9061 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.908  9061  9061 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:52.952  9061  9135 I Finsky  : [65] nel.a(218): AIM: AppInfoManager-Perf > ItemModel > CacheSize=0, cacheHitCount=0, cacheMissCount=1, total appsWithNoServerDataCount=0. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:42:53.105  1519 31880 W SQLiteLog: (28) double-quoted string literal: "com.qurany2019.quranyapp"
05-30 22:42:53.265  1292 31877 I ProximityAuth: [RecentAppsMediator] Package added: (user=UserHandle{0}) com.qurany2019.quranyapp
05-30 22:42:53.443 31777 31777 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:53.446 31777 31777 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:42:53.669 31843 31843 D nativeloader: Configuring clns-9 for other apk /data/app/~~ltqX0XwRB7XC9-gwzLUUkw==/com.qurany2019.quranyapp-dz_ZMNrDyHP2-pifIdo4QQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~ltqX0XwRB7XC9-gwzLUUkw==/com.qurany2019.quranyapp-dz_ZMNrDyHP2-pifIdo4QQ==/lib/x86_64:/data/app/~~ltqX0XwRB7XC9-gwzLUUkw==/com.qurany2019.quranyapp-dz_ZMNrDyHP2-pifIdo4QQ==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.qurany2019.quranyapp
05-30 22:42:53.737 31843 31843 I y2019.quranyapp: AssetManager2(0x7de4bdae4758) locale list changing from [] to [en-US]
05-30 22:42:53.774 31843 31843 V GraphicsEnvironment: com.qurany2019.quranyapp is not listed in per-application setting
05-30 22:42:53.775 31843 31843 V GraphicsEnvironment: com.qurany2019.quranyapp is not listed in ANGLE allowlist or settings, returning default
05-30 22:42:54.182 31843 31916 W y2019.quranyapp: ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*487574312]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[])
05-30 22:42:54.190 31843 31918 W y2019.quranyapp: Loading /data/app/~~2SvaL9YfN7vpmX9t2Fm-Qw==/com.google.android.gms-G2F3qHx-yB4BXIFIuBhiPw==/oat/x86_64/base.odex non-executable as it requires an image which we failed to load
05-30 22:42:54.241 31843 31918 I y2019.quranyapp: AssetManager2(0x7de4bdadf2f8) locale list changing from [] to [en-US]
05-30 22:42:54.268 31843 31916 W y2019.quranyapp: ClassLoaderContext classpath element checksum mismatch. expected=**********, found=862405734 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*487574312]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[/data/app/~~ltqX0XwRB7XC9-gwzLUUkw==/com.qurany2019.quranyapp-dz_ZMNrDyHP2-pifIdo4QQ==/base.apk*862405734])
05-30 22:42:54.332 31843 31916 I y2019.quranyapp: AssetManager2(0x7de4bdaed718) locale list changing from [] to [en-US]
05-30 22:42:54.334 31843 31916 I y2019.quranyapp: AssetManager2(0x7de4bdae1b98) locale list changing from [] to [en-US]
05-30 22:42:54.404   723   812 W JobServiceContext: Sending onNetworkChanged for a job that isn't started. JobStatus{6a56cfb #u0a220/107 com.qurany2019.quranyapp/androidx.work.impl.background.systemjob.SystemJobService u=0 s=10220 NET READY}
05-30 22:42:54.412   723  1239 D ConnectivityService: requestNetwork for uid/pid:10220/31843 activeRequest: null callbackRequest: 1184 [NetworkRequest [ REQUEST id=1185, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]] callback flags: 0 order: ********** isUidTracked: false declaredMethods: AVAIL|LOST|NC
05-30 22:42:54.413   723   723 W JobScheduler: Job didn't exist in JobStore: 6744630 #u0a220/107 com.qurany2019.quranyapp/androidx.work.impl.background.systemjob.SystemJobService
05-30 22:42:54.414   723   874 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=1185, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:42:54.414   723   874 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1185, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:42:54.414   723   874 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1185, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:42:54.414   723   874 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1185, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:42:54.669 31843 31926 E y2019.quranyapp: No package ID 75 found for resource ID 0x750b000f.
05-30 22:42:54.672 31843 31926 I FA      :   adb shell setprop debug.firebase.analytics.app com.qurany2019.quranyapp
05-30 22:43:00.060 31843 31974 D ProfileInstaller: Installing profile for com.qurany2019.quranyapp
05-30 22:43:09.544   723  1065 I AppsFilter: interaction: PackageSetting{1f71c87 com.qurany2019.quranyapp/10220} -> PackageSetting{a57c9ef com.google.android.apps.nexuslauncher/10180} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{4c21273 com.Zumbla.Burst2025/10246} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{954d5a9 com.android.microdroid.empty_payload/10194} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{beea72e com.app.yoursingleradio/10216} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{72162cf com.yourcompany.missingword/10219} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{6c545c com.demo.islamicprayer/10225} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{afc2365 com.alwan.kids2025/10223} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{542083a com.pdfrader.viewr.ahed/10215} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.559   723   781 I AppsFilter: interaction: PackageSetting{27b5ceb com.demo.chatai/10217} -> PackageSetting{1f5f53a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:43:10.578   723   930 D ShortcutService: changing package: com.qurany2019.quranyapp userId=0
05-30 22:43:10.799  1156  1201 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.qurany2019.quranyapp], user=UserHandle{0}
05-30 22:43:10.810  1589 31837 I AiAiEcho: AppIndexer Package:[com.qurany2019.quranyapp] UserProfile:[0] Enabled:[true].
05-30 22:43:10.811  1589 31837 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.qurany2019.quranyapp], userId:[0], reason:[package is updated.].
05-30 22:43:24.419   723   825 D ActivityManager: freezing 31843 com.qurany2019.quranyapp
05-30 22:47:40.176   723   792 D InstallDependencyHelper: No missing dependency for com.qurany2019.quranyapp
05-30 22:47:40.238   723   781 I PackageManager: Update package com.qurany2019.quranyapp code path from /data/app/~~ltqX0XwRB7XC9-gwzLUUkw==/com.qurany2019.quranyapp-dz_ZMNrDyHP2-pifIdo4QQ== to /data/app/vmdl837814284.tmp; Retain data and using new
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{f565e77 com.Zumbla.Burst2025/10246} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{2585dd9 com.android.microdroid.empty_payload/10194} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{8a65afa com.app.yoursingleradio/10216} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{1f0199 com.yourcompany.missingword/10219} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{83c0df com.demo.islamicprayer/10225} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{50b5da com.alwan.kids2025/10223} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{aa636c2 com.pdfrader.viewr.ahed/10215} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{9ecebe1 com.demo.chatai/10217} -> PackageSetting{1b4e11a com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{f565e77 com.Zumbla.Burst2025/10246} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{2585dd9 com.android.microdroid.empty_payload/10194} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{8a65afa com.app.yoursingleradio/10216} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{1f0199 com.yourcompany.missingword/10219} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.342   723   781 I AppsFilter: interaction: PackageSetting{83c0df com.demo.islamicprayer/10225} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.343   723   781 I AppsFilter: interaction: PackageSetting{50b5da com.alwan.kids2025/10223} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.343   723   781 I AppsFilter: interaction: PackageSetting{aa636c2 com.pdfrader.viewr.ahed/10215} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.343   723   781 I AppsFilter: interaction: PackageSetting{9ecebe1 com.demo.chatai/10217} -> PackageSetting{3b1c2dc com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.409   723   755 I ActivityManager: Force stopping com.qurany2019.quranyapp appid=10220 user=-1: installPackageLI
05-30 22:47:40.418   723   781 I PackageManager: installation completed for package:com.qurany2019.quranyapp. Final code path: /data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==
05-30 22:47:40.419   723   781 I AppsFilter: interaction: PackageSetting{4c21273 com.Zumbla.Burst2025/10246} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.419   723   781 I AppsFilter: interaction: PackageSetting{954d5a9 com.android.microdroid.empty_payload/10194} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.419   723   781 I AppsFilter: interaction: PackageSetting{beea72e com.app.yoursingleradio/10216} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.419   723   781 I AppsFilter: interaction: PackageSetting{72162cf com.yourcompany.missingword/10219} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.419   723   781 I AppsFilter: interaction: PackageSetting{6c545c com.demo.islamicprayer/10225} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.420   723   781 I AppsFilter: interaction: PackageSetting{afc2365 com.alwan.kids2025/10223} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.420   723   781 I AppsFilter: interaction: PackageSetting{542083a com.pdfrader.viewr.ahed/10215} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.420   723   781 I AppsFilter: interaction: PackageSetting{27b5ceb com.demo.chatai/10217} -> PackageSetting{393d593 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:40.428   723   755 I ActivityManager: Killing 31843:com.qurany2019.quranyapp/u0a220 (adj 905): stop com.qurany2019.quranyapp due to installPackageLI
05-30 22:47:40.481   723  1913 V ActivityManager: Got obituary of 31843:com.qurany2019.quranyapp
05-30 22:47:40.482   723   781 I ActivityManager: Force stopping com.qurany2019.quranyapp appid=10220 user=0: pkg removed
05-30 22:47:40.552  1156  1201 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.qurany2019.quranyapp], user=UserHandle{0}
05-30 22:47:40.572  1413  1413 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.573  1156  1156 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.573  1632  1632 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.574  4248  4248 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.575  1169  1169 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.576  1563  1563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.577  1438  1438 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.578 31777 31777 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.580  1674  1674 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.581  1096  1096 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.581  1413  1413 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.581  1129  1129 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.582  1156  1156 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.583  1632  1632 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.584  1438  1438 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.584  1169  1169 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.584 31777 31777 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.585  1674  1674 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.585  1096  1096 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.580  1589  1589 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.590  1589 32009 I AiAiEcho: AppIndexer Package:[com.qurany2019.quranyapp] UserProfile:[0] Enabled:[true].
05-30 22:47:40.590  1589 32009 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.qurany2019.quranyapp], userId:[0], reason:[package is updated.].
05-30 22:47:40.590  1589  1589 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.593  1292  1292 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.595  1129  1129 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.597  4248  4248 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.603  6018  6018 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.603  6018  6018 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.605   723   856 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:47:40.611  1601  1601 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.613  1601  1601 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.613   723   930 D ShortcutService: replacing package: com.qurany2019.quranyapp userId=0
05-30 22:47:40.616  1292  1292 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.617  1144  1144 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.618  1144  1144 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.620  1563  1563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.645  1144  1144 D CarrierSvcBindHelper: onPackageUpdateFinished: com.qurany2019.quranyapp
05-30 22:47:40.646  1144  1373 I SatelliteAppTracker: onPackageUpdateFinished : com.qurany2019.quranyapp
05-30 22:47:40.649  1144  1373 I SatelliteAppTracker: onPackageModified : com.qurany2019.quranyapp
05-30 22:47:40.665   723   723 I Telecom : CarModeTracker: Package com.qurany2019.quranyapp is not tracked.: SSH.oR@AuM
05-30 22:47:40.679  1144  1144 D CarrierSvcBindHelper: onPackageModified: com.qurany2019.quranyapp
05-30 22:47:40.681  3739  3739 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.684  3739  3739 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.688   723   723 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.689   723   937 I SdkSandboxManager: No SDKs used. Skipping SDK data reconcilation for CallingInfo{mUid=10220, mPackageName='com.qurany2019.quranyapp, mAppProcessToken='null'}
05-30 22:47:40.728  9010  9010 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.735  9010  9010 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.735   723   937 I SdkSandboxManager: No SDKs used. Skipping SDK data reconcilation for CallingInfo{mUid=10220, mPackageName='com.qurany2019.quranyapp, mAppProcessToken='null'}
05-30 22:47:40.737   723   723 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.753  9061  9061 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.754  9061  9061 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:40.757  3739  3739 I SafetyLabelChangedBroadcastReceiver: received broadcast packageName: com.qurany2019.quranyapp, current user: UserHandle{0}, packageChangeEvent: UPDATE, intent user: UserHandle{0}
05-30 22:47:40.760   723   756 I ActivityManager: Start proc 32012:com.qurany2019.quranyapp/u0a220 for broadcast {com.qurany2019.quranyapp/com.onesignal.UpgradeReceiver}
05-30 22:47:40.803  1144  1368 D ImsResolver: maybeAddedImsService, packageName: com.qurany2019.quranyapp
05-30 22:47:40.803  1144  1368 V ImsResolver: searchForImsServices: package=com.qurany2019.quranyapp, users=[UserHandle{0}]
05-30 22:47:40.805  1144  1368 V ImsResolver: searchForImsServices: package=com.qurany2019.quranyapp, users=[UserHandle{0}]
05-30 22:47:40.824  1144  1144 W VvmPkgInstalledRcvr: carrierVvmPkgAdded: carrier vvm packages doesn't contain com.qurany2019.quranyapp
05-30 22:47:40.839  1144  1144 D SatelliteController: packageStateChanged: package:com.qurany2019.quranyapp DefaultSmsPackageName:com.google.android.apps.messaging
05-30 22:47:40.870 32012 32012 I y2019.quranyapp: Late-enabling -Xcheck:jni
05-30 22:47:40.873  9010  9010 I Finsky:background: [2] www.d(66): IQ: onPackageRemoved com.qurany2019.quranyapp, replacing=true, uninstallingFromPlay=false
05-30 22:47:41.003 32012 32012 I y2019.quranyapp: Using CollectorTypeCMC GC.
05-30 22:47:41.018  9061  9108 I Finsky  : [58] neq.a(47): AIM: AppInfoManager-Perf > OnDeviceAppInfo > cacheHitCount=0, cacheMissCount=1. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:47:41.083 32012 32012 W y2019.quranyapp: Unexpected CPU variant for x86: x86_64.
05-30 22:47:41.083 32012 32012 W y2019.quranyapp: Known variants: atom, sandybridge, silvermont, goldmont, goldmont-plus, goldmont-without-sha-xsaves, tremont, kabylake, alderlake, default
05-30 22:47:41.121  1519  1519 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.125  1519  1519 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.126  9061  9159 I Finsky  : [68] nel.a(218): AIM: AppInfoManager-Perf > ItemModel > CacheSize=0, cacheHitCount=0, cacheMissCount=1, total appsWithNoServerDataCount=0. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:47:41.138  2563  2563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.141  2563  2563 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.164  5569  5569 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.164  5569  5569 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.197  3087  3087 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.198  3087  3087 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.224 22480 22480 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.245 21503 21503 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.249 21503 21503 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.249 22480 22480 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:41.258  9061  9133 I Finsky  : [63] neq.a(47): AIM: AppInfoManager-Perf > OnDeviceAppInfo > cacheHitCount=0, cacheMissCount=1. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:47:41.461  9061  9135 I Finsky  : [65] nel.a(218): AIM: AppInfoManager-Perf > ItemModel > CacheSize=0, cacheHitCount=0, cacheMissCount=1, total appsWithNoServerDataCount=0. Missed  in cache (limit 10) : [com.qurany2019.quranyapp]
05-30 22:47:41.477  1292 32062 I ProximityAuth: [RecentAppsMediator] Package added: (user=UserHandle{0}) com.qurany2019.quranyapp
05-30 22:47:41.628  1519 32053 W SQLiteLog: (28) double-quoted string literal: "com.qurany2019.quranyapp"
05-30 22:47:41.984 32012 32012 D nativeloader: Configuring clns-9 for other apk /data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/lib/x86_64:/data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/base.apk!/lib/x86_64, permitted_path=/data:/mnt/expand:/data/user/0/com.qurany2019.quranyapp
05-30 22:47:42.022 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae4758) locale list changing from [] to [en-US]
05-30 22:47:42.067 32012 32012 V GraphicsEnvironment: com.qurany2019.quranyapp is not listed in per-application setting
05-30 22:47:42.070 32012 32012 V GraphicsEnvironment: com.qurany2019.quranyapp is not listed in ANGLE allowlist or settings, returning default
05-30 22:47:42.356 32012 32083 W y2019.quranyapp: Loading /data/app/~~2SvaL9YfN7vpmX9t2Fm-Qw==/com.google.android.gms-G2F3qHx-yB4BXIFIuBhiPw==/oat/x86_64/base.odex non-executable as it requires an image which we failed to load
05-30 22:47:42.360 32012 32081 W y2019.quranyapp: ClassLoaderContext classpath size mismatch. expected=1, found=0 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*487574312]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[])
05-30 22:47:42.500 32012 32081 I y2019.quranyapp: AssetManager2(0x7de4bdae3df8) locale list changing from [] to [en-US]
05-30 22:47:42.524 32012 32081 W y2019.quranyapp: ClassLoaderContext classpath element checksum mismatch. expected=**********, found=108530326 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*487574312]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[/data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/base.apk*108530326])
05-30 22:47:42.579 32012 32081 I y2019.quranyapp: AssetManager2(0x7de4bdae9898) locale list changing from [] to [en-US]
05-30 22:47:42.580 32012 32081 I y2019.quranyapp: AssetManager2(0x7de4bdaeb7d8) locale list changing from [] to [en-US]
05-30 22:47:42.658 32012 32089 E y2019.quranyapp: No package ID 75 found for resource ID 0x750b000f.
05-30 22:47:42.659 32012 32089 I FA      :   adb shell setprop debug.firebase.analytics.app com.qurany2019.quranyapp
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{4c21273 com.Zumbla.Burst2025/10246} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{954d5a9 com.android.microdroid.empty_payload/10194} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{beea72e com.app.yoursingleradio/10216} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{72162cf com.yourcompany.missingword/10219} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{6c545c com.demo.islamicprayer/10225} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{afc2365 com.alwan.kids2025/10223} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{542083a com.pdfrader.viewr.ahed/10215} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.546   723   781 I AppsFilter: interaction: PackageSetting{27b5ceb com.demo.chatai/10217} -> PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:43.576   723   930 D ShortcutService: changing package: com.qurany2019.quranyapp userId=0
05-30 22:47:43.788  1156  1201 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.qurany2019.quranyapp], user=UserHandle{0}
05-30 22:47:43.822  1589 32011 I AiAiEcho: AppIndexer Package:[com.qurany2019.quranyapp] UserProfile:[0] Enabled:[true].
05-30 22:47:43.824  1589 32011 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.qurany2019.quranyapp], userId:[0], reason:[package is updated.].
05-30 22:47:47.902 32012 32104 D ProfileInstaller: Installing profile for com.qurany2019.quranyapp
05-30 22:47:51.163 32105 32105 W Monkey  : args: [-p, com.qurany2019.quranyapp, -c, android.intent.category.LAUNCHER, 1]
05-30 22:47:51.221 32105 32105 W Monkey  :  arg: "com.qurany2019.quranyapp"
05-30 22:47:51.222 32105 32105 W Monkey  : data="com.qurany2019.quranyapp"
05-30 22:47:51.465   723  1679 I ActivityTaskManager: START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash} with LAUNCH_MULTIPLE from uid 2000 (BAL_ALLOW_PERMISSION) result code=0
05-30 22:47:51.471 31777 31796 V WindowManagerShell: Transition requested (#363): android.os.BinderProxy@6935c28 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=1 lastActiveTime=48257241 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@1c29d41} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{d0cf2e6 com.qurany2019.quranyapp.Splash} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 363 }
05-30 22:47:51.557 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaed3f8) locale list changing from [] to [en-US]
05-30 22:47:51.572   723  1259 D CoreBackPreview: Window{5ea7f80 u0 Splash Screen com.qurany2019.quranyapp}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@d3813ac, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:47:51.795   723   746 V WindowManager: Sent Transition (#363) createdAt=05-30 22:47:51.423 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=1 lastActiveTime=48257241 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{47c63f3 Task{9bfab68 #506 type=standard A=10220:com.qurany2019.quranyapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{5cc8b0 com.qurany2019.quranyapp.Splash} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=false isVisible=false isVisibleRequested=false isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 363 }
05-30 22:47:51.796   723   746 V WindowManager:         {WCT{RemoteToken{47c63f3 Task{9bfab68 #506 type=standard A=10220:com.qurany2019.quranyapp}}} m=OPEN f=NONE leash=Surface(name=Task=506)/@0xe52c12d sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:47:52.086   723  1258 D CoreBackPreview: Window{9a27647 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@9934f9d, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:47:52.324   723   842 I ImeTracker: com.qurany2019.quranyapp:23c94534: onRequestHide at ORIGIN_SERVER reason HIDE_UNSPECIFIED_WINDOW fromUser false
05-30 22:47:52.326 32012 32029 I y2019.quranyapp: Compiler allocated 5042KB to compile void android.view.ViewRootImpl.performTraversals()
05-30 22:47:52.332 32012 32012 I ImeTracker: com.qurany2019.quranyapp:23c94534: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:47:52.364   723   746 I ActivityTaskManager: Displayed com.qurany2019.quranyapp/.Splash for user 0: +908ms
05-30 22:47:52.383  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:47:52.393   723  1258 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:47:52.497 32012 32032 W y2019.quranyapp: Suspending all threads took: 8.130ms
05-30 22:47:52.516 32012 32032 I y2019.quranyapp: Background concurrent mark compact GC freed 3640KB AllocSpace bytes, 33(840KB) LOS objects, 49% free, 3303KB/6607KB, paused 10.549ms,2.519ms total 42.061ms
05-30 22:47:52.706   723  1832 D CoreBackPreview: Window{5ea7f80 u0 Splash Screen com.qurany2019.quranyapp EXITING}: Setting back callback null
05-30 22:47:52.947   723  1832 I ActivityTaskManager: START u0 {xflg=0x4 cmp=com.qurany2019.quranyapp/.MainActivity} with LAUNCH_MULTIPLE from uid 10220 (sr=93765259) (BAL_ALLOW_VISIBLE_WINDOW) result code=0
05-30 22:47:52.948 31777 31796 V WindowManagerShell: Transition requested (#364): android.os.BinderProxy@d4629c4 TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=2 lastActiveTime=48258726 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{android.window.IWindowContainerToken$Stub$Proxy@931b0ad} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{cb1fe2 com.qurany2019.quranyapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 364 }
05-30 22:47:53.035 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae9898) locale list changing from [] to [en-US]
05-30 22:47:53.039 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae4a78) locale list changing from [] to [en-US]
05-30 22:47:53.079 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae4a78) locale list changing from [en-US] to [ar]
05-30 22:47:53.106 32012 32012 I y2019.quranyapp: hiddenapi: Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (runtime_flags=0, domain=platform, api=unsupported) from Landroidx/appcompat/widget/ViewUtils; (domain=app) using reflection: allowed
05-30 22:47:53.375 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaee9d8) locale list changing from [] to [en-US]
05-30 22:47:53.383 32012 32012 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:53.384 32012 32032 W y2019.quranyapp: Suspending all threads took: 7.962ms
05-30 22:47:53.390 32012 32032 I y2019.quranyapp: Background concurrent mark compact GC freed 1551KB AllocSpace bytes, 5(164KB) LOS objects, 49% free, 4812KB/9624KB, paused 8.919ms,1.281ms total 24.622ms
05-30 22:47:53.390 32012 32012 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on Background for 6.868ms
05-30 22:47:53.475 32012 32012 W y2019.quranyapp: ClassLoaderContext classpath element checksum mismatch. expected=**********, found=108530326 (DLC[];PCL[base.apk***********]{PCL[/system/framework/org.apache.http.legacy.jar***********]#PCL[/system/framework/com.android.media.remotedisplay.jar*487574312]#PCL[/system/framework/com.android.location.provider.jar***********]#PCL[/system_ext/framework/androidx.window.extensions.jar***********]#PCL[/system_ext/framework/androidx.window.sidecar.jar***********]} | DLC[];PCL[/data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/base.apk*108530326])
05-30 22:47:53.478 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae6378) locale list changing from [] to [en-US]
05-30 22:47:53.478 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf1ef8) locale list changing from [] to [en-US]
05-30 22:47:53.541   723  1258 D ConnectivityService: requestNetwork for uid/pid:10220/32012 activeRequest: null callbackRequest: 1187 [NetworkRequest [ REQUEST id=1188, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]] callback flags: 0 order: ********** isUidTracked: false declaredMethods: AVAIL|LOST
05-30 22:47:53.549   723   874 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=1188, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:53.552   723   874 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1188, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:53.552   723   874 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1188, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:53.552   723   874 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1188, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:53.599 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdae1558) locale list changing from [] to [en-US]
05-30 22:47:53.600 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaefc98) locale list changing from [] to [en-US]
05-30 22:47:53.601 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf4dd8) locale list changing from [] to [en-US]
05-30 22:47:53.601 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf0918) locale list changing from [] to [en-US]
05-30 22:47:53.602 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaeffb8) locale list changing from [] to [en-US]
05-30 22:47:53.602 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf50f8) locale list changing from [] to [en-US]
05-30 22:47:53.603 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaef978) locale list changing from [] to [en-US]
05-30 22:47:53.604 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf2538) locale list changing from [] to [en-US]
05-30 22:47:53.606 32012 32012 W y2019.quranyapp: Failed to find entry 'classes.dex': Entry not found
05-30 22:47:53.622 32012 32012 W y2019.quranyapp: Loading /data/app/~~334Vp6JWM-r7YEiQ-O0AtA==/com.google.android.webview-llps56_N0mbl0JgT036iSw==/oat/x86_64/base.odex non-executable as it requires an image which we failed to load
05-30 22:47:53.819 32012 32133 W InteractionJankMonitor: Initializing without READ_DEVICE_CONFIG permission. enabled=false, interval=1, missedFrameThreshold=3, frameTimeThreshold=64, package=com.qurany2019.quranyapp
05-30 22:47:53.838 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdadecb8) locale list changing from [] to [en-US]
05-30 22:47:53.841 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf2218) locale list changing from [] to [en-US]
05-30 22:47:53.880   723   756 I ActivityManager: Start proc 32163:com.google.android.webview:sandboxed_process0:org.chromium.content.app.SandboxedProcessService0:0/u0i31 for  {com.qurany2019.quranyapp/org.chromium.content.app.SandboxedProcessService0:0}
05-30 22:47:54.157  5398  5398 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:54.164  5398  5398 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:54.164  5398  5398 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:54.165  5398  5398 D ActivityThread: Package [com.qurany2019.quranyapp] reported as REPLACED, but missing application info. Assuming REMOVED.
05-30 22:47:54.216   723  1259 D ConnectivityService: requestNetwork for uid/pid:10220/32012 activeRequest: null callbackRequest: 1189 [NetworkRequest [ REQUEST id=1190, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]] callback flags: 0 order: ********** isUidTracked: false declaredMethods: AVAIL|LOST|NC|LP
05-30 22:47:54.217   723   874 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=1190, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.217   723   874 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1190, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.217   723   874 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1190, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.217   723   874 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1190, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.270 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaef018) locale list changing from [] to [en-US]
05-30 22:47:54.335 32012 32201 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.336 32012 32202 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.337 32012 32203 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.337 32012 32205 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.337 32012 32209 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.337 32012 32207 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.338 32012 32213 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.338 32012 32206 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.339 32012 32208 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.339 32012 32216 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.339 32012 32221 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.339 32012 32217 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.340 32012 32210 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.340 32012 32214 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.341 32012 32222 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.341 32012 32215 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.341 32012 32219 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.341 32012 32204 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.342 32012 32218 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.342 32012 32220 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.342 32012 32224 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.342 32012 32211 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.342 32012 32223 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.342 32012 32212 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.343 32012 32226 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.343 32012 32225 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.343 32012 32228 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.344 32012 32229 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.344 32012 32230 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.344   723  1258 D ConnectivityService: requestNetwork for uid/pid:10220/32012 activeRequest: null callbackRequest: 1192 [NetworkRequest [ REQUEST id=1193, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]] callback flags: 0 order: ********** isUidTracked: false declaredMethods: AVAIL|LOST|NC
05-30 22:47:54.345   723   874 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=1193, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.345   723   874 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1193, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.345   723   874 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1193, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.345   723   874 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1193, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.350   723  1258 D ConnectivityService: requestNetwork for uid/pid:10220/32012 activeRequest: null callbackRequest: 1194 [NetworkRequest [ REQUEST id=1195, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]] callback flags: 0 order: ********** isUidTracked: false declaredMethods: AVAIL|LOST|NC
05-30 22:47:54.351   723   874 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=1195, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.352   723   874 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1195, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.352   723   874 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1195, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.352   723   874 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=1195, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED&NOT_BANDWIDTH_CONSTRAINED Uid: 10220 RequestorUid: 10220 RequestorPkg: com.qurany2019.quranyapp UnderlyingNetworks: Null] ]
05-30 22:47:54.371 32012 32201 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on Background for 36.038ms
05-30 22:47:54.375 32012 32202 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32203 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32205 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32209 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32207 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32213 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32206 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32208 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32216 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32221 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32217 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32210 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32214 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.377 32012 32222 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32215 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32219 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32218 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32204 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32220 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32211 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32223 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32224 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.378 32012 32212 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.379 32012 32226 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.379 32012 32225 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.380 32012 32228 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.380 32012 32229 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.380 32012 32230 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.381 32012 32202 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 45.163ms
05-30 22:47:54.381 32012 32230 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 37.462ms
05-30 22:47:54.381 32012 32209 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.308ms
05-30 22:47:54.382 32012 32203 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.727ms
05-30 22:47:54.382 32012 32213 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.013ms
05-30 22:47:54.382 32012 32205 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.872ms
05-30 22:47:54.382 32012 32206 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 43.985ms
05-30 22:47:54.382 32012 32207 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.895ms
05-30 22:47:54.382 32012 32221 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 43.389ms
05-30 22:47:54.382 32012 32211 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.382 32012 32223 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.383 32012 32204 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.383 32012 32224 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.383 32012 32208 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.383 32012 32216 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.383 32012 32214 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 42.875ms
05-30 22:47:54.383 32012 32229 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 39.822ms
05-30 22:47:54.383 32012 32222 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 42.261ms
05-30 22:47:54.384 32012 32223 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32218 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32204 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32208 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32216 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32228 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32211 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32224 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.384 32012 32210 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.385 32012 32217 I y2019.quranyapp: Waiting for a blocking GC ClassLinker
05-30 22:47:54.385 32012 32223 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 42.652ms
05-30 22:47:54.385 32012 32218 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 43.162ms
05-30 22:47:54.386 32012 32204 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.002ms
05-30 22:47:54.386 32012 32211 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 43.627ms
05-30 22:47:54.386 32012 32228 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 42.820ms
05-30 22:47:54.386 32012 32224 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 44.282ms
05-30 22:47:54.387 32012 32208 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 48.568ms
05-30 22:47:54.388   723  1259 I AppsFilter: interaction: PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} -> PackageSetting{97975b com.google.android.apps.maps/10159} BLOCKED
05-30 22:47:54.388 32012 32216 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 49.640ms
05-30 22:47:54.389 32012 32217 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 49.527ms
05-30 22:47:54.390 32012 32220 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 47.671ms
05-30 22:47:54.390 32012 32210 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 50.237ms
05-30 22:47:54.395 32012 32219 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 53.296ms
05-30 22:47:54.396 32012 32225 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 52.716ms
05-30 22:47:54.396 32012 32215 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 54.749ms
05-30 22:47:54.396 32012 32226 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 53.690ms
05-30 22:47:54.396 32012 32212 I y2019.quranyapp: WaitForGcToComplete blocked ClassLinker on ClassLinker for 54.155ms
05-30 22:47:54.422 32012 32081 E y2019.quranyapp: No package ID 75 found for resource ID 0x750b000f.
05-30 22:47:54.686 32012 32270 D vulkan  : searching for layers in '/data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/lib/x86_64'
05-30 22:47:54.690 32012 32270 D vulkan  : searching for layers in '/data/app/~~q40hN0R0j9xe_JaTGJ9GCw==/com.qurany2019.quranyapp-gT3RQkh53UGiKYFofq6KBQ==/base.apk!/lib/x86_64'
05-30 22:47:54.697   723   866 D CoreBackPreview: Window{fca4b16 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity}: Setting back callback OnBackInvokedCallbackInfo{mCallback=android.window.IOnBackInvokedCallback$Stub$Proxy@a5db295, mPriority=0, mIsAnimationCallback=false, mOverrideBehavior=0}
05-30 22:47:55.007   723   746 I ActivityTaskManager: Displayed com.qurany2019.quranyapp/.MainActivity for user 0: +2s63ms
05-30 22:47:55.007   723   746 V WindowManager: Sent Transition (#364) createdAt=05-30 22:47:52.941 via request=TransitionRequestInfo { type = OPEN, triggerTask = TaskInfo{userId=0 taskId=506 effectiveUid=10220 displayId=0 isRunning=true baseIntent=Intent { act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10200000 cmp=com.qurany2019.quranyapp/.Splash } baseActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} topActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.MainActivity} origActivity=null realActivity=ComponentInfo{com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash} numActivities=2 lastActiveTime=48258726 supportsMultiWindow=true resizeMode=1 isResizeable=true minWidth=-1 minHeight=-1 defaultMinSize=220 token=WCT{RemoteToken{47c63f3 Task{9bfab68 #506 type=standard A=10220:com.qurany2019.quranyapp}}} topActivityType=1 pictureInPictureParams=null shouldDockBigOverlays=false launchIntoPipHostTaskId=-1 lastParentTaskIdBeforePip=-1 displayCutoutSafeInsets=Rect(0, 0 - 0, 0) topActivityInfo=ActivityInfo{7629f4d com.qurany2019.quranyapp.MainActivity} launchCookies=[] positionInParent=Point(0, 0) parentTaskId=-1 isFocused=true isVisible=true isVisibleRequested=true isTopActivityNoDisplay=false isSleeping=false locusId=null displayAreaFeatureId=1 isTopActivityTransparent=false isActivityStackTransparent=false lastNonFullscreenBounds=Rect(284, 660 - 796, 1740) capturedLink=null capturedLinkTimestamp=0 requestedVisibleTypes=-9 topActivityRequestOpenInBrowserEducationTimestamp=0 appCompatTaskInfo=AppCompatTaskInfo { topActivityInSizeCompat=false eligibleForLetterboxEducation= false isLetterboxEducationEnabled= true isLetterboxDoubleTapEnabled= false eligibleForUserAspectRatioButton= false topActivityBoundsLetterboxed= false isFromLetterboxDoubleTap= false topActivityLetterboxVerticalPosition= -1 topActivityLetterboxHorizontalPosition= -1 topActivityLetterboxWidth=-1 topActivityLetterboxHeight=-1 topActivityAppBounds=Rect(0, 0 - 1080, 2337) isUserFullscreenOverrideEnabled=false isSystemFullscreenOverrideEnabled=false hasMinAspectRatioOverride=false topActivityLetterboxBounds=null cameraCompatTaskInfo=CameraCompatTaskInfo { freeformCameraCompatMode=inactive}} topActivityMainWindowFrame=null}, pipChange = null, remoteTransition = null, displayChange = null, flags = 0, debugId = 364 }
05-30 22:47:55.008   723   746 V WindowManager:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0x5fe7277 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},
05-30 22:47:55.008   723   746 V WindowManager:         {m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{93765259 u0 com.qurany2019.quranyapp/.Splash)/@0x6ed0c76 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}
05-30 22:47:55.013 31777 31796 V WindowManagerShell:         {m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0x4ff9f73 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},
05-30 22:47:55.013 31777 31796 V WindowManagerShell:         {m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{93765259 u0 com.qurany2019.quranyapp/.Splash)/@0x95e1e30 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}
05-30 22:47:55.013 31777 31796 V WindowManagerShell: Transition doesn't have explicit remote, search filters for match for {id=364 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0x4ff9f73 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},{m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{93765259 u0 com.qurany2019.quranyapp/.Splash)/@0x95e1e30 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:47:55.013 31777 31796 V WindowManagerShell: start default transition animation, info = {id=364 t=OPEN f=0x0 trk=0 r=[0@Point(0, 0)] c=[{m=OPEN f=FILLS_TASK leash=Surface(name=ActivityRecord{263792022 u0 com.qurany2019.quranyapp/.MainActivity)/@0x4ff9f73 sb=Rect(0, 0 - 0, 0) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=-1->0 r=-1->0:-1 bc=ffffffff component=com.qurany2019.quranyapp/.MainActivity opt={t=FROM_STYLE mUserId=0}},{m=CLOSE f=FILLS_TASK leash=Surface(name=ActivityRecord{93765259 u0 com.qurany2019.quranyapp/.Splash)/@0x95e1e30 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 bc=ffffffff component=com.qurany2019.quranyapp/.Splash opt={t=FROM_STYLE mUserId=0}}]}
05-30 22:47:55.198   723   842 I ImeTracker: com.qurany2019.quranyapp:dd337b8f: onRequestHide at ORIGIN_SERVER reason HIDE_UNSPECIFIED_WINDOW fromUser false
05-30 22:47:55.239  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:47:55.248 32012 32012 I ImeTracker: com.qurany2019.quranyapp:dd337b8f: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:47:55.248   723  1259 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:47:55.531   723  1679 D CoreBackPreview: Window{9a27647 u0 com.qurany2019.quranyapp/com.qurany2019.quranyapp.Splash}: Setting back callback null
05-30 22:47:56.654 32012 32032 I y2019.quranyapp: Background concurrent mark compact GC freed 5372KB AllocSpace bytes, 18(872KB) LOS objects, 49% free, 8414KB/16MB, paused 1.950ms,5.567ms total 75.113ms
05-30 22:47:57.560   723  1259 I AppsFilter: interaction: PackageSetting{f13fb6e com.qurany2019.quranyapp/10220} -> PackageSetting{a57c9ef com.google.android.apps.nexuslauncher/10180} BLOCKED
05-30 22:47:57.848 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf6098) locale list changing from [] to [en-US]
05-30 22:47:57.850 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdaf7fd8) locale list changing from [] to [en-US]
05-30 22:47:57.850 32012 32012 I y2019.quranyapp: AssetManager2(0x7de4bdafc178) locale list changing from [] to [en-US]
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{4c21273 com.Zumbla.Burst2025/10246} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{954d5a9 com.android.microdroid.empty_payload/10194} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{beea72e com.app.yoursingleradio/10216} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{72162cf com.yourcompany.missingword/10219} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{6c545c com.demo.islamicprayer/10225} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{afc2365 com.alwan.kids2025/10223} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.599   723   781 I AppsFilter: interaction: PackageSetting{542083a com.pdfrader.viewr.ahed/10215} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.600   723   781 I AppsFilter: interaction: PackageSetting{27b5ceb com.demo.chatai/10217} -> PackageSetting{2ec8a61 com.qurany2019.quranyapp/10220} BLOCKED
05-30 22:47:58.622   723   930 D ShortcutService: changing package: com.qurany2019.quranyapp userId=0
05-30 22:47:58.763  1156  1201 D PackageUpdatedTask: Package updated: mOp=UPDATE packages=[com.qurany2019.quranyapp], user=UserHandle{0}
05-30 22:47:58.815  1589 32102 I AiAiEcho: AppIndexer Package:[com.qurany2019.quranyapp] UserProfile:[0] Enabled:[true].
05-30 22:47:58.815  1589 32102 I AiAiEcho: AppFetcherImplV2 updateApps package:[com.qurany2019.quranyapp], userId:[0], reason:[package is updated.].

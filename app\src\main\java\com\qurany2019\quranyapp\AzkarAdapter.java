package com.qurany2019.quranyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.qurany2019.quranyapp.database.AzkarDatabase;
import com.qurany2019.quranyapp.database.entities.AzkarProgressEntity;
import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;

public class AzkarAdapter extends RecyclerView.Adapter<AzkarAdapter.AzkarViewHolder> {
    private Context context;
    private List<AzkarItem> azkarList;
    private SharedPreferences progressPrefs;
    private String azkarType;
    private OnAzkarClickListener listener;

    // النظام الجديد
    private AzkarDatabase database;
    private ExecutorService executor;

    public interface OnAzkarClickListener {
        void onAzkarCompleted(AzkarItem azkar);
        void onSectionCompleted(String sectionName);
        void onProgressChanged(int progress);
    }

    public AzkarAdapter(Context context, List<AzkarItem> azkarList) {
        this.context = context;
        this.azkarList = azkarList;
        this.progressPrefs = context.getSharedPreferences("azkar_progress", Context.MODE_PRIVATE);

        // تهيئة النظام الجديد
        this.database = AzkarDatabase.getInstance(context);
        this.executor = Executors.newSingleThreadExecutor();
    }

    public void setAzkarType(String azkarType) {
        this.azkarType = azkarType;
    }

    public void setOnAzkarClickListener(OnAzkarClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public AzkarViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_azkar, parent, false);
        return new AzkarViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AzkarViewHolder holder, int position) {
        AzkarItem azkar = azkarList.get(position);
        
        // تحميل التقدم المحفوظ
        loadAzkarProgress(azkar);
        
        // إعداد النصوص
        if (azkar.getTitle() != null && !azkar.getTitle().isEmpty()) {
            holder.titleText.setText(azkar.getTitle());
            holder.titleText.setVisibility(View.VISIBLE);
        } else {
            holder.titleText.setVisibility(View.GONE);
        }
        
        holder.contentText.setText(azkar.getContent());
        holder.countText.setText(azkar.getCountText());

        // تحديث شريط التقدم
        holder.progressBar.setMax(azkar.getCount());
        holder.progressBar.setProgress(azkar.getCurrentCount());

        // تحديث حالة الإنجاز
        updateStatusText(holder, azkar);

        // تحديث مظهر البطاقة حسب الحالة
        updateCardAppearance(holder, azkar);

        // إعداد زر التسبيح
        holder.incrementButton.setOnClickListener(v -> {
            if (!azkar.isCompleted()) {
                azkar.incrementCount();

                // حفظ التقدم
                saveAzkarProgress(azkar);

                // تحديث قاعدة البيانات الجديدة
                updateDatabaseProgress(azkar);

                // إشعار الإنجاز
                if (azkar.isCompleted()) {
                    showCompletionNotification(azkar);
                    if (listener != null) {
                        listener.onAzkarCompleted(azkar);
                    }
                }

                // إشعار المستمع
                if (listener != null) {
                    listener.onProgressChanged(azkar.getProgressPercentage());
                }

                notifyItemChanged(position);
            }
        });

        // إعداد زر الإعادة تعيين
        holder.resetButton.setOnClickListener(v -> {
            azkar.resetCount();
            saveAzkarProgress(azkar);
            notifyItemChanged(position);
            Toast.makeText(context, "تم إعادة تعيين الذكر", Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    public int getItemCount() {
        return azkarList.size();
    }

    public void updateAzkar(List<AzkarItem> newAzkarList) {
        this.azkarList = newAzkarList;
        notifyDataSetChanged();
    }

    // حفظ تقدم الذكر
    private void saveAzkarProgress(AzkarItem azkar) {
        if (azkarType != null) {
            String key = azkarType + "_" + azkar.getAzkarId();
            SharedPreferences.Editor editor = progressPrefs.edit();
            editor.putInt(key + "_count", azkar.getCurrentCount());
            editor.putBoolean(key + "_completed", azkar.isCompleted());
            editor.putLong(key + "_last_read", System.currentTimeMillis());
            editor.apply();

            // حفظ إحصائيات إضافية للربط مع صفحة الإحصائيات
            saveStatisticsData(azkar);
        }
    }

    private void saveStatisticsData(AzkarItem azkar) {
        SharedPreferences.Editor editor = progressPrefs.edit();

        // إجمالي الأذكار المكتملة
        if (azkar.isCompleted()) {
            int totalCompleted = progressPrefs.getInt("total_azkar_completed", 0);
            editor.putInt("total_azkar_completed", totalCompleted + 1);
        }

        // إجمالي التسبيحات
        int totalTasbih = progressPrefs.getInt("total_tasbih_count", 0);
        editor.putInt("total_tasbih_count", totalTasbih + 1);

        // أذكار اليوم
        String today = getTodayDateString();
        String dailyKey = "daily_azkar_" + today;
        int dailyCount = progressPrefs.getInt(dailyKey, 0);
        editor.putInt(dailyKey, dailyCount + 1);

        // تحديث الأيام المتتالية
        updateStreakDays(editor, today);

        // حفظ وقت الجلسة
        updateSessionTime(editor);

        editor.apply();
    }

    private void updateStreakDays(SharedPreferences.Editor editor, String today) {
        String lastActiveDate = progressPrefs.getString("last_active_date", "");

        if (!today.equals(lastActiveDate)) {
            // يوم جديد
            String yesterday = getYesterdayDateString();
            int currentStreak = progressPrefs.getInt("streak_days", 0);

            if (yesterday.equals(lastActiveDate)) {
                // استمرار السلسلة
                editor.putInt("streak_days", currentStreak + 1);
            } else {
                // بداية سلسلة جديدة
                editor.putInt("streak_days", 1);
            }

            editor.putString("last_active_date", today);
        }
    }

    private void updateSessionTime(SharedPreferences.Editor editor) {
        long sessionStart = progressPrefs.getLong("session_start_time", System.currentTimeMillis());
        long sessionDuration = System.currentTimeMillis() - sessionStart;
        long totalSessionTime = progressPrefs.getLong("total_session_time", 0);
        editor.putLong("total_session_time", totalSessionTime + sessionDuration);
        editor.putLong("session_start_time", System.currentTimeMillis());
    }

    private String getYesterdayDateString() {
        java.util.Calendar cal = java.util.Calendar.getInstance();
        cal.add(java.util.Calendar.DAY_OF_YEAR, -1);
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
        return sdf.format(cal.getTime());
    }

    // تحميل تقدم الذكر
    private void loadAzkarProgress(AzkarItem azkar) {
        if (azkarType != null) {
            String key = azkarType + "_" + azkar.getAzkarId();
            int savedCount = progressPrefs.getInt(key + "_count", 0);
            boolean isCompleted = progressPrefs.getBoolean(key + "_completed", false);
            long lastReadTime = progressPrefs.getLong(key + "_last_read", 0);
            
            azkar.setCurrentCount(savedCount);
            azkar.setLastReadTime(lastReadTime);
            if (isCompleted) {
                azkar.markAsCompleted();
            }
        }
    }

    // تحديث نص الحالة
    private void updateStatusText(AzkarViewHolder holder, AzkarItem azkar) {
        if (azkar.isCompleted()) {
            holder.statusText.setText("مكتمل ✓");
            holder.statusText.setTextColor(context.getResources().getColor(R.color.islamic_green));
        } else if (azkar.isInProgress()) {
            holder.statusText.setText("جاري...");
            holder.statusText.setTextColor(context.getResources().getColor(R.color.islamic_gold));
        } else {
            holder.statusText.setText("لم يبدأ");
            holder.statusText.setTextColor(context.getResources().getColor(R.color.islamic_gray));
        }
    }

    // تحديث مظهر البطاقة
    private void updateCardAppearance(AzkarViewHolder holder, AzkarItem azkar) {
        if (azkar.isCompleted()) {
            // مكتمل - أخضر
            holder.cardView.setCardBackgroundColor(context.getResources().getColor(R.color.azkar_completed));
            holder.countText.setTextColor(context.getResources().getColor(R.color.islamic_green));
            holder.incrementButton.setEnabled(false);
            holder.incrementButton.setText("مكتمل");
        } else if (azkar.isInProgress()) {
            // جاري - برتقالي
            holder.cardView.setCardBackgroundColor(context.getResources().getColor(R.color.azkar_in_progress));
            holder.countText.setTextColor(context.getResources().getColor(R.color.islamic_gold));
            holder.incrementButton.setEnabled(true);
            holder.incrementButton.setText("تسبيح +1");
        } else {
            // لم يبدأ - رمادي فاتح
            holder.cardView.setCardBackgroundColor(context.getResources().getColor(R.color.azkar_not_started));
            holder.countText.setTextColor(context.getResources().getColor(R.color.islamic_gray));
            holder.incrementButton.setEnabled(true);
            holder.incrementButton.setText("ابدأ");
        }
    }

    // إظهار إشعار الإنجاز
    private void showCompletionNotification(AzkarItem azkar) {
        String title = azkar.getTitle().isEmpty() ? "ذكر" : azkar.getTitle();
        String message = "بارك الله فيك! أكملت " + title;
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }

    // تحديث قاعدة البيانات الجديدة
    private void updateDatabaseProgress(AzkarItem azkar) {
        executor.execute(() -> {
            try {
                long currentTime = System.currentTimeMillis();
                String todayDate = getTodayDateString();

                // نحتاج إلى استخدام ID رقمي، سنستخدم hash للـ azkarId
                int numericAzkarId = (azkarType + "_" + azkar.getAzkarId()).hashCode();
                if (numericAzkarId < 0) numericAzkarId = -numericAzkarId; // تأكد من أنه موجب

                // البحث عن التقدم الحالي
                AzkarProgressEntity progress = null;
                List<AzkarProgressEntity> progressList = database.azkarProgressDao().getProgressByAzkarId(numericAzkarId);
                if (progressList != null && !progressList.isEmpty()) {
                    // البحث عن تقدم اليوم
                    for (AzkarProgressEntity p : progressList) {
                        if (todayDate.equals(p.getDate())) {
                            progress = p;
                            break;
                        }
                    }
                }

                if (progress == null) {
                    progress = new AzkarProgressEntity(numericAzkarId, azkar.getCount(), todayDate);
                }

                progress.setCurrentCount(azkar.getCurrentCount());
                progress.setCompleted(azkar.isCompleted());
                progress.setUpdatedAt(currentTime);

                if (progress.getId() == 0) {
                    database.azkarProgressDao().insertProgress(progress);
                } else {
                    database.azkarProgressDao().updateProgress(progress);
                }

                // تحديث الإحصائيات
                updateStatistics(azkarType, currentTime);

                // فحص الإنجازات
                checkAchievements();

            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    // تحديث الإحصائيات
    private void updateStatistics(String category, long currentTime) {
        try {
            // إحصائيات يومية
            String todayDate = getTodayDateString();
            AzkarStatisticsEntity todayStats = database.azkarStatisticsDao().getStatisticsByDateAndCategory(todayDate, category);

            if (todayStats == null) {
                todayStats = new AzkarStatisticsEntity(todayDate, category);
                todayStats.setTotalAzkarCount(1);
                database.azkarStatisticsDao().insertStatistics(todayStats);
            } else {
                todayStats.setTotalAzkarCount(todayStats.getTotalAzkarCount() + 1);
                todayStats.setUpdatedAt(currentTime);
                database.azkarStatisticsDao().updateStatistics(todayStats);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // فحص الإنجازات الشاملة
    private void checkAchievements() {
        try {
            // الحصول على إجمالي الأذكار المكتملة
            int totalCompleted = database.azkarProgressDao().getTotalCompletedCount();

            // === إنجازات البداية ===
            if (totalCompleted >= 1) unlockAchievement("first_step");
            if (totalCompleted >= 5) unlockAchievement("five_dhikr");

            // === إنجازات يومية ===
            if (totalCompleted >= 10) unlockAchievement("daily_starter");
            if (totalCompleted >= 25) unlockAchievement("twenty_five");
            if (totalCompleted >= 50) unlockAchievement("half_hundred");
            if (totalCompleted >= 100) unlockAchievement("daily_champion");

            // === إنجازات المعالم ===
            if (totalCompleted >= 100) unlockAchievement("hundred_dhikr");
            if (totalCompleted >= 500) unlockAchievement("five_hundred");
            if (totalCompleted >= 1000) unlockAchievement("thousand_dhikr");
            if (totalCompleted >= 5000) unlockAchievement("five_thousand");
            if (totalCompleted >= 10000) unlockAchievement("ten_thousand");
            if (totalCompleted >= 25000) unlockAchievement("twenty_five_thousand");
            if (totalCompleted >= 50000) unlockAchievement("fifty_thousand");
            if (totalCompleted >= 100000) unlockAchievement("hundred_thousand");
            if (totalCompleted >= 1000000) unlockAchievement("million_dhikr");

            // فحص إنجازات الأقسام المحددة
            checkSectionSpecificAchievements();

            // فحص إنجازات التتابع (الأيام المتتالية)
            checkStreakAchievements();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // فحص إنجازات الأقسام المحددة
    private void checkSectionSpecificAchievements() {
        try {
            // فحص أذكار الصباح
            if (hasCompletedSection("morning")) {
                unlockAchievement("morning_light");
            }

            // فحص أذكار المساء
            if (hasCompletedSection("evening")) {
                unlockAchievement("evening_peace");
            }

            // فحص أذكار النوم
            if (hasCompletedSection("sleep")) {
                unlockAchievement("sleep_blessing");
            }

            // فحص إكمال الصباح والمساء في نفس اليوم
            if (hasCompletedSection("morning") && hasCompletedSection("evening")) {
                unlockAchievement("morning_evening");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // فحص إنجازات التتابع (الأيام المتتالية)
    private void checkStreakAchievements() {
        try {
            int currentStreak = getCurrentStreak();

            if (currentStreak >= 3) unlockAchievement("three_days");
            if (currentStreak >= 7) unlockAchievement("blessed_week");
            if (currentStreak >= 14) unlockAchievement("two_weeks");
            if (currentStreak >= 30) unlockAchievement("golden_month");
            if (currentStreak >= 40) unlockAchievement("forty_days");
            if (currentStreak >= 60) unlockAchievement("two_months");
            if (currentStreak >= 100) unlockAchievement("hundred_days");
            if (currentStreak >= 180) unlockAchievement("half_year");
            if (currentStreak >= 365) unlockAchievement("blessed_year");
            if (currentStreak >= 730) unlockAchievement("two_years");
            if (currentStreak >= 1095) unlockAchievement("three_years");
            if (currentStreak >= 1825) unlockAchievement("eternal_remembrance");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // فحص إكمال قسم معين
    private boolean hasCompletedSection(String section) {
        try {
            String today = getTodayDateString();
            return progressPrefs.getBoolean(section + "_completed_" + today, false);
        } catch (Exception e) {
            return false;
        }
    }

    // الحصول على عدد الأيام المتتالية
    private int getCurrentStreak() {
        try {
            return progressPrefs.getInt("current_streak", 0);
        } catch (Exception e) {
            return 0;
        }
    }

    // إنشاء وإلغاء قفل الإنجاز عند الحاجة
    private void createAndUnlockAchievement(String achievementId, String title, String description, String type, int targetValue) {
        try {
            // التحقق من وجود الإنجاز
            AchievementEntity existingAchievement = database.achievementDao().getAchievementByAchievementId(achievementId);

            if (existingAchievement == null) {
                // إنشاء الإنجاز إذا لم يكن موجوداً
                AchievementEntity newAchievement = new AchievementEntity(achievementId, title, description, type, targetValue, "general");
                newAchievement.setUnlocked(true);
                newAchievement.setUnlockDate(System.currentTimeMillis());
                newAchievement.setRewardPoints(getPointsForAchievementType(type));

                database.achievementDao().insertAchievement(newAchievement);

                // إظهار إشعار الإنجاز الجديد
                showAchievementUnlocked(title);

                // إرسال broadcast للتحديث الفوري
                sendAchievementUpdateBroadcast();
            } else if (!existingAchievement.isUnlocked()) {
                // إلغاء قفل الإنجاز إذا كان موجوداً لكن مقفل
                existingAchievement.setUnlocked(true);
                existingAchievement.setUnlockDate(System.currentTimeMillis());
                database.achievementDao().updateAchievement(existingAchievement);

                // إظهار إشعار الإنجاز
                showAchievementUnlocked(title);

                // إرسال broadcast للتحديث الفوري
                sendAchievementUpdateBroadcast();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // حساب النقاط بناءً على نوع الإنجاز
    private int getPointsForAchievementType(String type) {
        switch (type) {
            case "daily": return 10;
            case "weekly": return 50;
            case "monthly": return 200;
            case "milestone": return 500;
            default: return 10;
        }
    }

    // إلغاء قفل الإنجاز
    private void unlockAchievement(String achievementId) {
        try {
            AchievementEntity achievement = database.achievementDao().getAchievementByAchievementId(achievementId);
            if (achievement != null && !achievement.isUnlocked()) {
                achievement.unlock();
                database.achievementDao().updateAchievement(achievement);

                // إظهار إشعار الإنجاز
                showAchievementUnlocked(achievement.getTitle());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // إظهار إشعار إلغاء قفل الإنجاز
    private void showAchievementUnlocked(String achievementTitle) {
        // تشغيل على الـ UI thread
        if (context instanceof android.app.Activity) {
            ((android.app.Activity) context).runOnUiThread(() -> {
                Toast.makeText(context, "🏆 إنجاز جديد: " + achievementTitle, Toast.LENGTH_LONG).show();
            });
        }
    }

    // إرسال broadcast لتحديث صفحة الإنجازات فوراً
    private void sendAchievementUpdateBroadcast() {
        try {
            android.content.Intent intent = new android.content.Intent("ACHIEVEMENT_UPDATED");
            context.sendBroadcast(intent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // الحصول على تاريخ اليوم كنص
    private String getTodayDateString() {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
        return sdf.format(new java.util.Date());
    }

    static class AzkarViewHolder extends RecyclerView.ViewHolder {
        CardView cardView;
        TextView titleText;
        TextView contentText;
        TextView countText;
        TextView statusText;
        ProgressBar progressBar;
        Button resetButton;
        Button incrementButton;

        public AzkarViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.azkarCardView);
            titleText = itemView.findViewById(R.id.azkarTitle);
            contentText = itemView.findViewById(R.id.azkarContent);
            countText = itemView.findViewById(R.id.azkarCount);
            statusText = itemView.findViewById(R.id.azkarStatus);
            progressBar = itemView.findViewById(R.id.azkarProgressBar);
            resetButton = itemView.findViewById(R.id.resetButton);
            incrementButton = itemView.findViewById(R.id.incrementButton);
        }
    }
}

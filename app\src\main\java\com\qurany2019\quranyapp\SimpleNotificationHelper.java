package com.qurany2019.quranyapp;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

/**
 * مساعد بسيط للإشعارات
 */
public class SimpleNotificationHelper {

    private static final String CHANNEL_ID = "quran_simple_channel";
    private static final int NOTIFICATION_ID = 2001;

    private static NotificationManager notificationManager;
    private static Context context;

    /**
     * إعداد الإشعار
     */
    public static void setup(Context ctx) {
        context = ctx;
        notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        // إنشاء قناة الإشعار للأندرويد 8+
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "تشغيل القرآن",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("إشعارات تشغيل القرآن الكريم");
            channel.setShowBadge(false);
            notificationManager.createNotificationChannel(channel);
        }

        try {
            android.util.Log.d("NOTIFICATION", "✅ تم إعداد الإشعار البسيط");
        } catch (Exception e) {
            // تجاهل
        }
    }

    /**
     * إظهار إشعار بسيط
     */
    public static void showNotification(String title, String text, boolean isPlaying) {
        try {
            if (context == null || notificationManager == null) {
                DebugLogger.error("NOTIFICATION", "❌ الإشعار غير مُعد");
                return;
            }

            DebugLogger.log("NOTIFICATION", "🔔 إظهار إشعار: " + title + " - " + (isPlaying ? "يعمل" : "متوقف"));

            // إنشاء Intent للتطبيق الرئيسي
            Intent appIntent = new Intent(context, managerdb.class);
            appIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

            PendingIntent contentPendingIntent = PendingIntent.getActivity(
                context,
                0,
                appIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // إنشاء Intent للتحكم في التشغيل
            Intent controlIntent = new Intent(context, managerdb.class);
            controlIntent.setAction("TOGGLE_PLAYBACK");
            controlIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

            PendingIntent controlPendingIntent = PendingIntent.getActivity(
                context,
                1,
                controlIntent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // بناء الإشعار
            Notification.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                builder = new Notification.Builder(context, CHANNEL_ID);
            } else {
                builder = new Notification.Builder(context);
            }

            builder.setContentTitle(title)
                   .setContentText(text)
                   .setSmallIcon(17301540) // ic_media_play
                   .setContentIntent(contentPendingIntent)
                   .setOngoing(true)
                   .setAutoCancel(false);

            // إضافة زر التحكم
            String actionText = isPlaying ? "إيقاف" : "تشغيل";
            int actionIcon = isPlaying ? 17301539 : 17301540; // pause : play
            builder.addAction(actionIcon, actionText, controlPendingIntent);

            Notification notification = builder.build();
            notificationManager.notify(NOTIFICATION_ID, notification);

            DebugLogger.log("NOTIFICATION", "✅ تم إظهار الإشعار بنجاح");

        } catch (Exception e) {
            DebugLogger.error("NOTIFICATION", "❌ خطأ في إظهار الإشعار: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إخفاء الإشعار
     */
    public static void hideNotification() {
        try {
            if (notificationManager != null) {
                notificationManager.cancel(NOTIFICATION_ID);
                DebugLogger.log("NOTIFICATION", "✅ تم إخفاء الإشعار");
            }
        } catch (Exception e) {
            DebugLogger.error("NOTIFICATION", "❌ خطأ في إخفاء الإشعار: " + e.getMessage());
        }
    }
}

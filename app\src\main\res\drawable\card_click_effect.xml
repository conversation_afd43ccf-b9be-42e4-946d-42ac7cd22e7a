<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <solid android:color="@color/backgroundCard" />
            <stroke android:width="2dp" android:color="@color/islamicGold" />
        </shape>
    </item>

    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="16dp" />
            <solid android:color="@color/backgroundCard" />
            <stroke android:width="1dp" android:color="@color/dividerColor" />
        </shape>
    </item>
</selector>

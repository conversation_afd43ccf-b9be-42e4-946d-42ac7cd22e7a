<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/achievementCardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:layoutDirection="rtl">

        <!-- أيقونة الإنجاز -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <ImageView
                android:id="@+id/achievementIcon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_achievement"
                android:scaleType="centerCrop"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/islamic_gold"
                android:padding="8dp" />

            <!-- حالة القفل -->
            <ImageView
                android:id="@+id/lockIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_lock"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- محتوى الإنجاز -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- عنوان الإنجاز -->
            <TextView
                android:id="@+id/achievementTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="عنوان الإنجاز"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:fontFamily="@font/arabic_font"
                android:layout_marginBottom="4dp" />

            <!-- وصف الإنجاز -->
            <TextView
                android:id="@+id/achievementDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="وصف الإنجاز"
                android:textSize="14sp"
                android:textColor="@color/islamic_gray"
                android:fontFamily="@font/arabic_font"
                android:layout_marginBottom="8dp" />

            <!-- شريط التقدم -->
            <ProgressBar
                android:id="@+id/achievementProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="6dp"
                android:layout_marginBottom="4dp"
                android:progressTint="@color/islamic_gold"
                android:progressBackgroundTint="@color/islamic_light_gray" />

            <!-- نص التقدم -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/progressText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="0/10"
                    android:textSize="12sp"
                    android:textColor="@color/islamic_blue"
                    android:fontFamily="@font/arabic_font" />

                <TextView
                    android:id="@+id/rewardPoints"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="10 نقطة"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/islamic_gold"
                    android:fontFamily="@font/arabic_font" />

            </LinearLayout>

        </LinearLayout>

        <!-- حالة الإنجاز -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginStart="8dp">

            <!-- أيقونة الحالة -->
            <ImageView
                android:id="@+id/statusIcon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_check_circle"
                android:visibility="gone" />

            <!-- تاريخ الإنجاز -->
            <TextView
                android:id="@+id/unlockDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textSize="10sp"
                android:textColor="@color/islamic_gray"
                android:fontFamily="@font/arabic_font"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>

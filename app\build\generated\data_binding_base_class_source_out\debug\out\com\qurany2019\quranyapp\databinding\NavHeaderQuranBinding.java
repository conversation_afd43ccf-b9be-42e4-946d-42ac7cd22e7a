// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NavHeaderQuranBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final ImageView appIcon;

  @NonNull
  public final TextView appName;

  @NonNull
  public final ImageView backgroundImageView;

  @NonNull
  public final RelativeLayout iconContainer;

  @NonNull
  public final RelativeLayout navHeaderContainer;

  private NavHeaderQuranBinding(@NonNull RelativeLayout rootView, @NonNull ImageView appIcon,
      @NonNull TextView appName, @NonNull ImageView backgroundImageView,
      @NonNull RelativeLayout iconContainer, @NonNull RelativeLayout navHeaderContainer) {
    this.rootView = rootView;
    this.appIcon = appIcon;
    this.appName = appName;
    this.backgroundImageView = backgroundImageView;
    this.iconContainer = iconContainer;
    this.navHeaderContainer = navHeaderContainer;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NavHeaderQuranBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NavHeaderQuranBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.nav_header_quran, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NavHeaderQuranBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.appIcon;
      ImageView appIcon = ViewBindings.findChildViewById(rootView, id);
      if (appIcon == null) {
        break missingId;
      }

      id = R.id.appName;
      TextView appName = ViewBindings.findChildViewById(rootView, id);
      if (appName == null) {
        break missingId;
      }

      id = R.id.backgroundImageView;
      ImageView backgroundImageView = ViewBindings.findChildViewById(rootView, id);
      if (backgroundImageView == null) {
        break missingId;
      }

      id = R.id.iconContainer;
      RelativeLayout iconContainer = ViewBindings.findChildViewById(rootView, id);
      if (iconContainer == null) {
        break missingId;
      }

      RelativeLayout navHeaderContainer = (RelativeLayout) rootView;

      return new NavHeaderQuranBinding((RelativeLayout) rootView, appIcon, appName,
          backgroundImageView, iconContainer, navHeaderContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

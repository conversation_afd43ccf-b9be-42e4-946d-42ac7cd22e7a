package com.qurany2019.quranyapp.utils;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

public class PermissionHelper {

    public static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1001;
    public static final int LOCATION_PERMISSION_REQUEST_CODE = 1002;
    
    // فحص إذن الإشعارات
    public static boolean hasNotificationPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
                   == PackageManager.PERMISSION_GRANTED;
        }
        return true; // الإصدارات الأقدم لا تحتاج إذن صريح
    }
    
    // طلب إذن الإشعارات
    public static void requestNotificationPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (!hasNotificationPermission(activity)) {
                ActivityCompat.requestPermissions(
                    activity,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                );
            }
        }
    }
    
    // فحص إذن الموقع
    public static boolean hasLocationPermission(Context context) {
        return ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) 
               == PackageManager.PERMISSION_GRANTED ||
               ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) 
               == PackageManager.PERMISSION_GRANTED;
    }
    
    // طلب إذن الموقع
    public static void requestLocationPermission(Activity activity, int requestCode) {
        ActivityCompat.requestPermissions(
            activity,
            new String[]{
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            },
            requestCode
        );
    }

    // طلب إذن الموقع مع رمز الطلب الافتراضي
    public static void requestLocationPermission(Activity activity) {
        requestLocationPermission(activity, LOCATION_PERMISSION_REQUEST_CODE);
    }
    
    // فحص إذن التخزين
    public static boolean hasStoragePermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return true; // Android 13+ لا يحتاج إذن تخزين للملفات الخاصة
        }
        return ContextCompat.checkSelfPermission(context, Manifest.permission.WRITE_EXTERNAL_STORAGE) 
               == PackageManager.PERMISSION_GRANTED;
    }
    
    // طلب إذن التخزين
    public static void requestStoragePermission(Activity activity, int requestCode) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(
                activity,
                new String[]{
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                },
                requestCode
            );
        }
    }
    
    // معالجة نتيجة طلب الأذونات
    public static boolean isPermissionGranted(int[] grantResults) {
        return grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED;
    }
    
    // فحص جميع الأذونات المطلوبة
    public static boolean hasAllRequiredPermissions(Context context) {
        return hasNotificationPermission(context) && 
               hasLocationPermission(context) && 
               hasStoragePermission(context);
    }
    
    // طلب جميع الأذونات المطلوبة
    public static void requestAllRequiredPermissions(Activity activity) {
        // طلب إذن الإشعارات أولاً
        if (!hasNotificationPermission(activity)) {
            requestNotificationPermission(activity);
            return;
        }
        
        // ثم إذن الموقع
        if (!hasLocationPermission(activity)) {
            requestLocationPermission(activity, 1002);
            return;
        }
        
        // وأخيراً إذن التخزين
        if (!hasStoragePermission(activity)) {
            requestStoragePermission(activity, 1003);
        }
    }
}

<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_settings" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_settings_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="517" endOffset="51"/></Target><Target id="@+id/app_bar" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="22" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="66"/></Target><Target id="@+id/darkModeIcon" view="ImageView"><Expressions/><location startLine="103" startOffset="32" endLine="109" endOffset="66"/></Target><Target id="@+id/darkModeTitle" view="TextView"><Expressions/><location startLine="120" startOffset="32" endLine="128" endOffset="69"/></Target><Target id="@+id/darkModeDesc" view="TextView"><Expressions/><location startLine="130" startOffset="32" endLine="138" endOffset="68"/></Target><Target id="@+id/switchDarkMode" view="Switch"><Expressions/><location startLine="150" startOffset="32" endLine="157" endOffset="81"/></Target><Target id="@+id/arabicLanguageOption" view="LinearLayout"><Expressions/><location startLine="196" startOffset="20" endLine="245" endOffset="34"/></Target><Target id="@+id/arabicSelectedIcon" view="ImageView"><Expressions/><location startLine="237" startOffset="24" endLine="243" endOffset="58"/></Target><Target id="@+id/englishLanguageOption" view="LinearLayout"><Expressions/><location startLine="248" startOffset="20" endLine="296" endOffset="34"/></Target><Target id="@+id/englishSelectedIcon" view="ImageView"><Expressions/><location startLine="288" startOffset="24" endLine="294" endOffset="55"/></Target><Target id="@+id/switchNotifications" view="Switch"><Expressions/><location startLine="356" startOffset="24" endLine="360" endOffset="52"/></Target><Target id="@+id/switchAutoPlay" view="Switch"><Expressions/><location startLine="422" startOffset="24" endLine="426" endOffset="53"/></Target><Target id="@+id/tvFontSize" view="TextView"><Expressions/><location startLine="488" startOffset="24" endLine="497" endOffset="51"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="509" startOffset="4" endLine="515" endOffset="57"/></Target></Targets></Layout>
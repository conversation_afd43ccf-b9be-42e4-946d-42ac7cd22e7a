# دليل مواقع حفظ السور المحملة

## الموقع الحالي في الكود

### المسار المستخدم:
```java
File audioDir = new File(getExternalFilesDir(null), "قرآني/" + RecitesName);
File outputFile = new File(audioDir, RecitesAYA + ".mp3");
```

### المسار الكامل:
```
/storage/emulated/0/Android/data/com.qurany2019.quranyapp/files/قرآني/[اسم القارئ]/[اسم السورة].mp3
```

## أمثلة عملية:

### مثال 1: سورة الفاتحة - عبد الباسط عبد الصمد
```
/storage/emulated/0/Android/data/com.qurany2019.quranyapp/files/قرآني/عبد الباسط عبد الصمد/الفاتحة.mp3
```

### مثال 2: سورة البقرة - مشاري العفاسي
```
/storage/emulated/0/Android/data/com.qurany2019.quranyapp/files/قرآني/مشاري العفاسي/البقرة.mp3
```

### مثال 3: سورة آل عمران - ماهر المعيقلي
```
/storage/emulated/0/Android/data/com.qurany2019.quranyapp/files/قرآني/ماهر المعيقلي/آل عمران.mp3
```

## خصائص هذا الموقع:

### ✅ المزايا:
1. **آمن**: لا يحتاج أذونات خاصة في Android 10+
2. **منظم**: كل قارئ له مجلد منفصل
3. **محمي**: الملفات خاصة بالتطبيق
4. **تلقائي**: يتم حذفها عند إلغاء تثبيت التطبيق
5. **سريع**: الوصول مباشر بدون أذونات

### ⚠️ العيوب:
1. **غير مرئية**: لا تظهر في مدير الملفات العادي
2. **مؤقتة**: تُحذف مع إلغاء التثبيت
3. **غير قابلة للمشاركة**: لا يمكن الوصول إليها من تطبيقات أخرى

## كيفية الوصول للملفات:

### 1. من داخل التطبيق:
```java
// فحص وجود الملف
File audioDir = new File(getExternalFilesDir(null), "قرآني/" + reciterName);
File audioFile = new File(audioDir, suraName + ".mp3");
if (audioFile.exists()) {
    // الملف موجود
    String filePath = audioFile.getAbsolutePath();
}
```

### 2. من مدير الملفات (للمطورين):
- افتح مدير الملفات
- اذهب إلى: `Android/data/com.qurany2019.quranyapp/files/قرآني/`
- ستجد مجلدات القراء والسور

### 3. باستخدام ADB (للمطورين):
```bash
adb shell
cd /storage/emulated/0/Android/data/com.qurany2019.quranyapp/files/قرآني/
ls -la
```

## بدائل أخرى لمواقع الحفظ:

### 1. التخزين العام (يحتاج أذونات):
```java
// Android 9 وأقل
File publicDir = new File(Environment.getExternalStorageDirectory(), "قرآني/" + reciterName);
```
**المسار:** `/storage/emulated/0/قرآني/[اسم القارئ]/[اسم السورة].mp3`

### 2. مجلد الموسيقى (يحتاج أذونات):
```java
File musicDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC), "قرآني/" + reciterName);
```
**المسار:** `/storage/emulated/0/Music/قرآني/[اسم القارئ]/[اسم السورة].mp3`

### 3. مجلد التنزيلات (يحتاج أذونات):
```java
File downloadDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "قرآني/" + reciterName);
```
**المسار:** `/storage/emulated/0/Download/قرآني/[اسم القارئ]/[اسم السورة].mp3`

## الأذونات المطلوبة حسب الموقع:

### للموقع الحالي (getExternalFilesDir):
```xml
<!-- لا يحتاج أذونات خاصة في Android 10+ -->
<!-- للإصدارات الأقدم فقط -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="28" />
```

### للمواقع العامة:
```xml
<!-- للإصدارات القديمة -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
    android:maxSdkVersion="32" />

<!-- للإصدارات الحديثة Android 13+ -->
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
```

## التوصية:

**الموقع الحالي مثالي** لأنه:
- آمن ولا يحتاج أذونات معقدة
- منظم ومرتب
- سريع في الوصول
- متوافق مع جميع إصدارات Android

إذا كنت تريد أن تكون الملفات مرئية للمستخدم، يمكن إضافة خيار في الإعدادات للاختيار بين:
1. **التخزين الخاص** (الحالي) - آمن وسريع
2. **التخزين العام** - مرئي ولكن يحتاج أذونات

## كيفية فحص المساحة المستخدمة:

```java
public long getUsedStorage() {
    File audioDir = new File(getExternalFilesDir(null), "قرآني");
    return getFolderSize(audioDir);
}

private long getFolderSize(File folder) {
    long size = 0;
    if (folder.exists()) {
        File[] files = folder.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    size += getFolderSize(file);
                } else {
                    size += file.length();
                }
            }
        }
    }
    return size;
}
```

## كيفية حذف السور:

```java
public boolean deleteSura(String reciterName, String suraName) {
    File audioDir = new File(getExternalFilesDir(null), "قرآني/" + reciterName);
    File audioFile = new File(audioDir, suraName + ".mp3");
    return audioFile.exists() && audioFile.delete();
}
```

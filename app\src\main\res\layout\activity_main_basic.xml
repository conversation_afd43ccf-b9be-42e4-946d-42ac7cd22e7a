<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backgroundPrimary"
    tools:context=".MainActivity">

    <!-- الخلفية الرئيسية الجديدة -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/backgroundPrimary">

        <!-- المحتوى الثابت بدون تمرير -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/bottomNavigationContainer"
            android:orientation="vertical"
            android:paddingBottom="8dp">

                <!-- الهيدر العلوي -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="12dp"
                    android:background="@color/backgroundPrimary">

                    <!-- العنوان الرئيسي -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="@string/main_page_title"
                        android:textSize="16sp"
                        android:textColor="@color/textPrimary"
                        android:textStyle="bold"
                        android:fontFamily="@font/noto" />

                    <!-- زر الوضع الليلي الجميل والبسيط -->
                    <androidx.cardview.widget.CardView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        app:cardCornerRadius="20dp"
                        app:cardElevation="6dp"
                        app:cardBackgroundColor="@color/nightModeCardBg">

                        <LinearLayout
                            android:id="@+id/nightModeToggle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:padding="8dp"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"
                            android:background="?android:attr/selectableItemBackground">

                            <ImageView
                                android:id="@+id/nightModeIcon"
                                android:layout_width="18dp"
                                android:layout_height="18dp"
                                android:src="@drawable/ic_moon"
                                android:tint="@color/nightModeIconTint"
                                android:layout_marginEnd="6dp" />

                            <TextView
                                android:id="@+id/nightModeText"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/night_mode_dark"
                                android:textSize="11sp"
                                android:textColor="@color/nightModeTextColor"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </RelativeLayout>

                <!-- بطاقة السبت -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="150dp"
                    android:layout_margin="12dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="8dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/saturday_card_background"
                        android:padding="16dp">

                        <!-- نص السبت -->
                        <TextView
                            android:id="@+id/dayText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentTop="true"
                            android:layout_centerHorizontal="true"
                            android:text="@string/saturday"
                            android:textSize="20sp"
                            android:textColor="@color/whiteText"
                            android:textStyle="bold"
                            android:fontFamily="@font/noto" />

                        <!-- الوقت الحالي -->
                        <TextView
                            android:id="@+id/currentTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:text="@string/current_time_display"
                            android:textSize="28sp"
                            android:textColor="@color/whiteText"
                            android:textStyle="bold"
                            android:fontFamily="@font/noto" />

                        <!-- آخر قراءة -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:layout_alignParentStart="true"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/last_reading"
                                android:textSize="12sp"
                                android:textColor="@color/whiteText"
                                android:fontFamily="@font/noto" />

                            <TextView
                                android:id="@+id/lastReadSurah"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/surah_fatiha"
                                android:textSize="14sp"
                                android:textColor="@color/whiteText"
                                android:textStyle="bold"
                                android:fontFamily="@font/surahname" />

                        </LinearLayout>



                    </RelativeLayout>

                </androidx.cardview.widget.CardView>

                <!-- أوقات الصلاة -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="12dp"
                    android:layout_marginBottom="12dp"
                    android:orientation="horizontal"
                    android:weightSum="5">

                    <!-- الفجر -->
                    <LinearLayout
                        android:id="@+id/fajrLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/fajrTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/fajr_time"
                            android:textSize="11sp"
                            android:textColor="@color/textPrimary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/fajr"
                            android:textSize="10sp"
                            android:textColor="@color/textPrimary"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- الظهر -->
                    <LinearLayout
                        android:id="@+id/dhuhrLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/dhuhrTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/dhuhr_time"
                            android:textSize="11sp"
                            android:textColor="@color/textPrimary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/dhuhr"
                            android:textSize="10sp"
                            android:textColor="@color/textPrimary"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- العصر -->
                    <LinearLayout
                        android:id="@+id/asrLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/asrTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/asr_time"
                            android:textSize="11sp"
                            android:textColor="@color/textPrimary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/asr"
                            android:textSize="10sp"
                            android:textColor="@color/textPrimary"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- المغرب -->
                    <LinearLayout
                        android:id="@+id/maghribLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/maghribTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/maghrib_time"
                            android:textSize="11sp"
                            android:textColor="@color/textPrimary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/maghrib"
                            android:textSize="10sp"
                            android:textColor="@color/textPrimary"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                    <!-- العشاء -->
                    <LinearLayout
                        android:id="@+id/ishaLayout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:background="@drawable/prayer_time_card_bg"
                        android:orientation="vertical"
                        android:padding="10dp"
                        android:gravity="center">

                        <TextView
                            android:id="@+id/ishaTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/isha_time"
                            android:textSize="11sp"
                            android:textColor="@color/textPrimary"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/isha"
                            android:textSize="10sp"
                            android:textColor="@color/textPrimary"
                            android:fontFamily="@font/noto" />

                    </LinearLayout>

                </LinearLayout>

                <!-- الأقسام الرئيسية -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="12dp"
                    android:orientation="vertical">

                    <!-- الصف الأول: القرآن الكريم وقراء القرآن -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:orientation="horizontal">

                        <!-- القرآن الكريم - تصميم مميز -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="120dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            app:cardCornerRadius="25dp"
                            app:cardElevation="20dp"
                            app:cardBackgroundColor="@android:color/transparent">

                            <RelativeLayout
                                android:id="@+id/quranSection"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/gradient_quran_special"
                                android:clickable="true"
                                android:focusable="true"
                                android:foreground="@drawable/main_card_click_effect">

                                <!-- خلفية زخرفية إسلامية -->
                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/islamic_pattern_overlay"
                                    android:alpha="0.08" />

                                <!-- دائرة ذهبية مضيئة كبيرة -->
                                <View
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="8dp"
                                    android:background="@drawable/golden_glow_circle"
                                    android:elevation="5dp" />

                                <!-- هلال إسلامي زخرفي -->
                                <ImageView
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_marginTop="15dp"
                                    android:layout_marginLeft="18dp"
                                    android:src="@drawable/ic_crescent"
                                    android:tint="#FFD700"
                                    android:elevation="10dp" />

                                <!-- نجمة إسلامية -->
                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_alignParentRight="true"
                                    android:layout_marginTop="22dp"
                                    android:layout_marginRight="20dp"
                                    android:src="@drawable/ic_islamic_star"
                                    android:tint="#FFD700"
                                    android:elevation="10dp" />

                                <!-- أيقونة المصحف المميزة -->
                                <ImageView
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="20dp"
                                    android:src="@drawable/qurancover"
                                    android:elevation="12dp"
                                    android:scaleType="centerInside" />

                                <!-- شعاع ضوئي خلف النص -->
                                <View
                                    android:layout_width="120dp"
                                    android:layout_height="24dp"
                                    android:layout_centerHorizontal="true"
                                    android:layout_alignParentBottom="true"
                                    android:layout_marginBottom="16dp"
                                    android:background="@drawable/text_glow_bg"
                                    android:elevation="8dp" />

                                <!-- النص المميز -->
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerHorizontal="true"
                                    android:layout_alignParentBottom="true"
                                    android:layout_marginBottom="18dp"
                                    android:text="@string/holy_quran"
                                    android:textColor="#FFFFFF"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:shadowColor="#000000"
                                    android:shadowDx="3"
                                    android:shadowDy="3"
                                    android:shadowRadius="8"
                                    android:elevation="15dp"
                                    android:fontFamily="@font/noto" />

                            </RelativeLayout>

                        </androidx.cardview.widget.CardView>

                        <!-- الاستماع للقرآن الكريم - تصميم مميز -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="0dp"
                            android:layout_height="120dp"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            app:cardCornerRadius="25dp"
                            app:cardElevation="20dp"
                            app:cardBackgroundColor="@android:color/transparent">

                            <RelativeLayout
                                android:id="@+id/recitersSection"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@drawable/gradient_listening_special"
                                android:clickable="true"
                                android:focusable="true"
                                android:foreground="@drawable/main_card_click_effect">

                                <!-- خلفية زخرفية موسيقية -->
                                <View
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:background="@drawable/gradient_background"
                                    android:alpha="0.08" />

                                <!-- دائرة بنفسجية مضيئة كبيرة -->
                                <View
                                    android:layout_width="75dp"
                                    android:layout_height="75dp"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="8dp"
                                    android:background="@drawable/purple_glow_circle"
                                    android:elevation="5dp" />

                                <!-- نوتة موسيقية زخرفية -->
                                <ImageView
                                    android:layout_width="16dp"
                                    android:layout_height="16dp"
                                    android:layout_marginTop="18dp"
                                    android:layout_marginLeft="20dp"
                                    android:src="@drawable/ic_music_note"
                                    android:tint="#E1BEE7"
                                    android:elevation="10dp" />

                                <!-- موجة صوتية -->
                                <ImageView
                                    android:layout_width="18dp"
                                    android:layout_height="18dp"
                                    android:layout_alignParentRight="true"
                                    android:layout_marginTop="15dp"
                                    android:layout_marginRight="18dp"
                                    android:src="@drawable/ic_sound_wave"
                                    android:tint="#E1BEE7"
                                    android:elevation="10dp" />

                                <!-- أيقونة السماعات المميزة -->
                                <ImageView
                                    android:layout_width="50dp"
                                    android:layout_height="50dp"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="20dp"
                                    android:src="@drawable/nav_listen"
                                    android:tint="#FFFFFF"
                                    android:elevation="12dp" />

                                <!-- شعاع ضوئي خلف النص -->
                                <View
                                    android:layout_width="110dp"
                                    android:layout_height="24dp"
                                    android:layout_centerHorizontal="true"
                                    android:layout_alignParentBottom="true"
                                    android:layout_marginBottom="16dp"
                                    android:background="@drawable/text_glow_bg"
                                    android:elevation="8dp" />

                                <!-- النص المميز -->
                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_centerHorizontal="true"
                                    android:layout_alignParentBottom="true"
                                    android:layout_marginBottom="18dp"
                                    android:text="@string/listen_title"
                                    android:textColor="#FFFFFF"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    android:shadowColor="#000000"
                                    android:shadowDx="3"
                                    android:shadowDy="3"
                                    android:shadowRadius="8"
                                    android:elevation="15dp"
                                    android:fontFamily="@font/noto" />

                            </RelativeLayout>

                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <!-- الصف الثاني: أذكار وأدعية -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal">

                        <!-- أذكار -->
                        <LinearLayout
                            android:id="@+id/azkarSection"
                            android:layout_width="0dp"
                            android:layout_height="70dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            android:background="@drawable/card_click_effect"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_azkar"
                                android:layout_marginBottom="6dp"
                                android:tint="@color/islamicGoldDark" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/supplications_title"
                                android:textSize="12sp"
                                android:textColor="@color/textPrimary"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </LinearLayout>

                        <!-- السبحة -->
                        <LinearLayout
                            android:id="@+id/tasbihSection"
                            android:layout_width="0dp"
                            android:layout_height="70dp"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            android:background="@drawable/card_click_effect"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_tasbih"
                                android:layout_marginBottom="6dp"
                                android:tint="@color/islamicGoldDark" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/tasbih_title"
                                android:textSize="12sp"
                                android:textColor="@color/textPrimary"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- الصف الثالث: مواقيت الصلاة -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- مواقيت الصلاة -->
                        <LinearLayout
                            android:id="@+id/prayerTimesSection"
                            android:layout_width="0dp"
                            android:layout_height="70dp"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            android:background="@drawable/card_click_effect"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?android:attr/selectableItemBackground">

                            <ImageView
                                android:layout_width="28dp"
                                android:layout_height="28dp"
                                android:src="@drawable/ic_prayer_times"
                                android:layout_marginBottom="6dp"
                                android:tint="@color/islamicGoldDark" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/prayer_times_title"
                                android:textSize="12sp"
                                android:textColor="@color/textPrimary"
                                android:textStyle="bold"
                                android:fontFamily="@font/noto" />

                        </LinearLayout>



                    </LinearLayout>

                </LinearLayout>





                <!-- إعلان AdMob -->
                <com.google.android.gms.ads.AdView
                    android:id="@+id/adView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_margin="8dp"
                    app:adSize="BANNER"
                    app:adUnitId="ca-app-pub-3940256099942544/6300978111" />

        </LinearLayout>

        <!-- شريط التنقل السفلي العصري المتلاصق -->
        <RelativeLayout
            android:id="@+id/bottomNavigationContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@android:color/transparent"
            android:paddingStart="0dp"
            android:paddingEnd="0dp"
            android:paddingBottom="0dp"
            android:paddingTop="0dp">

            <!-- الخلفية العصرية المحدثة للشريط -->
            <LinearLayout
                android:id="@+id/bottomNavigation"
                android:layout_width="match_parent"
                android:layout_height="85dp"
                android:background="@drawable/modern_bottom_nav_bg"
                android:elevation="16dp"
                android:orientation="horizontal"
                android:gravity="center"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <!-- أيقونة الرئيسية العصرية -->
                <LinearLayout
                    android:id="@+id/homeNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="6dp">

                    <!-- حاوي الأيقونة مع خلفية عصرية -->
                    <RelativeLayout
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/modern_nav_icon_active_bg">

                        <ImageView
                            android:id="@+id/homeIcon"
                            android:layout_width="22dp"
                            android:layout_height="22dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/nav_home"
                            android:tint="@android:color/white"
                            android:scaleType="centerInside"
                            android:elevation="2dp" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nav_bottom_home"
                        android:textSize="9sp"
                        android:textColor="@color/islamicGreenMedium"
                        android:fontFamily="@font/noto"
                        android:textStyle="bold"
                        android:layout_marginTop="2dp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- أيقونة القرآن العصرية -->
                <LinearLayout
                    android:id="@+id/quranNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="4dp">

                    <!-- حاوي الأيقونة مع خلفية عصرية -->
                    <RelativeLayout
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/modern_nav_icon_bg">

                        <ImageView
                            android:id="@+id/quranIcon"
                            android:layout_width="22dp"
                            android:layout_height="22dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/nav_quran"
                            android:tint="@color/islamicGreenMedium"
                            android:scaleType="centerInside"
                            android:elevation="2dp" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nav_bottom_quran"
                        android:textSize="9sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- أيقونة الاستماع العصرية -->
                <LinearLayout
                    android:id="@+id/listenNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="6dp">

                    <!-- حاوي الأيقونة مع خلفية عصرية -->
                    <RelativeLayout
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:background="@drawable/modern_nav_icon_bg">

                        <ImageView
                            android:id="@+id/listenIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/nav_listen"
                            android:tint="@color/islamicGreenMedium"
                            android:scaleType="centerInside"
                            android:elevation="2dp" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nav_bottom_listen"
                        android:textSize="9sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- أيقونة الأذكار العصرية -->
                <LinearLayout
                    android:id="@+id/azkarNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="6dp">

                    <!-- حاوي الأيقونة مع خلفية عصرية -->
                    <RelativeLayout
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:background="@drawable/modern_nav_icon_bg">

                        <ImageView
                            android:id="@+id/azkarIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/nav_azkar"
                            android:tint="@color/islamicGreenMedium"
                            android:scaleType="centerInside"
                            android:elevation="2dp" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nav_bottom_azkar"
                        android:textSize="9sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp"
                        android:gravity="center" />

                </LinearLayout>

                <!-- أيقونة المزيد العصرية -->
                <LinearLayout
                    android:id="@+id/moreNavItem"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="@drawable/nav_item_selector"
                    android:padding="6dp">

                    <!-- حاوي الأيقونة مع خلفية عصرية -->
                    <RelativeLayout
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:background="@drawable/modern_nav_icon_bg">

                        <ImageView
                            android:id="@+id/moreIcon"
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_centerInParent="true"
                            android:src="@drawable/nav_more"
                            android:tint="@color/islamicGreenMedium"
                            android:scaleType="centerInside"
                            android:elevation="2dp" />

                    </RelativeLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/nav_bottom_more"
                        android:textSize="9sp"
                        android:textColor="@color/textSecondary"
                        android:fontFamily="@font/noto"
                        android:layout_marginTop="2dp"
                        android:gravity="center" />

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>

</RelativeLayout>

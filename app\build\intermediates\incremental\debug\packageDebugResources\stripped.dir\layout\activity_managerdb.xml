<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_night_background_islamic"
    android:clipToPadding="false"
    android:fitsSystemWindows="true"
    android:paddingBottom="0dp"
    tools:context=".managerdb">

    <!-- خلفية متدرجة مع هلال -->
    <ImageView
        android:id="@+id/backgroundCrescent"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="60dp"
        android:layout_marginEnd="40dp"
        android:src="@drawable/ic_crescent"
        android:alpha="0.8"
        android:tint="#FFB6C1" />

    <!-- نمط إسلامي زخرفي -->
    <ImageView
        android:id="@+id/islamicPattern1"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_marginTop="100dp"
        android:layout_marginStart="20dp"
        android:src="@drawable/islamic_pattern_overlay"
        android:alpha="0.3"
        android:tint="#FF69B4" />

    <!-- نمط إسلامي زخرفي آخر -->
    <ImageView
        android:id="@+id/islamicPattern2"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:layout_marginBottom="150dp"
        android:layout_marginEnd="30dp"
        android:src="@drawable/islamic_pattern_overlay"
        android:alpha="0.2"
        android:rotation="45"
        android:tint="#FFC0CB" />

    <!-- نجوم ذهبية متلألئة -->
    <ImageView
        android:id="@+id/star1"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_marginTop="150dp"
        android:layout_marginStart="50dp"
        android:src="@drawable/ic_star"
        android:alpha="0.6"
        android:tint="#FFB6C1" />

    <ImageView
        android:id="@+id/star2"
        android:layout_width="15dp"
        android:layout_height="15dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="200dp"
        android:layout_marginEnd="80dp"
        android:src="@drawable/ic_star"
        android:alpha="0.5"
        android:tint="#FF69B4" />

    <ImageView
        android:id="@+id/star3"
        android:layout_width="18dp"
        android:layout_height="18dp"
        android:layout_alignParentBottom="true"
        android:layout_alignParentStart="true"
        android:layout_marginBottom="200dp"
        android:layout_marginStart="80dp"
        android:src="@drawable/ic_star"
        android:alpha="0.4"
        android:tint="#FFC0CB" />

    <!-- شريط علوي مع العنوان -->
    <LinearLayout
        android:id="@+id/player_header_bg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:orientation="vertical"
        android:padding="20dp"
        android:gravity="center">

        <TextView
            android:id="@+id/songTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="الفاتحة"
            android:textColor="#FFB6C1"
            android:textSize="28sp"
            android:textStyle="bold"
            android:fontFamily="@font/arabic_font"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:shadowColor="#4A148C"
            android:shadowDx="3"
            android:shadowDy="3"
            android:shadowRadius="8"
            android:elevation="10dp"
            android:letterSpacing="0.05" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/holy_quran_title"
            android:textColor="#FFC0CB"
            android:textSize="18sp"
            android:textStyle="bold"
            android:fontFamily="@font/arabic_font"
            android:layout_marginTop="5dp"
            android:gravity="center"
            android:shadowColor="#4A148C"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="6"
            android:elevation="8dp"
            android:letterSpacing="0.08" />

    </LinearLayout>

    <!-- منطقة الصورة الرئيسية مع الانيميشن -->
    <FrameLayout
        android:id="@+id/songThumbnail"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_below="@id/player_header_bg"
        android:layout_above="@id/progressContainer"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="15dp"
        android:layout_marginHorizontal="30dp"
        android:gravity="center"
        android:elevation="10dp">



        <!-- الخلفية الدائرية الجميلة -->
        <ImageView
            android:id="@+id/backgroundCircle"
            android:layout_width="280dp"
            android:layout_height="280dp"
            android:layout_gravity="center"
            android:src="@drawable/beautiful_circle_background"
            android:scaleType="centerInside"
            android:elevation="1dp"
            android:alpha="0.9" />



        <!-- تأثير الإضاءة الخارجية -->
        <ImageView
            android:id="@+id/outerGlow"
            android:layout_width="300dp"
            android:layout_height="300dp"
            android:layout_gravity="center"
            android:src="@drawable/outer_glow_effect"
            android:scaleType="centerInside"
            android:elevation="2dp"
            android:alpha="0.4" />

        <!-- تأثير الإضاءة المتوسطة -->
        <ImageView
            android:id="@+id/middleGlow"
            android:layout_width="260dp"
            android:layout_height="260dp"
            android:layout_gravity="center"
            android:src="@drawable/middle_glow_effect"
            android:scaleType="centerInside"
            android:elevation="3dp"
            android:alpha="0.5" />

        <!-- تأثير الإضاءة الداخلية -->
        <ImageView
            android:id="@+id/innerGlow"
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:src="@drawable/inner_glow_effect"
            android:scaleType="centerInside"
            android:elevation="4dp"
            android:alpha="0.6" />

        <!-- الأشكال الزخرفية -->
        <ImageView
            android:id="@+id/decorativeStars"
            android:layout_width="280dp"
            android:layout_height="280dp"
            android:layout_gravity="center"
            android:src="@drawable/decorative_stars"
            android:scaleType="centerInside"
            android:elevation="5dp"
            android:alpha="0.3" />

        <!-- صورة القرآن الجميلة -->
        <ImageView
            android:id="@+id/quranIcon"
            android:layout_width="170dp"
            android:layout_height="170dp"
            android:layout_gravity="center"
            android:src="@drawable/quraomn"
            android:scaleType="centerInside"
            android:elevation="20dp"
            android:alpha="0.95" />

    </FrameLayout>

    <!-- منطقة شريط التقدم والوقت -->
    <LinearLayout
        android:id="@+id/progressContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/controlsContainer"
        android:layout_marginBottom="15dp"
        android:orientation="vertical"
        android:paddingHorizontal="30dp">

        <!-- شريط التقدم -->
        <SeekBar
            android:id="@+id/songProgressBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="12dp"
            android:progressDrawable="@drawable/custom_seekbar_progress"
            android:thumb="@drawable/custom_seekbar_thumb"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:splitTrack="false" />

        <!-- عرض الوقت -->
        <LinearLayout
            android:id="@+id/timerDisplay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/songCurrentDurationLabel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="00:00"
                android:textColor="#FFB6C1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="start" />

            <TextView
                android:id="@+id/songTotalDurationLabel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="00:00"
                android:textColor="#FFB6C1"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="end" />

        </LinearLayout>

    </LinearLayout>

    <!-- منطقة أزرار التحكم -->
    <LinearLayout
        android:id="@+id/controlsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/extraControlsContainer"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:paddingHorizontal="20dp">

        <!-- زر السابق -->
        <ImageButton
            android:id="@+id/btnPrevious"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/control_button_background"
            android:src="@drawable/ic_skip_previous_white"
            android:scaleType="centerInside"
            android:elevation="4dp" />

        <!-- زر الترجيع -->
        <ImageButton
            android:id="@+id/btnBackward"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/control_button_small_background"
            android:src="@drawable/ic_replay_10_white"
            android:scaleType="centerInside"
            android:elevation="4dp" />

        <!-- زر التشغيل الرئيسي -->
        <ImageButton
            android:id="@+id/btnPlay"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginHorizontal="10dp"
            android:background="@drawable/play_button_background"
            android:src="@drawable/ic_play_arrow_white"
            android:scaleType="centerInside"
            android:elevation="8dp" />

        <!-- زر التقديم -->
        <ImageButton
            android:id="@+id/btnForward"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/control_button_small_background"
            android:src="@drawable/ic_forward_10_white"
            android:scaleType="centerInside"
            android:elevation="4dp" />

        <!-- زر التالي -->
        <ImageButton
            android:id="@+id/btnNext"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/control_button_background"
            android:src="@drawable/ic_skip_next_white"
            android:scaleType="centerInside"
            android:elevation="4dp" />

    </LinearLayout>

    <!-- أزرار إضافية (تكرار وخلط) -->
    <LinearLayout
        android:id="@+id/extraControlsContainer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/layoutads"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:orientation="horizontal"
        android:gravity="center"
        android:paddingBottom="5dp">

        <ImageButton
            android:id="@+id/btnRepeat"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/extra_control_background"
            android:src="@drawable/ic_repeat_white"
            android:scaleType="centerInside"
            android:alpha="0.8"
            android:elevation="3dp" />

        <ImageButton
            android:id="@+id/btnShuffle"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_marginStart="20dp"
            android:background="@drawable/extra_control_background"
            android:src="@drawable/ic_shuffle_white"
            android:scaleType="centerInside"
            android:alpha="0.8"
            android:elevation="3dp" />

    </LinearLayout>

    <!-- منطقة الإعلانات -->
    <LinearLayout
        android:id="@+id/layoutads"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/adView"
        android:orientation="vertical"
        android:visibility="gone">

    </LinearLayout>

    <!-- الإعلان -->
    <com.google.android.gms.ads.AdView
        xmlns:ads="http://schemas.android.com/apk/res-auto"
        android:id="@+id/adView"
        android:layout_width="320dp"
        android:layout_height="50dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="0dp"
        ads:adSize="BANNER"
        ads:adUnitId="@string/banner_ad_id" />

</RelativeLayout>

package com.qurany2019.quranyapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.GravityCompat;
import androidx.drawerlayout.widget.DrawerLayout;
import com.google.android.material.navigation.NavigationView;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class MainActivitySimple extends AppCompatActivity
        implements NavigationView.OnNavigationItemSelectedListener {

    private TextView tvLanguageAr, tvLanguageEn;
    private ImageView menuIcon;
    private SharedPreferences prefs;
    private boolean isArabic = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // فرض استخدام الأرقام الإنجليزية في كل مكان
        forceEnglishNumbers();

        // تحديد اللغة المحفوظة
        prefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        isArabic = prefs.getBoolean("is_arabic", true);

        setLocale(isArabic ? "ar" : "en");
        setContentView(R.layout.activity_main_basic);

        // إعداد مفتاح تبديل اللغة
        setupLanguageToggle();

        // إعداد الأزرار الرئيسية
        setupMainButtons();

        // إعداد الأقسام والأيقونات
        setupSectionsAndIcons();

        // تحميل أوقات الصلاة الحقيقية
        loadRealPrayerTimes();

        // تحديث الوقت الحالي
        updateCurrentTime();

        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        NavigationView navigationView = findViewById(R.id.nav_view);
        navigationView.setNavigationItemSelectedListener(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        // تحديث أوقات الصلاة عند العودة للصفحة الرئيسية
        loadRealPrayerTimes();
        // تحديث الوقت الحالي أيضاً
        updateCurrentTime();
    }

    private void setupLanguageToggle() {
        tvLanguageAr = findViewById(R.id.tvLanguageAr);
        tvLanguageEn = findViewById(R.id.tvLanguageEn);
        menuIcon = findViewById(R.id.menuIcon);

        // تحديث حالة الأزرار إذا كانت موجودة
        if (tvLanguageAr != null && tvLanguageEn != null) {
            updateLanguageButtons();

            // إعداد أحداث النقر
            tvLanguageAr.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!isArabic) {
                        isArabic = true;
                        saveLanguagePreference();
                        setLocale("ar");
                        recreate();
                    }
                }
            });

            tvLanguageEn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (isArabic) {
                        isArabic = false;
                        saveLanguagePreference();
                        setLocale("en");
                        recreate();
                    }
                }
            });
        }

        // إعداد أيقونة القائمة
        if (menuIcon != null) {
            menuIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    DrawerLayout drawer = findViewById(R.id.drawer_layout);
                    if (drawer != null) {
                        drawer.openDrawer(GravityCompat.START);
                    }
                }
            });
        }
    }

    private void saveLanguagePreference() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("is_arabic", isArabic);
        editor.apply();
    }

    private void setupMainButtons() {
        // تحديث الوقت الحالي
        updateCurrentTime();

        // تحميل أوقات الصلاة الحقيقية
        loadRealPrayerTimes();

        // تحديث آخر سورة تم قراءتها
        updateLastReadSurah();
    }

    private void loadRealPrayerTimes() {
        // قراءة أوقات الصلاة المحفوظة من صفحة مواقيت الصلاة
        SharedPreferences prefs = getSharedPreferences("prayer_times", MODE_PRIVATE);

        String fajr = prefs.getString("fajr", "");
        String dhuhr = prefs.getString("dhuhr", "");
        String asr = prefs.getString("asr", "");
        String maghrib = prefs.getString("maghrib", "");
        String isha = prefs.getString("isha", "");

        // إذا كانت هناك أوقات محفوظة، استخدمها مباشرة
        if (!fajr.isEmpty() && !dhuhr.isEmpty()) {
            updatePrayerTimeViews(fajr, dhuhr, asr, maghrib, isha);
        } else {
            // إذا لم تكن هناك أوقات محفوظة، عرض رسالة للمستخدم لتحديث الأوقات
            updatePrayerTimeViews("--:--", "--:--", "--:--", "--:--", "--:--");
        }
    }

    // تحديث آخر سورة تم قراءتها
    private void updateLastReadSurah() {
        try {
            TextView lastReadSurahText = findViewById(R.id.lastReadSurah);
            if (lastReadSurahText != null) {
                // قراءة آخر سورة من SharedPreferences
                SharedPreferences prefs = getSharedPreferences("MyPrefsFile", MODE_PRIVATE);
                int lastSurahIndex = prefs.getInt("curSura", 0); // الافتراضي الفاتحة (0)

                // أسماء السور
                String[] surahNames = {
                    "الفاتحة", "البقرة", "آل عمران", "النساء", "المائدة", "الأنعام", "الأعراف", "الأنفال", "التوبة", "يونس",
                    "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
                    "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
                    "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
                    "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
                    "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
                    "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
                    "نوح", "الجن", "المزمل", "المدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
                    "التكوير", "الانفطار", "المطففين", "الانشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
                    "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
                    "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
                    "المسد", "الإخلاص", "الفلق", "الناس"
                };

                // التأكد من أن الفهرس صحيح
                if (lastSurahIndex >= 0 && lastSurahIndex < surahNames.length) {
                    lastReadSurahText.setText("سورة " + surahNames[lastSurahIndex]);
                } else {
                    lastReadSurahText.setText("سورة الفاتحة"); // افتراضي
                }
            }
        } catch (Exception e) {
            // في حالة الخطأ، عرض الفاتحة كافتراضي
            TextView lastReadSurahText = findViewById(R.id.lastReadSurah);
            if (lastReadSurahText != null) {
                lastReadSurahText.setText("سورة الفاتحة");
            }
        }
    }

    private void setupSectionsAndIcons() {
        // إعداد القرآن الكريم
        RelativeLayout quranSection = findViewById(R.id.quranSection);
        if (quranSection != null) {
            quranSection.setOnClickListener(v -> {
                Intent intent = new Intent(this, quranActivity.class);
                startActivity(intent);
            });
        }

        // إعداد قراء القرآن الكريم
        RelativeLayout recitersSection = findViewById(R.id.recitersSection);
        if (recitersSection != null) {
            recitersSection.setOnClickListener(v -> {
                Intent intent = new Intent(this, RecitesName.class);
                startActivity(intent);
            });
        }

        // إعداد الأذكار
        LinearLayout azkarSection = findViewById(R.id.azkarSection);
        if (azkarSection != null) {
            azkarSection.setOnClickListener(v -> {
                Intent intent = new Intent(this, AzkarActivity.class);
                startActivity(intent);
            });
        }

        // إعداد السبحة
        LinearLayout tasbihSection = findViewById(R.id.tasbihSection);
        if (tasbihSection != null) {
            tasbihSection.setOnClickListener(v -> {
                Intent intent = new Intent(this, Tazker.class);
                startActivity(intent);
            });
        }

        // إعداد مواقيت الصلاة
        LinearLayout prayerTimesSection = findViewById(R.id.prayerTimesSection);
        if (prayerTimesSection != null) {
            prayerTimesSection.setOnClickListener(v -> {
                Intent intent = new Intent(this, PrayerTimesActivity.class);
                startActivity(intent);
            });
        }



        // إعداد شريط التنقل السفلي العصري الجديد
        setupModernBottomNavigation();

        // ربط سياسة الخصوصية والمزيد من التطبيقات بصفحة About
        setupPrivacyAndMoreApps();
    }

    private void setupPrivacyAndMoreApps() {
        // يمكن إضافة أزرار سياسة الخصوصية والمزيد من التطبيقات هنا
        // أو ربطها بالقائمة الجانبية للانتقال إلى صفحة About
        // هذا سيتم تنفيذه في القائمة الجانبية
    }

    private void setupModernBottomNavigation() {
        // إعداد أيقونة الرئيسية
        LinearLayout homeNavItem = findViewById(R.id.homeNavItem);
        if (homeNavItem != null) {
            homeNavItem.setOnClickListener(v -> {
                animateNavItem(homeNavItem);
                // البقاء في الصفحة الرئيسية
                updateNavSelection(homeNavItem);
                Toast.makeText(this, "أنت في الصفحة الرئيسية", Toast.LENGTH_SHORT).show();
            });
        }

        // إعداد أيقونة القرآن
        LinearLayout quranNavItem = findViewById(R.id.quranNavItem);
        if (quranNavItem != null) {
            quranNavItem.setOnClickListener(v -> {
                animateNavItem(quranNavItem);
                updateNavSelection(quranNavItem);
                Intent intent = new Intent(this, quranActivity.class);
                startActivity(intent);
            });
        }

        // إعداد أيقونة الاستماع
        LinearLayout listenNavItem = findViewById(R.id.listenNavItem);
        if (listenNavItem != null) {
            listenNavItem.setOnClickListener(v -> {
                animateNavItem(listenNavItem);
                updateNavSelection(listenNavItem);
                Intent intent = new Intent(this, RecitesName.class);
                startActivity(intent);
            });
        }

        // إعداد أيقونة الأذكار
        LinearLayout azkarNavItem = findViewById(R.id.azkarNavItem);
        if (azkarNavItem != null) {
            azkarNavItem.setOnClickListener(v -> {
                animateNavItem(azkarNavItem);
                updateNavSelection(azkarNavItem);
                Intent intent = new Intent(this, AzkarActivity.class);
                startActivity(intent);
            });
        }

        // إعداد أيقونة المزيد
        LinearLayout moreNavItem = findViewById(R.id.moreNavItem);
        if (moreNavItem != null) {
            moreNavItem.setOnClickListener(v -> {
                animateNavItem(moreNavItem);
                updateNavSelection(moreNavItem);
                // فتح القائمة الجانبية أو صفحة المزيد
                DrawerLayout drawer = findViewById(R.id.drawer_layout);
                if (drawer != null) {
                    drawer.openDrawer(GravityCompat.START);
                }
            });
        }
    }

    private void animateNavItem(View view) {
        // تأثير حركي عصري عند النقر
        try {
            android.view.animation.Animation clickAnimation =
                android.view.animation.AnimationUtils.loadAnimation(this, R.anim.nav_icon_click);
            view.startAnimation(clickAnimation);
        } catch (Exception e) {
            // تأثير احتياطي
            view.animate()
                    .scaleX(0.85f)
                    .scaleY(0.85f)
                    .setDuration(150)
                    .withEndAction(() -> {
                        view.animate()
                                .scaleX(1.1f)
                                .scaleY(1.1f)
                                .setDuration(200)
                                .withEndAction(() -> {
                                    view.animate()
                                            .scaleX(1.0f)
                                            .scaleY(1.0f)
                                            .setDuration(150)
                                            .start();
                                })
                                .start();
                    })
                    .start();
        }
    }

    private void updateNavSelection(LinearLayout selectedItem) {
        // إعادة تعيين جميع الأيقونات للحالة غير المحددة
        resetAllNavItems();

        // تحديد العنصر المحدد
        ImageView icon = (ImageView) selectedItem.getChildAt(0);
        TextView text = (TextView) selectedItem.getChildAt(1);

        if (icon != null) {
            icon.setColorFilter(getResources().getColor(R.color.islamicGreenMedium));
        }
        if (text != null) {
            text.setTextColor(getResources().getColor(R.color.islamicGreenMedium));
        }
    }

    private void resetAllNavItems() {
        // إعادة تعيين جميع عناصر التنقل للحالة غير المحددة
        int[] navItemIds = {R.id.homeNavItem, R.id.quranNavItem, R.id.listenNavItem,
                           R.id.azkarNavItem, R.id.moreNavItem};

        for (int id : navItemIds) {
            LinearLayout navItem = findViewById(id);
            if (navItem != null) {
                ImageView icon = (ImageView) navItem.getChildAt(0);
                TextView text = (TextView) navItem.getChildAt(1);

                if (icon != null) {
                    icon.setColorFilter(getResources().getColor(R.color.textSecondary));
                }
                if (text != null) {
                    text.setTextColor(getResources().getColor(R.color.textSecondary));
                }
            }
        }
    }

    private void updatePrayerTimeViews(String fajr, String dhuhr, String asr, String maghrib, String isha) {
        // تحديث أوقات الصلاة باستخدام المعرفات المباشرة
        TextView fajrTime = findViewById(R.id.fajrTime);
        TextView dhuhrTime = findViewById(R.id.dhuhrTime);
        TextView asrTime = findViewById(R.id.asrTime);
        TextView maghribTime = findViewById(R.id.maghribTime);
        TextView ishaTime = findViewById(R.id.ishaTime);

        // تحويل الأوقات إلى تنسيق 12 ساعة مع تحويل الأرقام
        if (fajrTime != null) fajrTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(fajr)));
        if (dhuhrTime != null) dhuhrTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(dhuhr)));
        if (asrTime != null) asrTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(asr)));
        if (maghribTime != null) maghribTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(maghrib)));
        if (ishaTime != null) ishaTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(isha)));
    }

    // دالة لتحويل الوقت إلى تنسيق 12 ساعة
    private String convertTo12HourFormat(String time) {
        if (time == null || time.isEmpty()) return time;

        try {
            // إزالة أي نص إضافي (مثل المنطقة الزمنية)
            String cleanTime = time.split(" ")[0];

            // تحويل من 24 ساعة إلى 12 ساعة مع AM/PM
            SimpleDateFormat input = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
            SimpleDateFormat output = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

            String formattedTime = output.format(input.parse(cleanTime));

            // تطبيق الترجمة حسب اللغة المختارة
            if (isArabic) {
                return formattedTime.replace("AM", "ص").replace("PM", "م");
            } else {
                return formattedTime;
            }
        } catch (Exception e) {
            // إذا فشل التحويل، إرجاع الوقت كما هو
            return time;
        }
    }

    // دالة لتحويل الأرقام العربية إلى إنجليزية
    private String convertArabicToEnglishNumbers(String text) {
        if (text == null) return "";

        // تحويل الأرقام العربية-الهندية (٠١٢٣٤٥٦٧٨٩)
        String result = text.replace("٠", "0")
                           .replace("١", "1")
                           .replace("٢", "2")
                           .replace("٣", "3")
                           .replace("٤", "4")
                           .replace("٥", "5")
                           .replace("٦", "6")
                           .replace("٧", "7")
                           .replace("٨", "8")
                           .replace("٩", "9");

        // تحويل الأرقام الفارسية/الأردية (۰۱۲۳۴۵۶۷۸۹) إذا وجدت
        result = result.replace("۰", "0")
                      .replace("۱", "1")
                      .replace("۲", "2")
                      .replace("۳", "3")
                      .replace("۴", "4")
                      .replace("۵", "5")
                      .replace("۶", "6")
                      .replace("۷", "7")
                      .replace("۸", "8")
                      .replace("۹", "9");

        return result;
    }

    // دالة لفرض استخدام الأرقام الإنجليزية في كل مكان
    private void forceEnglishNumbers() {
        // فرض استخدام الأرقام الإنجليزية بدلاً من العربية
        Locale.setDefault(new Locale("en", "US"));

        // إعداد إضافي لضمان استخدام الأرقام الإنجليزية
        Configuration config = getResources().getConfiguration();
        config.locale = new Locale("en", "US");
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    private void updateCurrentTime() {
        TextView currentTimeView = findViewById(R.id.currentTime);
        if (currentTimeView != null) {
            Calendar calendar = Calendar.getInstance();
            // استخدام تنسيق 12 ساعة مع AM/PM
            SimpleDateFormat timeFormat = new SimpleDateFormat("h:mm a", Locale.ENGLISH);
            String currentTime = timeFormat.format(calendar.getTime());

            // تطبيق الترجمة حسب اللغة المختارة
            if (isArabic) {
                currentTime = currentTime.replace("AM", "ص").replace("PM", "م");
            }

            currentTimeView.setText(currentTime);
        }

        // تحديث اليوم
        TextView dayText = findViewById(R.id.dayText);
        if (dayText != null) {
            Calendar calendar = Calendar.getInstance();

            // تحديد اللغة المختارة
            SharedPreferences prefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
            boolean isArabic = prefs.getBoolean("is_arabic", true);

            Locale currentLocale = isArabic ? new Locale("ar") : Locale.ENGLISH;
            SimpleDateFormat dayFormat = new SimpleDateFormat("EEEE", currentLocale);
            String currentDay = dayFormat.format(calendar.getTime());
            dayText.setText(currentDay);
        }
    }

    private void updateLanguageButtons() {
        if (isArabic) {
            tvLanguageAr.setTextColor(getResources().getColor(R.color.white));
            tvLanguageAr.setBackgroundResource(R.drawable.language_button_selected);
            tvLanguageEn.setTextColor(getResources().getColor(R.color.islamicGreenDark));
            tvLanguageEn.setBackgroundResource(R.drawable.language_button_unselected);
        } else {
            tvLanguageAr.setTextColor(getResources().getColor(R.color.islamicGreenDark));
            tvLanguageAr.setBackgroundResource(R.drawable.language_button_unselected);
            tvLanguageEn.setTextColor(getResources().getColor(R.color.white));
            tvLanguageEn.setBackgroundResource(R.drawable.language_button_selected);
        }
    }

    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    @Override
    public void onBackPressed() {
        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        if (drawer.isDrawerOpen(GravityCompat.START)) {
            drawer.closeDrawer(GravityCompat.START);
        } else {
            super.onBackPressed();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onNavigationItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.nav_home) {
            // البقاء في الصفحة الرئيسية
        } else if (id == R.id.playl) {
            // startActivity(new Intent(this, RecitesName.class));
        } else if (id == R.id.kraal) {
            // startActivity(new Intent(this, quranActivity.class));
        } else if (id == R.id.close) {
            finish();
        }

        DrawerLayout drawer = findViewById(R.id.drawer_layout);
        drawer.closeDrawer(GravityCompat.START);
        return true;
    }
}

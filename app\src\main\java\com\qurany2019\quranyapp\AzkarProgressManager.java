package com.qurany2019.quranyapp;

import android.content.Context;
import android.content.SharedPreferences;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class AzkarProgressManager {
    private static final String PREFS_NAME = "azkar_progress";
    private static final String KEY_LAST_RESET_DATE = "last_reset_date";
    private static final String KEY_TOTAL_AZKAR_TODAY = "total_azkar_today";
    private static final String KEY_COMPLETED_SECTIONS_TODAY = "completed_sections_today";
    private static final String KEY_STREAK_DAYS = "streak_days";
    private static final String KEY_LAST_ACTIVITY_DATE = "last_activity_date";
    
    private SharedPreferences prefs;
    private Context context;
    private SimpleDateFormat dateFormat;

    public AzkarProgressManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        
        // Check if we need to reset daily progress
        checkAndResetDailyProgress();
    }

    // Save progress for a specific azkar
    public void saveAzkarProgress(String azkarType, String azkarId, int currentCount, boolean isCompleted) {
        String key = azkarType + "_" + azkarId;
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(key + "_count", currentCount);
        editor.putBoolean(key + "_completed", isCompleted);
        editor.putLong(key + "_last_read", System.currentTimeMillis());
        editor.apply();
        
        // Update daily statistics
        updateDailyStats();
    }

    // Load progress for a specific azkar
    public void loadAzkarProgress(String azkarType, AzkarItem azkarItem) {
        String key = azkarType + "_" + azkarItem.getAzkarId();
        int savedCount = prefs.getInt(key + "_count", 0);
        boolean isCompleted = prefs.getBoolean(key + "_completed", false);
        long lastReadTime = prefs.getLong(key + "_last_read", 0);
        
        azkarItem.setCurrentCount(savedCount);
        azkarItem.setLastReadTime(lastReadTime);
        if (isCompleted) {
            azkarItem.markAsCompleted();
        }
    }

    // Load progress for all azkar in a list
    public void loadProgressForAzkarList(String azkarType, List<AzkarItem> azkarList) {
        for (AzkarItem azkar : azkarList) {
            loadAzkarProgress(azkarType, azkar);
        }
    }

    // Save progress for all azkar in a list
    public void saveProgressForAzkarList(String azkarType, List<AzkarItem> azkarList) {
        for (AzkarItem azkar : azkarList) {
            saveAzkarProgress(azkarType, azkar.getAzkarId(), azkar.getCurrentCount(), azkar.isCompleted());
        }
    }

    // Check if a section is completed
    public boolean isSectionCompleted(String azkarType, List<AzkarItem> azkarList) {
        for (AzkarItem azkar : azkarList) {
            if (!azkar.isCompleted()) {
                return false;
            }
        }
        return true;
    }

    // Get section progress percentage
    public int getSectionProgress(List<AzkarItem> azkarList) {
        if (azkarList.isEmpty()) return 0;
        
        int totalAzkar = azkarList.size();
        int completedAzkar = 0;
        
        for (AzkarItem azkar : azkarList) {
            if (azkar.isCompleted()) {
                completedAzkar++;
            }
        }
        
        return Math.round((float) completedAzkar / totalAzkar * 100);
    }

    // Get detailed section progress
    public String getSectionProgressText(List<AzkarItem> azkarList) {
        int completed = 0;
        int total = azkarList.size();
        
        for (AzkarItem azkar : azkarList) {
            if (azkar.isCompleted()) {
                completed++;
            }
        }
        
        return completed + " من " + total;
    }

    // Daily statistics methods
    private void updateDailyStats() {
        String today = dateFormat.format(new Date());
        SharedPreferences.Editor editor = prefs.edit();
        
        // Update last activity date
        editor.putString(KEY_LAST_ACTIVITY_DATE, today);
        
        // Update streak
        updateStreak(today);
        
        editor.apply();
    }

    private void updateStreak(String today) {
        String lastActivity = prefs.getString(KEY_LAST_ACTIVITY_DATE, "");
        int currentStreak = prefs.getInt(KEY_STREAK_DAYS, 0);
        
        if (lastActivity.isEmpty()) {
            // First time
            prefs.edit().putInt(KEY_STREAK_DAYS, 1).apply();
        } else if (!lastActivity.equals(today)) {
            // Check if it's consecutive day
            try {
                Date lastDate = dateFormat.parse(lastActivity);
                Date todayDate = dateFormat.parse(today);
                long diffInMillies = todayDate.getTime() - lastDate.getTime();
                long diffInDays = diffInMillies / (24 * 60 * 60 * 1000);
                
                if (diffInDays == 1) {
                    // Consecutive day
                    prefs.edit().putInt(KEY_STREAK_DAYS, currentStreak + 1).apply();
                } else if (diffInDays > 1) {
                    // Streak broken
                    prefs.edit().putInt(KEY_STREAK_DAYS, 1).apply();
                }
                // If diffInDays == 0, it's the same day, no change needed
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    // Check and reset daily progress if needed
    private void checkAndResetDailyProgress() {
        String today = dateFormat.format(new Date());
        String lastResetDate = prefs.getString(KEY_LAST_RESET_DATE, "");
        
        if (!today.equals(lastResetDate)) {
            // Reset daily progress
            resetDailyProgress();
            prefs.edit().putString(KEY_LAST_RESET_DATE, today).apply();
        }
    }

    // Reset daily progress (called automatically each day)
    public void resetDailyProgress() {
        SharedPreferences.Editor editor = prefs.edit();
        
        // Clear all azkar progress
        editor.clear();
        
        // Keep streak and other persistent data
        String today = dateFormat.format(new Date());
        editor.putString(KEY_LAST_RESET_DATE, today);
        
        editor.apply();
    }

    // Get daily statistics
    public int getTotalAzkarToday() {
        return prefs.getInt(KEY_TOTAL_AZKAR_TODAY, 0);
    }

    public int getCompletedSectionsToday() {
        return prefs.getInt(KEY_COMPLETED_SECTIONS_TODAY, 0);
    }

    public int getStreakDays() {
        return prefs.getInt(KEY_STREAK_DAYS, 0);
    }

    // Manual reset (for testing or user request)
    public void resetAllProgress() {
        prefs.edit().clear().apply();
    }

    // Check if user has completed any azkar today
    public boolean hasActivityToday() {
        String today = dateFormat.format(new Date());
        String lastActivity = prefs.getString(KEY_LAST_ACTIVITY_DATE, "");
        return today.equals(lastActivity);
    }
}

package com.qurany2019.quranyapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.tabs.TabLayout;
import com.qurany2019.quranyapp.adapters.AchievementAdapter;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;
import com.qurany2019.quranyapp.viewmodel.AzkarViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class AchievementsActivity extends BaseActivity {
    
    private AzkarViewModel viewModel;
    private AchievementAdapter adapter;
    
    // UI Components
    private TextView totalAchievements;
    private TextView unlockedAchievements;
    private TextView totalPoints;
    private TextView progressText;
    private ProgressBar achievementProgressBar;
    private TabLayout achievementTabs;
    private RecyclerView achievementsRecyclerView;
    
    private List<AchievementEntity> allAchievements = new ArrayList<>();
    private List<AchievementEntity> filteredAchievements = new ArrayList<>();

    // BroadcastReceiver للتحديث الفوري
    private BroadcastReceiver achievementUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if ("ACHIEVEMENT_UPDATED".equals(intent.getAction())) {
                // إعادة تحميل البيانات فوراً
                refreshAchievements();
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_achievements);
        
        setupToolbar();
        initializeViews();
        setupViewModel();
        setupRecyclerView();
        setupTabs();
        observeData();

        // تسجيل BroadcastReceiver مع تحديد RECEIVER_NOT_EXPORTED للأمان
        IntentFilter filter = new IntentFilter("ACHIEVEMENT_UPDATED");
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(achievementUpdateReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(achievementUpdateReceiver, filter);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // إلغاء تسجيل BroadcastReceiver
        try {
            unregisterReceiver(achievementUpdateReceiver);
        } catch (Exception e) {
            // تجاهل الخطأ إذا لم يكن مسجل
        }
    }

    // دالة إعادة تحميل الإنجازات فوراً
    private void refreshAchievements() {
        if (viewModel != null) {
            // إعادة مراقبة البيانات لتحديث فوري
            viewModel.getAllAchievements().observe(this, achievements -> {
                if (achievements != null) {
                    allAchievements.clear();
                    allAchievements.addAll(achievements);
                    updateSummaryStatistics();
                    filterAchievements(achievementTabs.getSelectedTabPosition());
                }
            });
        }
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
        
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void initializeViews() {
        totalAchievements = findViewById(R.id.totalAchievements);
        unlockedAchievements = findViewById(R.id.unlockedAchievements);
        totalPoints = findViewById(R.id.totalPoints);
        progressText = findViewById(R.id.progressText);
        achievementProgressBar = findViewById(R.id.achievementProgressBar);
        achievementTabs = findViewById(R.id.achievementTabs);
        achievementsRecyclerView = findViewById(R.id.achievementsRecyclerView);
    }

    private void setupViewModel() {
        viewModel = new ViewModelProvider(this).get(AzkarViewModel.class);
    }

    private void setupRecyclerView() {
        adapter = new AchievementAdapter(this, filteredAchievements);
        achievementsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        achievementsRecyclerView.setAdapter(adapter);
    }

    private void setupTabs() {
        achievementTabs.addTab(achievementTabs.newTab().setText(getString(R.string.tab_all)));
        achievementTabs.addTab(achievementTabs.newTab().setText(getString(R.string.tab_unlocked)));
        achievementTabs.addTab(achievementTabs.newTab().setText(getString(R.string.tab_locked)));
        achievementTabs.addTab(achievementTabs.newTab().setText(getString(R.string.tab_daily)));
        achievementTabs.addTab(achievementTabs.newTab().setText(getString(R.string.tab_weekly)));
        achievementTabs.addTab(achievementTabs.newTab().setText(getString(R.string.tab_monthly)));
        
        achievementTabs.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                filterAchievements(tab.getPosition());
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });
    }

    private void observeData() {
        viewModel.getAllAchievements().observe(this, achievements -> {
            if (achievements != null) {
                allAchievements.clear();
                allAchievements.addAll(achievements);

                // تحديث الإنجازات بناءً على البيانات الحقيقية
                updateAchievementsBasedOnRealData();

                updateSummaryStatistics();
                filterAchievements(achievementTabs.getSelectedTabPosition());
            }
        });
    }

    private void updateAchievementsBasedOnRealData() {
        try {
            // قراءة البيانات الحقيقية من SharedPreferences
            android.content.SharedPreferences prefs = getSharedPreferences("azkar_progress", MODE_PRIVATE);

            // قراءة الإحصائيات
            int totalCompleted = prefs.getInt("total_azkar_completed", 0);
            int totalTasbih = prefs.getInt("total_tasbih_count", 0);
            int streakDays = prefs.getInt("streak_days", 0);

            // حساب أذكار اليوم
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
            String today = sdf.format(new java.util.Date());
            int todayAzkar = prefs.getInt("daily_azkar_" + today, 0);

            // تحديث الإنجازات بناءً على البيانات الحقيقية
            for (AchievementEntity achievement : allAchievements) {
                boolean shouldUnlock = false;

                switch (achievement.getAchievementId()) {
                    case "first_azkar":
                        shouldUnlock = totalCompleted >= 1;
                        break;
                    case "morning_complete":
                    case "evening_complete":
                        shouldUnlock = todayAzkar >= 5; // إذا أكمل 5 أذكار اليوم
                        break;
                    case "daily_complete":
                        shouldUnlock = todayAzkar >= 10; // إذا أكمل 10 أذكار اليوم
                        break;
                    case "week_streak":
                        shouldUnlock = streakDays >= 7;
                        break;
                    case "hundred_tasbih":
                        shouldUnlock = totalTasbih >= 100;
                        break;
                    case "month_streak":
                        shouldUnlock = streakDays >= 30;
                        break;
                    case "thousand_tasbih":
                        shouldUnlock = totalTasbih >= 1000;
                        break;
                    case "dedicated_user":
                        shouldUnlock = streakDays >= 100;
                        break;
                    case "azkar_master":
                        shouldUnlock = totalCompleted >= 1000;
                        break;
                }

                // تحديث حالة الإنجاز إذا تحقق الشرط
                if (shouldUnlock && !achievement.isUnlocked()) {
                    achievement.setUnlocked(true);
                    // achievement.setUnlockedDate(System.currentTimeMillis()); // معطل مؤقتاً
                    // حفظ التحديث في قاعدة البيانات
                    // viewModel.updateAchievement(achievement); // معطل مؤقتاً
                }
            }

        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    private void updateSummaryStatistics() {
        int total = allAchievements.size();
        int unlocked = 0;
        int points = 0;
        
        for (AchievementEntity achievement : allAchievements) {
            if (achievement.isUnlocked()) {
                unlocked++;
                points += achievement.getRewardPoints();
            }
        }
        
        totalAchievements.setText(String.valueOf(total));
        unlockedAchievements.setText(String.valueOf(unlocked));
        totalPoints.setText(String.valueOf(points));
        
        // تحديث شريط التقدم
        int progressPercentage = total > 0 ? (unlocked * 100) / total : 0;
        achievementProgressBar.setMax(100);
        achievementProgressBar.setProgress(progressPercentage);
        progressText.setText(progressPercentage + getString(R.string.progress_completed));
    }

    private void filterAchievements(int tabPosition) {
        filteredAchievements.clear();
        
        switch (tabPosition) {
            case 0: // الكل
                filteredAchievements.addAll(allAchievements);
                break;
            case 1: // مفتوحة
                for (AchievementEntity achievement : allAchievements) {
                    if (achievement.isUnlocked()) {
                        filteredAchievements.add(achievement);
                    }
                }
                break;
            case 2: // مقفلة
                for (AchievementEntity achievement : allAchievements) {
                    if (!achievement.isUnlocked()) {
                        filteredAchievements.add(achievement);
                    }
                }
                break;
            case 3: // يومية
                filterByType("daily");
                break;
            case 4: // أسبوعية
                filterByType("weekly");
                break;
            case 5: // شهرية
                filterByType("monthly");
                break;
        }
        
        adapter.notifyDataSetChanged();
    }

    private void filterByType(String type) {
        for (AchievementEntity achievement : allAchievements) {
            if (type.equals(achievement.getAchievementType())) {
                filteredAchievements.add(achievement);
            }
        }
    }

    // تم حذف دوال إنشاء الإنجازات المكررة
    // AzkarDataInitializer يقوم بإنشاء الإنجازات الافتراضية

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}

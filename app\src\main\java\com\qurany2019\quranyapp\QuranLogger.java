package com.qurany2019.quranyapp;

import android.util.Log;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * نظام تتبع شامل لتطبيق القرآن الكريم
 * يسجل جميع الأحداث والأخطاء مع تفاصيل دقيقة
 */
public class QuranLogger {
    
    // أسماء السجلات المختلفة
    public static final String MEDIA_PLAYER = "🎵 MEDIA_PLAYER";
    public static final String FILE_SYSTEM = "📁 FILE_SYSTEM";
    public static final String NOTIFICATION = "🔔 NOTIFICATION";
    public static final String DOWNLOAD = "⬇️ DOWNLOAD";
    public static final String NETWORK = "🌐 NETWORK";
    public static final String UI_CONTROL = "🎮 UI_CONTROL";
    public static final String BROADCAST = "📡 BROADCAST";
    public static final String STATIC_CONTROL = "⚡ STATIC_CONTROL";
    public static final String AUDIO_PATH = "🎧 AUDIO_PATH";
    public static final String STORAGE = "💾 STORAGE";
    
    private static final String LOG_FILE_NAME = "quran_debug.log";
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault());
    
    /**
     * تسجيل معلومات عامة
     */
    public static void info(String tag, String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = String.format("[%s] %s: %s", timestamp, tag, message);
        Log.i(tag, message);
        writeToFile(logMessage);
    }
    
    /**
     * تسجيل أخطاء
     */
    public static void error(String tag, String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = String.format("[%s] ❌ %s: %s", timestamp, tag, message);
        Log.e(tag, message);
        writeToFile(logMessage);
    }
    
    /**
     * تسجيل أخطاء مع استثناء
     */
    public static void error(String tag, String message, Exception e) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = String.format("[%s] ❌ %s: %s - Exception: %s", 
            timestamp, tag, message, e.getMessage());
        Log.e(tag, message, e);
        writeToFile(logMessage);
        if (e.getStackTrace().length > 0) {
            writeToFile("Stack trace: " + Log.getStackTraceString(e));
        }
    }
    
    /**
     * تسجيل تحذيرات
     */
    public static void warning(String tag, String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = String.format("[%s] ⚠️ %s: %s", timestamp, tag, message);
        Log.w(tag, message);
        writeToFile(logMessage);
    }
    
    /**
     * تسجيل تصحيح الأخطاء
     */
    public static void debug(String tag, String message) {
        String timestamp = dateFormat.format(new Date());
        String logMessage = String.format("[%s] 🔍 %s: %s", timestamp, tag, message);
        Log.d(tag, message);
        writeToFile(logMessage);
    }
    
    /**
     * تسجيل حالة الملف
     */
    public static void logFileStatus(String filePath) {
        try {
            File file = new File(filePath);
            String status = String.format(
                "📁 ملف: %s\n" +
                "   ✅ موجود: %s\n" +
                "   📏 الحجم: %d بايت\n" +
                "   📖 قابل للقراءة: %s\n" +
                "   ✍️ قابل للكتابة: %s\n" +
                "   📂 المجلد الأب: %s",
                filePath,
                file.exists(),
                file.length(),
                file.canRead(),
                file.canWrite(),
                file.getParent()
            );
            info(FILE_SYSTEM, status);
        } catch (Exception e) {
            error(FILE_SYSTEM, "خطأ في فحص الملف: " + filePath, e);
        }
    }
    
    /**
     * تسجيل حالة MediaPlayer
     */
    public static void logMediaPlayerStatus(android.media.MediaPlayer mp) {
        try {
            if (mp == null) {
                warning(MEDIA_PLAYER, "MediaPlayer is null!");
                return;
            }
            
            String status = String.format(
                "🎵 MediaPlayer Status:\n" +
                "   ▶️ يعمل: %s\n" +
                "   ⏱️ المدة الحالية: %d ms\n" +
                "   ⏱️ المدة الكاملة: %d ms\n" +
                "   🔊 مستوى الصوت: %.2f",
                mp.isPlaying(),
                mp.getCurrentPosition(),
                mp.getDuration(),
                1.0f // يمكن تحسينه لاحقاً
            );
            info(MEDIA_PLAYER, status);
        } catch (Exception e) {
            error(MEDIA_PLAYER, "خطأ في فحص MediaPlayer", e);
        }
    }
    
    /**
     * تسجيل معلومات الشبكة
     */
    public static void logNetworkInfo(android.content.Context context) {
        try {
            android.net.ConnectivityManager cm = (android.net.ConnectivityManager) 
                context.getSystemService(android.content.Context.CONNECTIVITY_SERVICE);
            android.net.NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
            
            if (activeNetwork != null) {
                String networkInfo = String.format(
                    "🌐 الشبكة:\n" +
                    "   🔗 متصل: %s\n" +
                    "   📶 النوع: %s\n" +
                    "   🏷️ الاسم: %s",
                    activeNetwork.isConnected(),
                    activeNetwork.getTypeName(),
                    activeNetwork.getExtraInfo()
                );
                info(NETWORK, networkInfo);
            } else {
                warning(NETWORK, "لا توجد شبكة متاحة");
            }
        } catch (Exception e) {
            error(NETWORK, "خطأ في فحص الشبكة", e);
        }
    }
    
    /**
     * كتابة السجل في ملف
     */
    private static void writeToFile(String message) {
        try {
            // يمكن تحسين مسار الملف لاحقاً
            File logFile = new File("/sdcard/Download/" + LOG_FILE_NAME);
            FileWriter writer = new FileWriter(logFile, true);
            writer.append(message).append("\n");
            writer.close();
        } catch (IOException e) {
            // تجاهل أخطاء كتابة السجل لتجنب التكرار اللانهائي
        }
    }
    
    /**
     * مسح ملف السجل
     */
    public static void clearLogFile() {
        try {
            File logFile = new File("/sdcard/Download/" + LOG_FILE_NAME);
            if (logFile.exists()) {
                logFile.delete();
            }
            info("LOGGER", "تم مسح ملف السجل");
        } catch (Exception e) {
            error("LOGGER", "خطأ في مسح ملف السجل", e);
        }
    }
}

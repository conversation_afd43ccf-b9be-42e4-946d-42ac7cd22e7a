package com.qurany2019.quranyapp;

import android.app.AlarmManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

/**
 * خدمة إشعارات أوقات الصلاة
 * تقوم بإرسال إشعارات جميلة عند اقتراب أوقات الصلاة
 */
public class PrayerNotificationService {
    
    private static final String TAG = "PrayerNotificationService";
    private static final String CHANNEL_ID = "prayer_times_channel";
    private static final String CHANNEL_NAME = "أوقات الصلاة";
    private static final String CHANNEL_DESCRIPTION = "إشعارات أوقات الصلاة";
    
    // أسماء الصلوات - سيتم استخدام نظام الترجمة
    public String[] getPrayerNames() {
        return new String[]{
            context.getString(R.string.fajr),
            context.getString(R.string.sunrise),
            context.getString(R.string.dhuhr),
            context.getString(R.string.asr),
            context.getString(R.string.maghrib),
            context.getString(R.string.isha)
        };
    }
    
    // رموز الصلوات الجميلة
    public static final String[] PRAYER_ICONS = {
        "🌅", "☀️", "🌞", "🌇", "🌆", "🌙"
    };
    
    private final Context context;
    private final NotificationManager notificationManager;
    private final AlarmManager alarmManager;
    
    public PrayerNotificationService(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        this.alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        
        createNotificationChannel();
    }
    
    /**
     * إنشاء قناة الإشعارات
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{0, 1000, 500, 1000});
            channel.enableLights(true);
            
            notificationManager.createNotificationChannel(channel);
            Log.d(TAG, "Notification channel created");
        }
    }
    
    /**
     * جدولة إشعارات أوقات الصلاة
     */
    public void schedulePrayerNotifications(String[] prayerTimes, String cityName) {
        if (prayerTimes == null || prayerTimes.length < 6) {
            Log.e(TAG, "Invalid prayer times array");
            return;
        }
        
        // إلغاء الإشعارات السابقة
        cancelAllNotifications();
        
        Calendar today = Calendar.getInstance();
        
        for (int i = 0; i < prayerTimes.length; i++) {
            if (i == 1) continue; // تخطي الشروق
            
            try {
                Calendar prayerTime = parseTime(prayerTimes[i]);
                if (prayerTime != null) {
                    // إشعار قبل 10 دقائق
                    scheduleNotification(prayerTime, i, cityName, 10);
                    
                    // إشعار في وقت الصلاة
                    scheduleNotification(prayerTime, i, cityName, 0);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error scheduling notification for " + getPrayerNames()[i], e);
            }
        }
        
        Log.d(TAG, "Prayer notifications scheduled for " + cityName);
    }
    
    /**
     * جدولة إشعار واحد
     */
    private void scheduleNotification(Calendar prayerTime, int prayerIndex, String cityName, int minutesBefore) {
        Calendar notificationTime = (Calendar) prayerTime.clone();
        notificationTime.add(Calendar.MINUTE, -minutesBefore);
        
        // إذا كان الوقت قد مضى اليوم، جدوله لليوم التالي
        Calendar now = Calendar.getInstance();
        if (notificationTime.before(now)) {
            notificationTime.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        Intent intent = new Intent(context, PrayerNotificationReceiver.class);
        intent.putExtra("prayer_name", getPrayerNames()[prayerIndex]);
        intent.putExtra("prayer_icon", PRAYER_ICONS[prayerIndex]);
        intent.putExtra("city_name", cityName);
        intent.putExtra("minutes_before", minutesBefore);
        intent.putExtra("prayer_time", formatTime(prayerTime));
        
        int requestCode = prayerIndex * 100 + minutesBefore;
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    notificationTime.getTimeInMillis(),
                    pendingIntent
                );
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    notificationTime.getTimeInMillis(),
                    pendingIntent
                );
            }
            
            Log.d(TAG, String.format("Scheduled %s notification for %s at %s (-%d min)",
                getPrayerNames()[prayerIndex], cityName,
                formatTime(notificationTime), minutesBefore));
                
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied for exact alarms", e);
        }
    }
    
    /**
     * تحليل وقت الصلاة من النص
     */
    private Calendar parseTime(String timeString) {
        try {
            // إزالة ص/م وتحويل إلى 24 ساعة
            String cleanTime = timeString.replace("ص", "AM").replace("م", "PM").trim();
            
            SimpleDateFormat inputFormat = new SimpleDateFormat("h:mm a", Locale.ENGLISH);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(inputFormat.parse(cleanTime));
            
            // نسخ التاريخ الحالي مع الوقت المحدد
            Calendar result = Calendar.getInstance();
            result.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY));
            result.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE));
            result.set(Calendar.SECOND, 0);
            result.set(Calendar.MILLISECOND, 0);
            
            return result;
        } catch (Exception e) {
            Log.e(TAG, "Error parsing time: " + timeString, e);
            return null;
        }
    }
    
    /**
     * تنسيق الوقت للعرض
     */
    private String formatTime(Calendar calendar) {
        SimpleDateFormat format = new SimpleDateFormat("h:mm a", new Locale("ar"));
        return format.format(calendar.getTime()).replace("AM", "ص").replace("PM", "م");
    }
    
    /**
     * إلغاء جميع الإشعارات المجدولة
     */
    public void cancelAllNotifications() {
        for (int i = 0; i < getPrayerNames().length; i++) {
            if (i == 1) continue; // تخطي الشروق
            
            // إلغاء إشعار قبل 10 دقائق
            cancelNotification(i * 100 + 10);
            
            // إلغاء إشعار وقت الصلاة
            cancelNotification(i * 100);
        }
        
        Log.d(TAG, "All prayer notifications cancelled");
    }
    
    /**
     * إلغاء إشعار محدد
     */
    private void cancelNotification(int requestCode) {
        Intent intent = new Intent(context, PrayerNotificationReceiver.class);
        PendingIntent pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        alarmManager.cancel(pendingIntent);
    }
    
    /**
     * التحقق من تفعيل الإشعارات
     */
    public boolean areNotificationsEnabled() {
        SharedPreferences prefs = context.getSharedPreferences("prayer_settings", Context.MODE_PRIVATE);
        return prefs.getBoolean("notifications_enabled", true);
    }
    
    /**
     * تفعيل/إلغاء تفعيل الإشعارات
     */
    public void setNotificationsEnabled(boolean enabled) {
        SharedPreferences prefs = context.getSharedPreferences("prayer_settings", Context.MODE_PRIVATE);
        prefs.edit().putBoolean("notifications_enabled", enabled).apply();
        
        if (!enabled) {
            cancelAllNotifications();
        }
        
        Log.d(TAG, "Notifications " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * إرسال إشعار فوري للاختبار
     */
    public void sendTestNotification() {
        String title = "🕌 اختبار إشعارات أوقات الصلاة";
        String message = "تم تفعيل إشعارات أوقات الصلاة بنجاح! ستصلك إشعارات عند اقتراب كل صلاة.";
        
        showNotification(title, message, 9999);
    }
    
    /**
     * عرض الإشعار
     */
    public void showNotification(String title, String message, int notificationId) {
        Intent intent = new Intent(context, PrayerTimesActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_mosque) // تأكد من وجود هذا الأيقون
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(new NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setVibrate(new long[]{0, 1000, 500, 1000})
            .setLights(0xFF00FF00, 3000, 3000);
        
        try {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(context);
            notificationManager.notify(notificationId, builder.build());
            Log.d(TAG, "Notification shown: " + title);
        } catch (SecurityException e) {
            Log.e(TAG, "Permission denied for notifications", e);
        }
    }
}

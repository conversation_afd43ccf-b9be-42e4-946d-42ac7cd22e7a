package com.qurany2019.quranyapp.notifications;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.work.Worker;
import androidx.work.WorkerParameters;

import java.util.Calendar;

public class AzkarReminderWorker extends Worker {

    public AzkarReminderWorker(@NonNull Context context, @NonNull WorkerParameters workerParams) {
        super(context, workerParams);
    }

    @NonNull
    @Override
    public Result doWork() {
        String reminderType = getInputData().getString("reminder_type");
        int targetHour = getInputData().getInt("hour", 0);
        int targetMinute = getInputData().getInt("minute", 0);

        // التحقق من الوقت الحالي
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);
        int currentMinute = now.get(Calendar.MINUTE);

        // التحقق إذا كان الوقت مناسب لإرسال التذكير
        if (isTimeForReminder(currentHour, currentMinute, targetHour, targetMinute)) {
            AzkarNotificationManager notificationManager = new AzkarNotificationManager(getApplicationContext());
            
            if ("morning_reminder".equals(reminderType)) {
                notificationManager.showMorningReminderNotification();
            } else if ("evening_reminder".equals(reminderType)) {
                notificationManager.showEveningReminderNotification();
            }
        }

        return Result.success();
    }

    private boolean isTimeForReminder(int currentHour, int currentMinute, int targetHour, int targetMinute) {
        // السماح بهامش 15 دقيقة
        int currentTotalMinutes = currentHour * 60 + currentMinute;
        int targetTotalMinutes = targetHour * 60 + targetMinute;
        
        return Math.abs(currentTotalMinutes - targetTotalMinutes) <= 15;
    }
}

package com.qurany2019.quranyapp.database.dao;

import android.database.Cursor;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.qurany2019.quranyapp.database.entities.AzkarProgressEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AzkarProgressDao_Impl implements AzkarProgressDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AzkarProgressEntity> __insertionAdapterOfAzkarProgressEntity;

  private final EntityDeletionOrUpdateAdapter<AzkarProgressEntity> __deletionAdapterOfAzkarProgressEntity;

  private final EntityDeletionOrUpdateAdapter<AzkarProgressEntity> __updateAdapterOfAzkarProgressEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProgressById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProgressByAzkarId;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProgressByDate;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProgressOlderThan;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllProgress;

  private final SharedSQLiteStatement __preparedStmtOfUpdateDateTimestamp;

  private final SharedSQLiteStatement __preparedStmtOfIncrementProgress;

  private final SharedSQLiteStatement __preparedStmtOfMarkAsCompleted;

  private final SharedSQLiteStatement __preparedStmtOfResetProgress;

  public AzkarProgressDao_Impl(RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAzkarProgressEntity = new EntityInsertionAdapter<AzkarProgressEntity>(__db) {
      @Override
      public String createQuery() {
        return "INSERT OR REPLACE INTO `azkar_progress_table` (`id`,`azkar_id`,`current_count`,`target_count`,`is_completed`,`date`,`start_time`,`completion_time`,`session_duration`,`created_at`,`updated_at`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AzkarProgressEntity value) {
        stmt.bindLong(1, value.getId());
        stmt.bindLong(2, value.getAzkarId());
        stmt.bindLong(3, value.getCurrentCount());
        stmt.bindLong(4, value.getTargetCount());
        final int _tmp = value.isCompleted() ? 1 : 0;
        stmt.bindLong(5, _tmp);
        if (value.getDate() == null) {
          stmt.bindNull(6);
        } else {
          stmt.bindString(6, value.getDate());
        }
        stmt.bindLong(7, value.getStartTime());
        stmt.bindLong(8, value.getCompletionTime());
        stmt.bindLong(9, value.getSessionDuration());
        stmt.bindLong(10, value.getCreatedAt());
        stmt.bindLong(11, value.getUpdatedAt());
      }
    };
    this.__deletionAdapterOfAzkarProgressEntity = new EntityDeletionOrUpdateAdapter<AzkarProgressEntity>(__db) {
      @Override
      public String createQuery() {
        return "DELETE FROM `azkar_progress_table` WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AzkarProgressEntity value) {
        stmt.bindLong(1, value.getId());
      }
    };
    this.__updateAdapterOfAzkarProgressEntity = new EntityDeletionOrUpdateAdapter<AzkarProgressEntity>(__db) {
      @Override
      public String createQuery() {
        return "UPDATE OR ABORT `azkar_progress_table` SET `id` = ?,`azkar_id` = ?,`current_count` = ?,`target_count` = ?,`is_completed` = ?,`date` = ?,`start_time` = ?,`completion_time` = ?,`session_duration` = ?,`created_at` = ?,`updated_at` = ? WHERE `id` = ?";
      }

      @Override
      public void bind(SupportSQLiteStatement stmt, AzkarProgressEntity value) {
        stmt.bindLong(1, value.getId());
        stmt.bindLong(2, value.getAzkarId());
        stmt.bindLong(3, value.getCurrentCount());
        stmt.bindLong(4, value.getTargetCount());
        final int _tmp = value.isCompleted() ? 1 : 0;
        stmt.bindLong(5, _tmp);
        if (value.getDate() == null) {
          stmt.bindNull(6);
        } else {
          stmt.bindString(6, value.getDate());
        }
        stmt.bindLong(7, value.getStartTime());
        stmt.bindLong(8, value.getCompletionTime());
        stmt.bindLong(9, value.getSessionDuration());
        stmt.bindLong(10, value.getCreatedAt());
        stmt.bindLong(11, value.getUpdatedAt());
        stmt.bindLong(12, value.getId());
      }
    };
    this.__preparedStmtOfDeleteProgressById = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_progress_table WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteProgressByAzkarId = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_progress_table WHERE azkar_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteProgressByDate = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_progress_table WHERE date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteProgressOlderThan = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_progress_table WHERE date < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllProgress = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "DELETE FROM azkar_progress_table";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateDateTimestamp = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_progress_table SET updated_at = ? WHERE date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementProgress = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_progress_table SET current_count = current_count + 1, updated_at = ? WHERE azkar_id = ? AND date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfMarkAsCompleted = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_progress_table SET is_completed = 1, completion_time = ?, session_duration = ?, updated_at = ? WHERE azkar_id = ? AND date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfResetProgress = new SharedSQLiteStatement(__db) {
      @Override
      public String createQuery() {
        final String _query = "UPDATE azkar_progress_table SET current_count = 0, is_completed = 0, start_time = 0, completion_time = 0, session_duration = 0, updated_at = ? WHERE azkar_id = ? AND date = ?";
        return _query;
      }
    };
  }

  @Override
  public long insertProgress(final AzkarProgressEntity progress) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      long _result = __insertionAdapterOfAzkarProgressEntity.insertAndReturnId(progress);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Long> insertAllProgress(final List<AzkarProgressEntity> progressList) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      List<Long> _result = __insertionAdapterOfAzkarProgressEntity.insertAndReturnIdsList(progressList);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int deleteProgress(final AzkarProgressEntity progress) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total +=__deletionAdapterOfAzkarProgressEntity.handle(progress);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int updateProgress(final AzkarProgressEntity progress) {
    __db.assertNotSuspendingTransaction();
    int _total = 0;
    __db.beginTransaction();
    try {
      _total +=__updateAdapterOfAzkarProgressEntity.handle(progress);
      __db.setTransactionSuccessful();
      return _total;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public int deleteProgressById(final int progressId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProgressById.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, progressId);
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteProgressById.release(_stmt);
    }
  }

  @Override
  public int deleteProgressByAzkarId(final int azkarId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProgressByAzkarId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, azkarId);
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteProgressByAzkarId.release(_stmt);
    }
  }

  @Override
  public int deleteProgressByDate(final String date) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProgressByDate.acquire();
    int _argIndex = 1;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteProgressByDate.release(_stmt);
    }
  }

  @Override
  public int deleteProgressOlderThan(final String date) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProgressOlderThan.acquire();
    int _argIndex = 1;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteProgressOlderThan.release(_stmt);
    }
  }

  @Override
  public int deleteAllProgress() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllProgress.acquire();
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfDeleteAllProgress.release(_stmt);
    }
  }

  @Override
  public int updateDateTimestamp(final String date, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateDateTimestamp.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfUpdateDateTimestamp.release(_stmt);
    }
  }

  @Override
  public int incrementProgress(final int azkarId, final String date, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementProgress.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, azkarId);
    _argIndex = 3;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfIncrementProgress.release(_stmt);
    }
  }

  @Override
  public int markAsCompleted(final int azkarId, final String date, final long completionTime,
      final long sessionDuration, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfMarkAsCompleted.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, completionTime);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, sessionDuration);
    _argIndex = 3;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 4;
    _stmt.bindLong(_argIndex, azkarId);
    _argIndex = 5;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfMarkAsCompleted.release(_stmt);
    }
  }

  @Override
  public int resetProgress(final int azkarId, final String date, final long timestamp) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfResetProgress.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, timestamp);
    _argIndex = 2;
    _stmt.bindLong(_argIndex, azkarId);
    _argIndex = 3;
    if (date == null) {
      _stmt.bindNull(_argIndex);
    } else {
      _stmt.bindString(_argIndex, date);
    }
    __db.beginTransaction();
    try {
      final int _result = _stmt.executeUpdateDelete();
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
      __preparedStmtOfResetProgress.release(_stmt);
    }
  }

  @Override
  public AzkarProgressEntity getProgressById(final int progressId) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, progressId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AzkarProgressEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _result.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _result.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _result.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _result.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _result.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _result.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _result.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _result.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public AzkarProgressEntity getProgressByAzkarAndDate(final int azkarId, final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE azkar_id = ? AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, azkarId);
    _argIndex = 2;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AzkarProgressEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _result.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _result.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _result.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _result.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _result.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _result.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _result.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _result.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<AzkarProgressEntity> getProgressByAzkarAndDateLiveData(final int azkarId,
      final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE azkar_id = ? AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, azkarId);
    _argIndex = 2;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<AzkarProgressEntity>() {
      @Override
      public AzkarProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final AzkarProgressEntity _result;
          if(_cursor.moveToFirst()) {
            _result = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _result.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _result.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _result.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _result.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _result.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _result.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _result.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _result.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result.setUpdatedAt(_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarProgressEntity> getProgressByDate(final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE date = ? ORDER BY azkar_id";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarProgressEntity>> getProgressByDateLiveData(final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE date = ? ORDER BY azkar_id";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<List<AzkarProgressEntity>>() {
      @Override
      public List<AzkarProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarProgressEntity _item;
            _item = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _item.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _item.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _item.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _item.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _item.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _item.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarProgressEntity> getProgressByAzkarId(final int azkarId) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE azkar_id = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, azkarId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public AzkarProgressEntity getProgressByAzkarId(final String azkarId) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE azkar_id = ? ORDER BY date DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (azkarId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, azkarId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AzkarProgressEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _result.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _result.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _result.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _result.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _result.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _result.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _result.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _result.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarProgressEntity>> getProgressByAzkarIdLiveData(final int azkarId) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE azkar_id = ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, azkarId);
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<List<AzkarProgressEntity>>() {
      @Override
      public List<AzkarProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarProgressEntity _item;
            _item = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _item.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _item.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _item.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _item.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _item.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _item.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarProgressEntity> getAllProgress() {
    final String _sql = "SELECT * FROM azkar_progress_table ORDER BY date DESC, azkar_id";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarProgressEntity>> getAllProgressLiveData() {
    final String _sql = "SELECT * FROM azkar_progress_table ORDER BY date DESC, azkar_id";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<List<AzkarProgressEntity>>() {
      @Override
      public List<AzkarProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarProgressEntity _item;
            _item = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _item.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _item.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _item.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _item.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _item.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _item.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarProgressEntity> getCompletedProgressByDate(final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE is_completed = 1 AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarProgressEntity>> getCompletedProgressByDateLiveData(final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE is_completed = 1 AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<List<AzkarProgressEntity>>() {
      @Override
      public List<AzkarProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarProgressEntity _item;
            _item = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _item.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _item.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _item.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _item.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _item.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _item.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<AzkarProgressEntity> getIncompleteProgressByDate(final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE is_completed = 0 AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarProgressEntity>> getIncompleteProgressByDateLiveData(
      final String date) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE is_completed = 0 AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<List<AzkarProgressEntity>>() {
      @Override
      public List<AzkarProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarProgressEntity _item;
            _item = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _item.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _item.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _item.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _item.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _item.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _item.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getProgressCountByDate(final String date) {
    final String _sql = "SELECT COUNT(*) FROM azkar_progress_table WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getCompletedCountByDate(final String date) {
    final String _sql = "SELECT COUNT(*) FROM azkar_progress_table WHERE date = ? AND is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getIncompleteCountByDate(final String date) {
    final String _sql = "SELECT COUNT(*) FROM azkar_progress_table WHERE date = ? AND is_completed = 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getCompletedCountByAzkarId(final int azkarId) {
    final String _sql = "SELECT COUNT(*) FROM azkar_progress_table WHERE azkar_id = ? AND is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, azkarId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTotalCompletedCount() {
    final String _sql = "SELECT SUM(current_count) FROM azkar_progress_table WHERE is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getAverageProgressByDate(final String date) {
    final String _sql = "SELECT AVG(current_count) FROM azkar_progress_table WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getTotalCountByDate(final String date) {
    final String _sql = "SELECT SUM(current_count) FROM azkar_progress_table WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public double getAverageSessionDurationByDate(final String date) {
    final String _sql = "SELECT AVG(session_duration) FROM azkar_progress_table WHERE date = ? AND is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final double _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getDouble(0);
      } else {
        _result = 0.0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public long getTotalSessionDurationByDate(final String date) {
    final String _sql = "SELECT SUM(session_duration) FROM azkar_progress_table WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final long _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getLong(0);
      } else {
        _result = 0L;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarProgressEntity> getProgressBetweenDates(final String startDate,
      final String endDate) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<AzkarProgressEntity>> getProgressBetweenDatesLiveData(final String startDate,
      final String endDate) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE date BETWEEN ? AND ? ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    return __db.getInvalidationTracker().createLiveData(new String[]{"azkar_progress_table"}, false, new Callable<List<AzkarProgressEntity>>() {
      @Override
      public List<AzkarProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
          final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
          final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
          while(_cursor.moveToNext()) {
            final AzkarProgressEntity _item;
            _item = new AzkarProgressEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpAzkarId;
            _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
            _item.setAzkarId(_tmpAzkarId);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            _item.setCurrentCount(_tmpCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            _item.setTargetCount(_tmpTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            _item.setCompleted(_tmpIsCompleted);
            final String _tmpDate;
            if (_cursor.isNull(_cursorIndexOfDate)) {
              _tmpDate = null;
            } else {
              _tmpDate = _cursor.getString(_cursorIndexOfDate);
            }
            _item.setDate(_tmpDate);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            _item.setStartTime(_tmpStartTime);
            final long _tmpCompletionTime;
            _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
            _item.setCompletionTime(_tmpCompletionTime);
            final long _tmpSessionDuration;
            _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
            _item.setSessionDuration(_tmpSessionDuration);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item.setCreatedAt(_tmpCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item.setUpdatedAt(_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public int getCompletedCountBetweenDates(final String startDate, final String endDate) {
    final String _sql = "SELECT COUNT(*) FROM azkar_progress_table WHERE date BETWEEN ? AND ? AND is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarProgressEntity> getRecentProgress(final int limit) {
    final String _sql = "SELECT * FROM azkar_progress_table ORDER BY updated_at DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarProgressEntity> getRecentCompletions(final int limit) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE is_completed = 1 ORDER BY completion_time DESC LIMIT ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, limit);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<String> getCompletionDates() {
    final String _sql = "SELECT DISTINCT date FROM azkar_progress_table WHERE is_completed = 1 ORDER BY date DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final List<String> _result = new ArrayList<String>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final String _item;
        if (_cursor.isNull(0)) {
          _item = null;
        } else {
          _item = _cursor.getString(0);
        }
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int getCompletionDaysCount(final String startDate, final String endDate) {
    final String _sql = "SELECT COUNT(DISTINCT date) FROM azkar_progress_table WHERE is_completed = 1 AND date BETWEEN ? AND ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (startDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, startDate);
    }
    _argIndex = 2;
    if (endDate == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, endDate);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public long getBestCompletionTime() {
    final String _sql = "SELECT MIN(session_duration) FROM azkar_progress_table WHERE is_completed = 1 AND session_duration > 0";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final long _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getLong(0);
      } else {
        _result = 0L;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public long getWorstCompletionTime() {
    final String _sql = "SELECT MAX(session_duration) FROM azkar_progress_table WHERE is_completed = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final long _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getLong(0);
      } else {
        _result = 0L;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public AzkarProgressEntity getBestPerformance() {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE session_duration = (SELECT MIN(session_duration) FROM azkar_progress_table WHERE is_completed = 1 AND session_duration > 0)";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final AzkarProgressEntity _result;
      if(_cursor.moveToFirst()) {
        _result = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _result.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _result.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _result.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _result.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _result.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _result.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _result.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _result.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _result.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _result.setUpdatedAt(_tmpUpdatedAt);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<AzkarProgressEntity> getProgressOlderThan(final long timestamp) {
    final String _sql = "SELECT * FROM azkar_progress_table WHERE updated_at < ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, timestamp);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfAzkarId = CursorUtil.getColumnIndexOrThrow(_cursor, "azkar_id");
      final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "current_count");
      final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "target_count");
      final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "is_completed");
      final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
      final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "start_time");
      final int _cursorIndexOfCompletionTime = CursorUtil.getColumnIndexOrThrow(_cursor, "completion_time");
      final int _cursorIndexOfSessionDuration = CursorUtil.getColumnIndexOrThrow(_cursor, "session_duration");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final List<AzkarProgressEntity> _result = new ArrayList<AzkarProgressEntity>(_cursor.getCount());
      while(_cursor.moveToNext()) {
        final AzkarProgressEntity _item;
        _item = new AzkarProgressEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpAzkarId;
        _tmpAzkarId = _cursor.getInt(_cursorIndexOfAzkarId);
        _item.setAzkarId(_tmpAzkarId);
        final int _tmpCurrentCount;
        _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
        _item.setCurrentCount(_tmpCurrentCount);
        final int _tmpTargetCount;
        _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
        _item.setTargetCount(_tmpTargetCount);
        final boolean _tmpIsCompleted;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
        _tmpIsCompleted = _tmp != 0;
        _item.setCompleted(_tmpIsCompleted);
        final String _tmpDate;
        if (_cursor.isNull(_cursorIndexOfDate)) {
          _tmpDate = null;
        } else {
          _tmpDate = _cursor.getString(_cursorIndexOfDate);
        }
        _item.setDate(_tmpDate);
        final long _tmpStartTime;
        _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
        _item.setStartTime(_tmpStartTime);
        final long _tmpCompletionTime;
        _tmpCompletionTime = _cursor.getLong(_cursorIndexOfCompletionTime);
        _item.setCompletionTime(_tmpCompletionTime);
        final long _tmpSessionDuration;
        _tmpSessionDuration = _cursor.getLong(_cursorIndexOfSessionDuration);
        _item.setSessionDuration(_tmpSessionDuration);
        final long _tmpCreatedAt;
        _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
        _item.setCreatedAt(_tmpCreatedAt);
        final long _tmpUpdatedAt;
        _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
        _item.setUpdatedAt(_tmpUpdatedAt);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public int checkProgressExists(final int azkarId, final String date) {
    final String _sql = "SELECT COUNT(*) FROM azkar_progress_table WHERE azkar_id = ? AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, azkarId);
    _argIndex = 2;
    if (date == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, date);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _result;
      if(_cursor.moveToFirst()) {
        _result = _cursor.getInt(0);
      } else {
        _result = 0;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}

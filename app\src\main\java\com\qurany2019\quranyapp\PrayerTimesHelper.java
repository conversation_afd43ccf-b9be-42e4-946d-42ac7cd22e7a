package com.qurany2019.quranyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.util.Log;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import org.json.JSONObject;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class PrayerTimesHelper {
    
    private static final String TAG = "PrayerTimesHelper";
    private Context context;
    
    public interface PrayerTimesCallback {
        void onSuccess(PrayerTimes prayerTimes);
        void onError(String error);
    }
    
    public static class PrayerTimes {
        public String fajr;
        public String dhuhr;
        public String asr;
        public String maghrib;
        public String isha;
        public String cityName;
        
        public PrayerTimes(String fajr, String dhuhr, String asr, String maghrib, String isha, String cityName) {
            this.fajr = fajr;
            this.dhuhr = dhuhr;
            this.asr = asr;
            this.maghrib = maghrib;
            this.isha = isha;
            this.cityName = cityName;
        }
    }
    
    public PrayerTimesHelper(Context context) {
        this.context = context;
    }
    
    public void getPrayerTimes(double latitude, double longitude, String cityName, PrayerTimesCallback callback) {
        new FetchPrayerTimesTask(callback, cityName).execute(latitude, longitude);
    }
    
    public PrayerTimes getSavedPrayerTimes() {
        SharedPreferences prefs = context.getSharedPreferences("prayer_times", Context.MODE_PRIVATE);
        String fajr = prefs.getString("fajr", "5:46");
        String dhuhr = prefs.getString("dhuhr", "12:26");
        String asr = prefs.getString("asr", "3:46");
        String maghrib = prefs.getString("maghrib", "7:11");
        String isha = prefs.getString("isha", "8:30");
        String cityName = prefs.getString("city_name", "الرياض");
        
        return new PrayerTimes(fajr, dhuhr, asr, maghrib, isha, cityName);
    }
    
    public void savePrayerTimes(PrayerTimes prayerTimes) {
        SharedPreferences prefs = context.getSharedPreferences("prayer_times", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putString("fajr", prayerTimes.fajr);
        editor.putString("dhuhr", prayerTimes.dhuhr);
        editor.putString("asr", prayerTimes.asr);
        editor.putString("maghrib", prayerTimes.maghrib);
        editor.putString("isha", prayerTimes.isha);
        editor.putString("city_name", prayerTimes.cityName);
        editor.apply();
    }
    
    private class FetchPrayerTimesTask extends AsyncTask<Double, Void, PrayerTimes> {
        private PrayerTimesCallback callback;
        private String cityName;
        private String errorMessage;
        
        public FetchPrayerTimesTask(PrayerTimesCallback callback, String cityName) {
            this.callback = callback;
            this.cityName = cityName;
        }
        
        @Override
        protected PrayerTimes doInBackground(Double... params) {
            try {
                double latitude = params[0];
                double longitude = params[1];
                
                // تنسيق التاريخ الحالي
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy", Locale.ENGLISH);
                String currentDate = dateFormat.format(Calendar.getInstance().getTime());
                
                // بناء رابط API
                String urlString = String.format(Locale.ENGLISH,
                    "http://api.aladhan.com/v1/timings/%s?latitude=%f&longitude=%f&method=4",
                    currentDate, latitude, longitude);
                
                Log.d(TAG, "Fetching prayer times from: " + urlString);
                
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                
                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    // تحليل JSON
                    JSONObject jsonResponse = new JSONObject(response.toString());
                    JSONObject data = jsonResponse.getJSONObject("data");
                    JSONObject timings = data.getJSONObject("timings");
                    
                    String fajr = extractTime(timings.getString("Fajr"));
                    String dhuhr = extractTime(timings.getString("Dhuhr"));
                    String asr = extractTime(timings.getString("Asr"));
                    String maghrib = extractTime(timings.getString("Maghrib"));
                    String isha = extractTime(timings.getString("Isha"));
                    
                    return new PrayerTimes(fajr, dhuhr, asr, maghrib, isha, cityName);
                } else {
                    errorMessage = "HTTP Error: " + responseCode;
                    return null;
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error fetching prayer times", e);
                errorMessage = e.getMessage();
                return null;
            }
        }
        
        @Override
        protected void onPostExecute(PrayerTimes result) {
            if (result != null) {
                savePrayerTimes(result);
                callback.onSuccess(result);
            } else {
                callback.onError(errorMessage != null ? errorMessage : "Unknown error");
            }
        }
        
        private String extractTime(String timeString) {
            try {
                // إزالة المنطقة الزمنية والاحتفاظ بالوقت فقط
                if (timeString.contains(" ")) {
                    return timeString.split(" ")[0];
                }
                return timeString;
            } catch (Exception e) {
                return timeString;
            }
        }
    }
}

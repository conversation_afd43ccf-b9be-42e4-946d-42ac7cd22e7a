<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layoutDirection="rtl"
    android:padding="6dp">

    <!-- بطاقة القارئ الجديدة -->
    <LinearLayout
        android:id="@+id/card_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@drawable/modern_card_background"
        android:foreground="?android:attr/selectableItemBackgroundBorderless"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:layoutDirection="rtl">

        <!-- أيقونة القرآن -->
        <LinearLayout
            android:layout_width="56dp"
            android:layout_height="56dp"
            android:background="@drawable/circle_background_islamic"
            android:gravity="center"
            android:layout_marginLeft="16dp">

            <ImageView
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:src="@drawable/quran_recites2"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- معلومات القارئ -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginRight="16dp">

            <!-- اسم القارئ -->
            <TextView
                android:id="@+id/txtRecitesName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="اسم القارئ"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/islamicTextPrimary"
                android:gravity="right"
                android:layout_gravity="right"
                android:fontFamily="sans-serif-medium" />

            <!-- وصف إضافي -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/quran_reader_title"
                android:textSize="13sp"
                android:textColor="@color/textSecondary"
                android:layout_marginTop="4dp"
                android:gravity="right"
                android:layout_gravity="right" />

        </LinearLayout>

        <!-- سهم الانتقال -->
        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/backirrow"
            android:scaleType="centerInside"
            android:tint="@color/textSecondary"
            android:alpha="0.7" />

    </LinearLayout>

</LinearLayout>

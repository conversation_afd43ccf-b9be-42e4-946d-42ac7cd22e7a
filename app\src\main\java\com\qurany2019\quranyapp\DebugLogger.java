package com.qurany2019.quranyapp;

/**
 * نظام تتبع بسيط للتطبيق
 */
public class DebugLogger {
    
    /**
     * تسجيل معلومات
     */
    public static void log(String tag, String message) {
        try {
            android.util.Log.d(tag, message);
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }
    
    /**
     * تسجيل أخطاء
     */
    public static void error(String tag, String message) {
        try {
            android.util.Log.e(tag, message);
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }
    
    /**
     * فحص حالة الملف
     */
    public static void checkFile(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            log("FILE_CHECK", "📁 ملف: " + filePath);
            log("FILE_CHECK", "   ✅ موجود: " + file.exists());
            if (file.exists()) {
                log("FILE_CHECK", "   📏 الحجم: " + file.length() + " بايت");
                log("FILE_CHECK", "   📖 قابل للقراءة: " + file.canRead());
            }
        } catch (Exception e) {
            error("FILE_CHECK", "خطأ في فحص الملف: " + e.getMessage());
        }
    }
    
    /**
     * فحص حالة MediaPlayer
     */
    public static void checkMediaPlayer(android.media.MediaPlayer mp) {
        try {
            if (mp == null) {
                error("MEDIA_PLAYER", "MediaPlayer is null!");
                return;
            }
            
            log("MEDIA_PLAYER", "🎵 MediaPlayer Status:");
            log("MEDIA_PLAYER", "   ▶️ يعمل: " + mp.isPlaying());
            log("MEDIA_PLAYER", "   ⏱️ المدة الحالية: " + mp.getCurrentPosition() + " ms");
            log("MEDIA_PLAYER", "   ⏱️ المدة الكاملة: " + mp.getDuration() + " ms");
        } catch (Exception e) {
            error("MEDIA_PLAYER", "خطأ في فحص MediaPlayer: " + e.getMessage());
        }
    }
}

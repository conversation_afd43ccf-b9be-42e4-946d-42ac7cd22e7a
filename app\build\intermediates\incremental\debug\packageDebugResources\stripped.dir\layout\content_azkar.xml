<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainScrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/islamic_gradient_bg"
    android:fillViewport="true"
    android:scrollbars="none"
    tools:context="com.qurany2019.quranyapp.AzkarActivity"
    tools:showIn="@layout/activity_azkar">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layoutDirection="rtl"
        android:padding="16dp"
        android:paddingTop="8dp"
        android:paddingBottom="16dp"
        android:clipToPadding="false">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <!-- Notification Icon with Status -->
            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp">

                <ImageView
                    android:id="@+id/notificationIcon"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:src="@drawable/ic_notification"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="6dp"
                    android:tint="@color/azkar_notification_icon_active" />

                <!-- مؤشر حالة الإشعارات -->
                <View
                    android:id="@+id/notificationStatusIndicator"
                    android:layout_width="12dp"
                    android:layout_height="12dp"
                    android:layout_alignEnd="@id/notificationIcon"
                    android:layout_alignTop="@id/notificationIcon"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="2dp"
                    android:background="@drawable/notification_status_indicator"
                    android:visibility="visible" />

            </RelativeLayout>

            <!-- Title -->
            <TextView
                android:id="@+id/azkarTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/azkar_title"
                android:textSize="26sp"
                android:textColor="@color/azkar_header_text_light"
                android:fontFamily="@font/arabic_font"
                android:gravity="center"
                android:textStyle="bold" />

            <!-- Night/Day Toggle -->
            <ImageView
                android:id="@+id/nightDayToggle"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:src="@drawable/ic_moon"
                android:layout_marginStart="16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:tint="@color/azkar_night_day_icon" />

        </LinearLayout>

        <!-- شبكة بطاقات الأذكار -->
        <GridLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:columnCount="2"
            android:rowCount="3"
            android:alignmentMode="alignBounds"
            android:useDefaultMargins="false"
            android:layout_marginTop="8dp">

            <!-- بطاقة أذكار الصباح -->
            <androidx.cardview.widget.CardView
                android:id="@+id/morningAzkarCard"
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="@color/azkar_morning_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_sun_morning"
                        android:layout_marginBottom="8dp"
                        android:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/azkar_morning"
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:fontFamily="@font/arabic_font"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- بطاقة أذكار المساء -->
            <androidx.cardview.widget.CardView
                android:id="@+id/eveningAzkarCard"
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="@color/azkar_evening_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_moon_evening"
                        android:layout_marginBottom="8dp"
                        android:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/azkar_evening"
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:fontFamily="@font/arabic_font"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- بطاقة أذكار الاستيقاظ -->
            <androidx.cardview.widget.CardView
                android:id="@+id/wakeAzkarCard"
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="@color/azkar_wake_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_wake_up"
                        android:layout_marginBottom="8dp"
                        android:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/azkar_wake"
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:fontFamily="@font/arabic_font"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- بطاقة أذكار النوم -->
            <androidx.cardview.widget.CardView
                android:id="@+id/sleepAzkarCard"
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="@color/azkar_sleep_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="12dp">

                    <ImageView
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_sleep"
                        android:layout_marginBottom="8dp"
                        android:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/azkar_sleep"
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:fontFamily="@font/arabic_font"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:maxLines="2"
                        android:ellipsize="end" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- بطاقة أذكار بعد الصلاة -->
            <androidx.cardview.widget.CardView
                android:id="@+id/prayerAzkarCard"
                android:layout_width="0dp"
                android:layout_height="140dp"
                android:layout_columnWeight="1"
                android:layout_margin="8dp"
                android:layout_columnSpan="2"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="16dp"
                app:cardElevation="6dp"
                app:cardBackgroundColor="@color/azkar_prayer_card">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:src="@drawable/ic_prayer_mat"
                        android:layout_marginEnd="16dp"
                        android:tint="@color/white" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/azkar_prayer"
                        android:textSize="18sp"
                        android:textColor="@color/white"
                        android:fontFamily="@font/arabic_font"
                        android:textStyle="bold"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </GridLayout>

    </LinearLayout>

</ScrollView>

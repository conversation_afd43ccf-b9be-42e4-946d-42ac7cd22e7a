package com.qurany2019.quranyapp;

import android.Manifest;
import android.animation.ObjectAnimator;
import android.text.TextWatcher;
import android.text.Editable;
import android.widget.EditText;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.SearchView;
import android.widget.TextView;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;
import com.google.android.material.appbar.CollapsingToolbarLayout;


import java.util.ArrayList;

/**
 * Created by java_dude on 06/06/18.
 */
public class RecitesName extends AppCompatActivity {
   public ArrayList<AuthorClass> listrecites = new ArrayList<AuthorClass>();
    ListView lVRecites;
    private AdView mAdView;



    String RecitesName="";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق إعدادات اللغة المحفوظة
        applyLanguageSettings();

        // إزالة fullscreen mode لإظهار شريط الأزرار بشكل طبيعي
        setContentView(R.layout.activity_recites_name);
        lVRecites = (ListView) findViewById(R.id.listView);

        // طباعة قيمة اللغة للتأكد
        android.util.Log.d("RecitesName", "SaveSettings.LanguageSelect = " + SaveSettings.LanguageSelect);

        //get list of recites
        LnaguageClass lc = new LnaguageClass();
        listrecites = lc.AutherList();

        // طباعة عدد القراء للتأكد من التحميل
        android.util.Log.d("RecitesName", "عدد القراء المحملين: " + listrecites.size());

        // إذا كانت القائمة فارغة، أضف قراء يدوياً للاختبار
        if (listrecites.size() == 0) {
            android.util.Log.d("RecitesName", "القائمة فارغة! إضافة قراء للاختبار...");
            listrecites.add(new AuthorClass("basit", "عبد الباسط عبد الصمد"));
            listrecites.add(new AuthorClass("afs", "مشاري العفاسي"));
            listrecites.add(new AuthorClass("maher", "ماهر المعيقلي"));
            listrecites.add(new AuthorClass("shur", "سعود الشريم"));
            listrecites.add(new AuthorClass("sds", "عبدالرحمن السديس"));
            android.util.Log.d("RecitesName", "تم إضافة " + listrecites.size() + " قراء للاختبار");
        }

        lVRecites.setAdapter(new VivzAdapter(listrecites));
        lVRecites.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @RequiresApi(api = Build.VERSION_CODES.M)
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                //load recites aya
                TextView txtRecitesName = (TextView) view.findViewById(R.id.txtRecitesName);
               // AuthorClass temp = listrecites.get(position);
                for (AuthorClass listrecitesitem : listrecites) {
                    if (listrecitesitem.RealName.equals(txtRecitesName.getText())) {
                        RecitesName = listrecitesitem.ServerName;
                       // String welcomes = listrecitesitem.ServerName;

                        // التحقق من إصدار API قبل استدعاء DisplayAya
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                            DisplayAya();
                        } else {
                            // للإصدارات الأقدم، انتقل مباشرة إلى ListAya
                            ListAya();
                        }
                        break;
                    }
                }

            }
        });

        // إعداد البحث للـ EditText الجديد
        EditText searchEditText = findViewById(R.id.searchEditText);
        if (searchEditText != null) {
            searchEditText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {}

                @Override
                public void afterTextChanged(Editable s) {
                    String searchText = s.toString().trim();
                    ArrayList<AuthorClass> filteredList = new ArrayList<>();

                    if (searchText.isEmpty()) {
                        // إذا كان البحث فارغ، أظهر جميع القراء
                        filteredList.addAll(listrecites);
                    } else {
                        // فلترة القراء حسب النص المدخل
                        for (AuthorClass reciter : listrecites) {
                            if (reciter.RealName.contains(searchText)) {
                                filteredList.add(reciter);
                            }
                        }
                    }

                    // تحديث الـ ListView بالنتائج المفلترة
                    lVRecites.setAdapter(new VivzAdapter(filteredList));
                    android.util.Log.d("RecitesName", "البحث: " + searchText + " - النتائج: " + filteredList.size());
                }
            });
        }
        if(SaveSettings.OnTimeAds==false) {
            if(SaveSettings.IsRated==1){
            SaveSettings.OnTimeAds=true;
            }

        }
        mAdView = (AdView) findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
    }


    @RequiresApi(api = Build.VERSION_CODES.M)
    private void DisplayAya() {
        android.util.Log.d("VivzAdapter", "DisplayAya() تم استدعاؤها");

        //check permison
        if ((int) Build.VERSION.SDK_INT >= 23)
        {
            if ((ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) !=
                    PackageManager.PERMISSION_GRANTED)||
                    ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) !=
                            PackageManager.PERMISSION_GRANTED)
            {
                android.util.Log.d("VivzAdapter", "طلب صلاحيات التخزين");
                requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,Manifest.permission.READ_EXTERNAL_STORAGE},
                            REQUEST_CODE_ASK_PERMISSIONS);

                return;
            }

        }
        //permisons
        android.util.Log.d("VivzAdapter", "الصلاحيات متوفرة، استدعاء ListAya()");
        ListAya();
    }
    void ListAya(){
        android.util.Log.d("VivzAdapter", "ListAya() تم استدعاؤها، RecitesName = " + RecitesName);
        try{
            if(    RecitesName.length()>1){
                android.util.Log.d("VivzAdapter", "إنشاء Intent للانتقال إلى AyaList");
                Intent intent = new Intent(this, AyaList.class);
                intent.putExtra("RecitesName",RecitesName);
                android.util.Log.d("VivzAdapter", "بدء Activity");
                startActivity(intent);
            } else {
                android.util.Log.d("VivzAdapter", "RecitesName قصير جداً: " + RecitesName);
            }
        }catch (Exception ex) {
            android.util.Log.e("VivzAdapter", "خطأ في ListAya(): " + ex.getMessage());
        }
    }




    SearchView searchView;
    Menu myMenu;
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_recites_name, menu);
        myMenu=menu;
        // Associate searchable configuration with the SearchView
        // SearchManager searchManager = (SearchManager) getSystemService(Context.SEARCH_SERVICE); // REMOVED: Causes system to scan all apps
        searchView = (SearchView) menu.findItem(R.id.search).getActionView();
        // searchView.setSearchableInfo(searchManager.getSearchableInfo(getComponentName())); // REMOVED: Causes system to scan all apps
        //final Context co=this;
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {

                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                ArrayList<AuthorClass> listrecitestemp = new ArrayList<AuthorClass>();
                for ( AuthorClass  listrecitesitem:  listrecites) {
                    if (listrecitesitem.RealName.contains(newText)) {
                        listrecitestemp.add(listrecitesitem);

                    }
                }
                lVRecites.setAdapter(new VivzAdapter(listrecitestemp));
                    return false;
            }
        });
        //   searchView.setOnCloseListener(this);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        if (id == R.id.menu) {
            Intent intent = new Intent(this, Sellings.class);
            startActivity(intent);

        }


        return super.onOptionsItemSelected(item);
    }




    class VivzAdapter extends BaseAdapter {

        ArrayList<AuthorClass> listrecitesLocal;

        VivzAdapter(ArrayList<AuthorClass> listrecites) {

            listrecitesLocal = new ArrayList<AuthorClass>();
            listrecitesLocal = listrecites;

        }


        @Override
        public int getCount() {
            android.util.Log.d("VivzAdapter", "getCount() called, size = " + listrecitesLocal.size());
            return listrecitesLocal.size();
        }

        @Override
        public String getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            android.util.Log.d("VivzAdapter", "getView() called for position " + position + " of " + listrecitesLocal.size());

            LayoutInflater mInflater = getLayoutInflater();
            View myView = mInflater.inflate(R.layout.recites_ticket, null);
            TextView txtRecitesName = (TextView) myView.findViewById(R.id.txtRecitesName);
            View cardLayout = myView.findViewById(R.id.card_layout);
            AuthorClass temp = listrecitesLocal.get(position);
            txtRecitesName.setText(temp.RealName);

            // إضافة النقر للبطاقة مع تأثير تفاعلي بسيط
            cardLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // تأثير النقر - تصغير وتكبير سريع
                    v.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .setDuration(100)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                v.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(100)
                                    .start();
                            }
                        })
                        .start();

                    // منطق النقر
                    RecitesName = temp.ServerName;
                    android.util.Log.d("VivzAdapter", "تم النقر على القارئ: " + temp.RealName + " - " + temp.ServerName);

                    // التحقق من إصدار API قبل استدعاء DisplayAya
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        DisplayAya();
                    } else {
                        // للإصدارات الأقدم، انتقل مباشرة إلى ListAya
                        ListAya();
                    }
                }
            });

            android.util.Log.d("VivzAdapter", "عرض القارئ: " + temp.RealName);
            return myView;

        }


    }

    //get access to mailbox
    final private int REQUEST_CODE_ASK_PERMISSIONS = 123;
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults)
    {
        android.util.Log.d("VivzAdapter", "onRequestPermissionsResult() تم استدعاؤها، RecitesName = " + RecitesName);
        switch (requestCode)
        {
            case REQUEST_CODE_ASK_PERMISSIONS:
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED)
                {
                    android.util.Log.d("VivzAdapter", "تم منح الصلاحيات، استدعاء ListAya()");
                    ListAya();
                } else {
                    // Permission Denied
                    android.util.Log.d("VivzAdapter", "تم رفض الصلاحيات، استدعاء ListAya() على أي حال");
                    ListAya();
                }
                break;
            default:
                super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    // دالة تطبيق إعدادات اللغة
    private void applyLanguageSettings() {
        try {
            android.content.SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
            boolean isArabic = langPrefs.getBoolean("is_arabic", true);

            // تحديث SaveSettings.LanguageSelect ليتطابق مع اللغة المحفوظة
            SaveSettings.LanguageSelect = isArabic ? 1 : 2;

            setLocale(isArabic ? "ar" : "en");
        } catch (Exception e) {
            // في حالة فشل تطبيق اللغة، استخدم الافتراضي
            SaveSettings.LanguageSelect = 1; // افتراضي عربي
        }
    }

    private void setLocale(String languageCode) {
        try {
            java.util.Locale locale = new java.util.Locale(languageCode);
            java.util.Locale.setDefault(locale);
            android.content.res.Configuration config = new android.content.res.Configuration();
            config.locale = locale;
            getResources().updateConfiguration(config, getResources().getDisplayMetrics());
        } catch (Exception e) {
            // في حالة فشل تطبيق اللغة، استخدم الافتراضي
        }
    }
}

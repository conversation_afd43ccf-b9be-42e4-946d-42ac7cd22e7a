package com.qurany2019.quranyapp;

import android.app.AlarmManager;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;
import java.util.Calendar;

/**
 * مساعد إشعارات الأذكار الذكي
 */
public class AzkarNotificationHelper {
    
    private static final String TAG = "AzkarNotification";
    private static final String CHANNEL_ID = "azkar_notifications";
    private static final String PREFS_NAME = "azkar_notifications_prefs";
    
    // أنواع الأذكار
    public static final String AZKAR_MORNING = "morning";
    public static final String AZKAR_EVENING = "evening";
    public static final String AZKAR_SLEEP = "sleep";
    public static final String AZKAR_WAKE = "wake";
    
    private Context context;
    private NotificationManager notificationManager;
    private AlarmManager alarmManager;
    
    public AzkarNotificationHelper(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        this.alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        createNotificationChannel();
    }
    
    /**
     * إنشاء قناة الإشعارات
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "تذكيرات الأذكار",
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription("إشعارات تذكير بأوقات الأذكار");
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{0, 1000, 500, 1000});
            channel.enableLights(true);
            channel.setLightColor(0xFF2E7D32); // أخضر إسلامي
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    /**
     * تحليل الوقت الحالي واقتراح الذكر المناسب
     */
    public String getCurrentAzkarSuggestion() {
        Calendar now = Calendar.getInstance();
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        if (hour >= 6 && hour < 11) {
            return AZKAR_MORNING;
        } else if (hour >= 15 && hour < 19) {
            return AZKAR_EVENING;
        } else if (hour >= 22 || hour < 6) {
            return AZKAR_SLEEP;
        } else {
            // في الأوقات الأخرى، اقترح حسب آخر ذكر لم يتم تفعيله
            return getNextMissedAzkar();
        }
    }
    
    /**
     * الحصول على آخر ذكر لم يتم تفعيله
     */
    private String getNextMissedAzkar() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        // فحص الأذكار حسب الأولوية
        if (!prefs.getBoolean("morning_completed_today", false)) {
            return AZKAR_MORNING;
        } else if (!prefs.getBoolean("evening_completed_today", false)) {
            return AZKAR_EVENING;
        } else if (!prefs.getBoolean("sleep_completed_today", false)) {
            return AZKAR_SLEEP;
        } else {
            return AZKAR_MORNING; // افتراضي
        }
    }
    
    /**
     * تفعيل تذكير ذكي للذكر المقترح
     */
    public void activateSmartReminder() {
        String suggestedAzkar = getCurrentAzkarSuggestion();
        String message = getSmartReminderMessage(suggestedAzkar);
        
        // عرض رسالة فورية
        showInstantNotification("🔔 تذكير الأذكار", message);
        
        // جدولة تذكير لاحق إذا لزم الأمر
        scheduleAzkarReminder(suggestedAzkar);
        
        Log.d(TAG, "Smart reminder activated for: " + suggestedAzkar);
    }
    
    /**
     * الحصول على رسالة التذكير الذكي
     */
    private String getSmartReminderMessage(String azkarType) {
        Calendar now = Calendar.getInstance();
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        switch (azkarType) {
            case AZKAR_MORNING:
                if (hour >= 6 && hour < 11) {
                    return "🌅 حان وقت أذكار الصباح - بارك الله في يومك";
                } else {
                    return "🌅 لم تقرأ أذكار الصباح بعد - اقرأها الآن";
                }
                
            case AZKAR_EVENING:
                if (hour >= 15 && hour < 19) {
                    return "🌆 حان وقت أذكار المساء - تقبل الله منك";
                } else {
                    return "🌆 لم تقرأ أذكار المساء بعد - اقرأها الآن";
                }
                
            case AZKAR_SLEEP:
                if (hour >= 22 || hour < 6) {
                    return "🌙 حان وقت أذكار النوم - ليلة مباركة";
                } else {
                    return "🌙 لا تنس أذكار النوم الليلة";
                }
                
            case AZKAR_WAKE:
                return "☀️ ابدأ يومك بأذكار الاستيقاظ";
                
            default:
                return "📿 حان وقت الأذكار - بارك الله فيك";
        }
    }
    
    /**
     * عرض إشعار فوري
     */
    public void showInstantNotification(String title, String message) {
        Intent intent = new Intent(context, AzkarActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(new NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setVibrate(new long[]{0, 1000, 500, 1000})
            .setLights(0xFF2E7D32, 3000, 3000)
            .setColor(ContextCompat.getColor(context, R.color.islamic_green));
        
        notificationManager.notify(1001, builder.build());
    }
    
    /**
     * جدولة تذكير للذكر
     */
    private void scheduleAzkarReminder(String azkarType) {
        Calendar reminderTime = getNextReminderTime(azkarType);
        
        if (reminderTime != null) {
            Intent intent = new Intent(context, AzkarReminderReceiver.class);
            intent.putExtra("azkar_type", azkarType);
            
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context,
                azkarType.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );
            
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        reminderTime.getTimeInMillis(),
                        pendingIntent
                    );
                } else {
                    alarmManager.setExact(
                        AlarmManager.RTC_WAKEUP,
                        reminderTime.getTimeInMillis(),
                        pendingIntent
                    );
                }
                
                Log.d(TAG, "Scheduled reminder for " + azkarType + " at " + reminderTime.getTime());
                
            } catch (SecurityException e) {
                Log.e(TAG, "Permission denied for exact alarms", e);
            }
        }
    }
    
    /**
     * تحديد وقت التذكير التالي
     */
    private Calendar getNextReminderTime(String azkarType) {
        Calendar calendar = Calendar.getInstance();
        
        switch (azkarType) {
            case AZKAR_MORNING:
                // تذكير في الساعة 7 صباحاً
                calendar.set(Calendar.HOUR_OF_DAY, 7);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                break;
                
            case AZKAR_EVENING:
                // تذكير في الساعة 6 مساءً
                calendar.set(Calendar.HOUR_OF_DAY, 18);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                break;
                
            case AZKAR_SLEEP:
                // تذكير في الساعة 10 مساءً
                calendar.set(Calendar.HOUR_OF_DAY, 22);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                break;
                
            default:
                return null;
        }
        
        // إذا كان الوقت قد مضى اليوم، جدوله لليوم التالي
        Calendar now = Calendar.getInstance();
        if (calendar.before(now)) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }
        
        return calendar;
    }
    
    /**
     * تحديد حالة إكمال الذكر
     */
    public void markAzkarCompleted(String azkarType) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(azkarType + "_completed_today", true);
        editor.putLong(azkarType + "_last_completed", System.currentTimeMillis());
        editor.apply();
        
        Log.d(TAG, "Marked " + azkarType + " as completed");
    }
    
    /**
     * إعادة تعيين حالة الأذكار اليومية (يتم استدعاؤها في بداية كل يوم)
     */
    public void resetDailyAzkarStatus() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("morning_completed_today", false);
        editor.putBoolean("evening_completed_today", false);
        editor.putBoolean("sleep_completed_today", false);
        editor.putBoolean("wake_completed_today", false);
        editor.apply();

        Log.d(TAG, "Daily azkar status reset");
    }

    /**
     * فحص حالة تفعيل الإشعارات
     */
    public boolean areNotificationsEnabled() {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getBoolean("notifications_enabled", true);
    }

    /**
     * تفعيل/إيقاف الإشعارات
     */
    public void setNotificationsEnabled(boolean enabled) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("notifications_enabled", enabled);
        editor.apply();

        if (!enabled) {
            // إلغاء جميع الإشعارات المجدولة
            cancelAllScheduledNotifications();
        }

        Log.d(TAG, "Notifications " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * تبديل حالة الإشعارات
     */
    public boolean toggleNotifications() {
        boolean currentState = areNotificationsEnabled();
        boolean newState = !currentState;
        setNotificationsEnabled(newState);
        return newState;
    }

    /**
     * إلغاء جميع الإشعارات المجدولة
     */
    private void cancelAllScheduledNotifications() {
        String[] azkarTypes = {AZKAR_MORNING, AZKAR_EVENING, AZKAR_SLEEP, AZKAR_WAKE};

        for (String azkarType : azkarTypes) {
            Intent intent = new Intent(context, AzkarReminderReceiver.class);
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context,
                azkarType.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            alarmManager.cancel(pendingIntent);
        }

        Log.d(TAG, "All scheduled notifications cancelled");
    }

    /**
     * الحصول على رسالة حالة الإشعارات
     */
    public String getNotificationStatusMessage() {
        boolean enabled = areNotificationsEnabled();
        if (enabled) {
            return "🔔 الإشعارات مفعلة - اضغط لإيقافها";
        } else {
            return "🔕 الإشعارات معطلة - اضغط لتفعيلها";
        }
    }
}

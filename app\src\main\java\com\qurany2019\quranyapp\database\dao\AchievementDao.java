package com.qurany2019.quranyapp.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.lifecycle.LiveData;

import com.qurany2019.quranyapp.database.entities.AchievementEntity;

import java.util.List;

@Dao
public interface AchievementDao {

    // Insert operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertAchievement(AchievementEntity achievement);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAllAchievements(List<AchievementEntity> achievementsList);

    // Update operations
    @Update
    int updateAchievement(AchievementEntity achievement);

    // Delete operations
    @Delete
    int deleteAchievement(AchievementEntity achievement);

    @Query("DELETE FROM achievements_table WHERE id = :achievementId")
    int deleteAchievementById(int achievementId);

    @Query("DELETE FROM achievements_table WHERE achievement_id = :achievementId")
    int deleteAchievementByAchievementId(String achievementId);

    @Query("DELETE FROM achievements_table WHERE achievement_type = :achievementType")
    int deleteAchievementsByType(String achievementType);

    @Query("DELETE FROM achievements_table WHERE category = :category")
    int deleteAchievementsByCategory(String category);

    @Query("DELETE FROM achievements_table")
    int deleteAllAchievements();

    // Select operations
    @Query("SELECT * FROM achievements_table WHERE id = :achievementId")
    AchievementEntity getAchievementById(int achievementId);

    @Query("SELECT * FROM achievements_table WHERE achievement_id = :achievementId")
    AchievementEntity getAchievementByAchievementId(String achievementId);

    @Query("SELECT * FROM achievements_table WHERE achievement_id = :achievementId")
    LiveData<AchievementEntity> getAchievementByAchievementIdLiveData(String achievementId);

    @Query("SELECT * FROM achievements_table ORDER BY is_unlocked DESC, achievement_type, title")
    List<AchievementEntity> getAllAchievements();

    @Query("SELECT * FROM achievements_table ORDER BY is_unlocked DESC, achievement_type, title")
    LiveData<List<AchievementEntity>> getAllAchievementsLiveData();

    // Filter by unlock status
    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC")
    List<AchievementEntity> getUnlockedAchievements();

    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC")
    LiveData<List<AchievementEntity>> getUnlockedAchievementsLiveData();

    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 0 ORDER BY achievement_type, title")
    List<AchievementEntity> getLockedAchievements();

    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 0 ORDER BY achievement_type, title")
    LiveData<List<AchievementEntity>> getLockedAchievementsLiveData();

    // Filter by type
    @Query("SELECT * FROM achievements_table WHERE achievement_type = :achievementType ORDER BY is_unlocked DESC, title")
    List<AchievementEntity> getAchievementsByType(String achievementType);

    @Query("SELECT * FROM achievements_table WHERE achievement_type = :achievementType ORDER BY is_unlocked DESC, title")
    LiveData<List<AchievementEntity>> getAchievementsByTypeLiveData(String achievementType);

    // Filter by category
    @Query("SELECT * FROM achievements_table WHERE category = :category ORDER BY is_unlocked DESC, achievement_type, title")
    List<AchievementEntity> getAchievementsByCategory(String category);

    @Query("SELECT * FROM achievements_table WHERE category = :category ORDER BY is_unlocked DESC, achievement_type, title")
    LiveData<List<AchievementEntity>> getAchievementsByCategoryLiveData(String category);

    // Progress tracking
    @Query("SELECT * FROM achievements_table WHERE current_value >= target_value AND is_unlocked = 0")
    List<AchievementEntity> getCompletedButNotUnlockedAchievements();

    @Query("SELECT * FROM achievements_table WHERE current_value > 0 AND current_value < target_value ORDER BY (current_value * 100 / target_value) DESC")
    List<AchievementEntity> getInProgressAchievements();

    @Query("SELECT * FROM achievements_table WHERE current_value > 0 AND current_value < target_value ORDER BY (current_value * 100 / target_value) DESC")
    LiveData<List<AchievementEntity>> getInProgressAchievementsLiveData();

    // Count operations
    @Query("SELECT COUNT(*) FROM achievements_table")
    int getTotalAchievementsCount();

    @Query("SELECT COUNT(*) FROM achievements_table")
    int getAchievementCount();

    @Query("SELECT COUNT(*) FROM achievements_table WHERE is_unlocked = 1")
    int getUnlockedAchievementsCount();

    @Query("SELECT COUNT(*) FROM achievements_table WHERE is_unlocked = 0")
    int getLockedAchievementsCount();

    @Query("SELECT COUNT(*) FROM achievements_table WHERE achievement_type = :achievementType")
    int getAchievementsCountByType(String achievementType);

    @Query("SELECT COUNT(*) FROM achievements_table WHERE achievement_type = :achievementType AND is_unlocked = 1")
    int getUnlockedAchievementsCountByType(String achievementType);

    @Query("SELECT COUNT(*) FROM achievements_table WHERE category = :category")
    int getAchievementsCountByCategory(String category);

    @Query("SELECT COUNT(*) FROM achievements_table WHERE category = :category AND is_unlocked = 1")
    int getUnlockedAchievementsCountByCategory(String category);

    // Statistics
    @Query("SELECT SUM(reward_points) FROM achievements_table WHERE is_unlocked = 1")
    int getTotalRewardPoints();

    @Query("SELECT AVG(current_value * 100.0 / target_value) FROM achievements_table WHERE target_value > 0")
    double getOverallProgressPercentage();

    @Query("SELECT AVG(current_value * 100.0 / target_value) FROM achievements_table WHERE achievement_type = :achievementType AND target_value > 0")
    double getProgressPercentageByType(String achievementType);

    // Recent operations
    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC LIMIT :limit")
    List<AchievementEntity> getRecentlyUnlockedAchievements(int limit);

    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 1 ORDER BY unlock_date DESC LIMIT :limit")
    LiveData<List<AchievementEntity>> getRecentlyUnlockedAchievementsLiveData(int limit);

    @Query("SELECT * FROM achievements_table ORDER BY updated_at DESC LIMIT :limit")
    List<AchievementEntity> getRecentlyUpdatedAchievements(int limit);

    // Date range queries
    @Query("SELECT * FROM achievements_table WHERE is_unlocked = 1 AND unlock_date BETWEEN :startDate AND :endDate ORDER BY unlock_date DESC")
    List<AchievementEntity> getAchievementsUnlockedBetweenDates(long startDate, long endDate);

    @Query("SELECT COUNT(*) FROM achievements_table WHERE is_unlocked = 1 AND unlock_date BETWEEN :startDate AND :endDate")
    int getAchievementsUnlockedCountBetweenDates(long startDate, long endDate);

    // Search operations
    @Query("SELECT * FROM achievements_table WHERE title LIKE :searchQuery OR description LIKE :searchQuery ORDER BY is_unlocked DESC, title")
    List<AchievementEntity> searchAchievements(String searchQuery);

    @Query("SELECT * FROM achievements_table WHERE title LIKE :searchQuery OR description LIKE :searchQuery ORDER BY is_unlocked DESC, title")
    LiveData<List<AchievementEntity>> searchAchievementsLiveData(String searchQuery);

    // Type and category analysis
    @Query("SELECT DISTINCT achievement_type FROM achievements_table ORDER BY achievement_type")
    List<String> getAllAchievementTypes();

    @Query("SELECT DISTINCT category FROM achievements_table ORDER BY category")
    List<String> getAllAchievementCategories();

    // Validation
    @Query("SELECT COUNT(*) FROM achievements_table WHERE achievement_id = :achievementId")
    int checkAchievementIdExists(String achievementId);

    // Custom update operations
    @Query("UPDATE achievements_table SET current_value = :currentValue, updated_at = :timestamp WHERE achievement_id = :achievementId")
    int updateAchievementProgress(String achievementId, int currentValue, long timestamp);

    @Query("UPDATE achievements_table SET current_value = current_value + :increment, updated_at = :timestamp WHERE achievement_id = :achievementId")
    int incrementAchievementProgress(String achievementId, int increment, long timestamp);

    @Query("UPDATE achievements_table SET is_unlocked = 1, unlock_date = :unlockDate, updated_at = :timestamp WHERE achievement_id = :achievementId")
    int unlockAchievement(String achievementId, long unlockDate, long timestamp);

    @Query("UPDATE achievements_table SET current_value = 0, is_unlocked = 0, unlock_date = 0, updated_at = :timestamp WHERE achievement_id = :achievementId")
    int resetAchievement(String achievementId, long timestamp);

    // Bulk operations
    @Query("UPDATE achievements_table SET updated_at = :timestamp WHERE achievement_type = :achievementType")
    int updateTypeTimestamp(String achievementType, long timestamp);

    @Query("UPDATE achievements_table SET updated_at = :timestamp WHERE category = :category")
    int updateCategoryTimestamp(String category, long timestamp);

    // Maintenance operations
    @Query("SELECT * FROM achievements_table WHERE updated_at < :timestamp")
    List<AchievementEntity> getAchievementsOlderThan(long timestamp);

    @Query("UPDATE achievements_table SET updated_at = :timestamp")
    int updateAllTimestamps(long timestamp);
}

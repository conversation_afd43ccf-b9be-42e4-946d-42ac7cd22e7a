# دليل تحديث نظام التنزيل

## المشكلة
- `AsyncTask` أصبحت مهجورة (deprecated) في Android API 30+
- الكود الحالي يستخدم `AsyncTask` مما يسبب تحذيرات ومشاكل في الإصدارات الحديثة
- نحتاج لحل حديث يدعم أذونات Android 13+ الجديدة

## الحل
تم إنشاء `DownloadHelper` كبديل حديث يستخدم:
- `Thread` بدلاً من `AsyncTask`
- `runOnUiThread()` لتحديث الواجهة
- فحص أذونات Android 13+ الجديدة
- معالجة أفضل للأخطاء

## الملفات الجديدة

### 1. DownloadHelper.java
مساعد التنزيل الحديث الذي يحتوي على:
- فحص الأذونات للإصدارات المختلفة من Android
- فحص الاتصال بالإنترنت
- تنزيل الملفات مع شريط تقدم
- معالجة الأخطاء
- واجهة `DownloadListener` للتفاعل مع Activity

### 2. AyaListExample.java
مثال على كيفية استخدام `DownloadHelper` في Activity

## خطوات التطبيق على AyaList.java

### 1. إضافة DownloadHelper
```java
private DownloadHelper downloadHelper;

@Override
protected void onCreate(Bundle savedInstanceState) {
    // ... الكود الموجود
    
    // إنشاء مساعد التنزيل
    downloadHelper = new DownloadHelper(this, progressBar);
    downloadHelper.setDownloadListener(this);
}
```

### 2. تنفيذ DownloadListener
```java
public class AyaList extends AppCompatActivity implements DownloadHelper.DownloadListener {
    
    @Override
    public void onDownloadStart() {
        LayoutLoading.setVisibility(View.VISIBLE);
    }

    @Override
    public void onDownloadProgress(int progress) {
        // التحديث يتم تلقائياً
    }

    @Override
    public void onDownloadComplete(String filePath) {
        LayoutLoading.setVisibility(View.GONE);
        LoadAya();
    }

    @Override
    public void onDownloadError(String error) {
        LayoutLoading.setVisibility(View.GONE);
    }
}
```

### 3. تحديث startDownload()
```java
// القديم:
public void startDownload(String ImgUrl, String ServerName) {
    RecitesAYA = ServerName;
    String url = ImgUrl;
    new DownloadFileAsync().execute(url);
}

// الجديد:
public void startDownload(String ImgUrl, String ServerName) {
    RecitesAYA = ServerName;
    downloadHelper.startDownload(ImgUrl, RecitesAYA, RecitesName);
}
```

### 4. تحديث فحص حالة التنزيل
```java
// القديم:
if (!ISDonwloading)
    startDownload(temp.ImgUrl, ServerName);

// الجديد:
if (!downloadHelper.isDownloading())
    startDownload(temp.ImgUrl, ServerName);
```

### 5. حذف AsyncTask القديمة
احذف كامل:
```java
class DownloadFileAsync extends AsyncTask<String, String, String> {
    // ... كل المحتوى
}
```

### 6. تحديث معالجة الأذونات
```java
@Override
public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    downloadHelper.onRequestPermissionsResult(requestCode, permissions, grantResults);
}
```

### 7. تحديث Adapter
```java
// في getView():
budownload.setOnClickListener(v -> {
    if (!((AyaList)context).downloadHelper.isDownloading())
        ((AyaList)context).startDownload(temp.ImgUrl, ServerName);
});
```

## الأذونات المطلوبة في AndroidManifest.xml

```xml
<!-- للإصدارات القديمة -->
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" 
    android:maxSdkVersion="32" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" 
    android:maxSdkVersion="32" />

<!-- للإصدارات الحديثة Android 13+ -->
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

<!-- للاتصال بالإنترنت -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

## المزايا الجديدة

1. **متوافق مع Android 13+**: يدعم أذونات الوسائط الجديدة
2. **لا تحذيرات**: لا يستخدم APIs مهجورة
3. **معالجة أفضل للأخطاء**: رسائل واضحة للمستخدم
4. **فحص الاتصال**: يتحقق من الإنترنت قبل التنزيل
5. **أمان أكثر**: فحص الأذونات قبل التنزيل
6. **سهولة الاستخدام**: واجهة بسيطة مع callbacks

## اختبار التحديث

1. اختبر على Android 6-12 (أذونات التخزين القديمة)
2. اختبر على Android 13+ (أذونات الوسائط الجديدة)
3. اختبر بدون إنترنت
4. اختبر رفض الأذونات
5. اختبر التنزيل العادي

## ملاحظات مهمة

- تأكد من إضافة `implements DownloadHelper.DownloadListener` للـ Activity
- لا تنس استدعاء `downloadHelper.onRequestPermissionsResult()` 
- استخدم `downloadHelper.isDownloading()` بدلاً من المتغير القديم
- الملفات تُحفظ في `getExternalFilesDir()` وهو آمن ولا يحتاج أذونات خاصة

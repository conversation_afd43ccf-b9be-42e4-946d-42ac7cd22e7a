// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ContentAzkarBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final TextView azkarTitle;

  @NonNull
  public final CardView eveningAzkarCard;

  @NonNull
  public final ScrollView mainScrollView;

  @NonNull
  public final CardView morningAzkarCard;

  @NonNull
  public final ImageView nightDayToggle;

  @NonNull
  public final ImageView notificationIcon;

  @NonNull
  public final View notificationStatusIndicator;

  @NonNull
  public final CardView prayerAzkarCard;

  @NonNull
  public final CardView sleepAzkarCard;

  @NonNull
  public final CardView wakeAzkarCard;

  private ContentAzkarBinding(@NonNull ScrollView rootView, @NonNull TextView azkarTitle,
      @NonNull CardView eveningAzkarCard, @NonNull ScrollView mainScrollView,
      @NonNull CardView morningAzkarCard, @NonNull ImageView nightDayToggle,
      @NonNull ImageView notificationIcon, @NonNull View notificationStatusIndicator,
      @NonNull CardView prayerAzkarCard, @NonNull CardView sleepAzkarCard,
      @NonNull CardView wakeAzkarCard) {
    this.rootView = rootView;
    this.azkarTitle = azkarTitle;
    this.eveningAzkarCard = eveningAzkarCard;
    this.mainScrollView = mainScrollView;
    this.morningAzkarCard = morningAzkarCard;
    this.nightDayToggle = nightDayToggle;
    this.notificationIcon = notificationIcon;
    this.notificationStatusIndicator = notificationStatusIndicator;
    this.prayerAzkarCard = prayerAzkarCard;
    this.sleepAzkarCard = sleepAzkarCard;
    this.wakeAzkarCard = wakeAzkarCard;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ContentAzkarBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ContentAzkarBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.content_azkar, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ContentAzkarBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.azkarTitle;
      TextView azkarTitle = ViewBindings.findChildViewById(rootView, id);
      if (azkarTitle == null) {
        break missingId;
      }

      id = R.id.eveningAzkarCard;
      CardView eveningAzkarCard = ViewBindings.findChildViewById(rootView, id);
      if (eveningAzkarCard == null) {
        break missingId;
      }

      ScrollView mainScrollView = (ScrollView) rootView;

      id = R.id.morningAzkarCard;
      CardView morningAzkarCard = ViewBindings.findChildViewById(rootView, id);
      if (morningAzkarCard == null) {
        break missingId;
      }

      id = R.id.nightDayToggle;
      ImageView nightDayToggle = ViewBindings.findChildViewById(rootView, id);
      if (nightDayToggle == null) {
        break missingId;
      }

      id = R.id.notificationIcon;
      ImageView notificationIcon = ViewBindings.findChildViewById(rootView, id);
      if (notificationIcon == null) {
        break missingId;
      }

      id = R.id.notificationStatusIndicator;
      View notificationStatusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (notificationStatusIndicator == null) {
        break missingId;
      }

      id = R.id.prayerAzkarCard;
      CardView prayerAzkarCard = ViewBindings.findChildViewById(rootView, id);
      if (prayerAzkarCard == null) {
        break missingId;
      }

      id = R.id.sleepAzkarCard;
      CardView sleepAzkarCard = ViewBindings.findChildViewById(rootView, id);
      if (sleepAzkarCard == null) {
        break missingId;
      }

      id = R.id.wakeAzkarCard;
      CardView wakeAzkarCard = ViewBindings.findChildViewById(rootView, id);
      if (wakeAzkarCard == null) {
        break missingId;
      }

      return new ContentAzkarBinding((ScrollView) rootView, azkarTitle, eveningAzkarCard,
          mainScrollView, morningAzkarCard, nightDayToggle, notificationIcon,
          notificationStatusIndicator, prayerAzkarCard, sleepAzkarCard, wakeAzkarCard);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

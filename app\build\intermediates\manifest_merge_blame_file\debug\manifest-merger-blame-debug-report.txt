1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.qurany2019.quranyapp"
4    android:versionCode="6"
5    android:versionName="5.2" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
11-->Z:\5qren5\app\src\main\AndroidManifest.xml:5:5-76
11-->Z:\5qren5\app\src\main\AndroidManifest.xml:5:22-73
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->Z:\5qren5\app\src\main\AndroidManifest.xml:6:5-79
12-->Z:\5qren5\app\src\main\AndroidManifest.xml:6:22-76
13    <uses-permission android:name="android.permission.INTERNET" />
13-->Z:\5qren5\app\src\main\AndroidManifest.xml:7:5-67
13-->Z:\5qren5\app\src\main\AndroidManifest.xml:7:22-64
14    <uses-permission android:name="android.permission.wake_lock" />
14-->Z:\5qren5\app\src\main\AndroidManifest.xml:8:5-68
14-->Z:\5qren5\app\src\main\AndroidManifest.xml:8:22-65
15    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
15-->Z:\5qren5\app\src\main\AndroidManifest.xml:9:5-78
15-->Z:\5qren5\app\src\main\AndroidManifest.xml:9:22-75
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->Z:\5qren5\app\src\main\AndroidManifest.xml:10:5-77
16-->Z:\5qren5\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->Z:\5qren5\app\src\main\AndroidManifest.xml:11:5-80
17-->Z:\5qren5\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission
18-->Z:\5qren5\app\src\main\AndroidManifest.xml:12:5-107
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->Z:\5qren5\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->Z:\5qren5\app\src\main\AndroidManifest.xml:12:79-105
21    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- الإضافة المطلوبة الجديدة -->
21-->Z:\5qren5\app\src\main\AndroidManifest.xml:13:5-75
21-->Z:\5qren5\app\src\main\AndroidManifest.xml:13:22-72
22
23
24    <!-- أذونات القبلة وأوقات الصلاة -->
25    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
25-->Z:\5qren5\app\src\main\AndroidManifest.xml:16:5-79
25-->Z:\5qren5\app\src\main\AndroidManifest.xml:16:22-76
26    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
26-->Z:\5qren5\app\src\main\AndroidManifest.xml:17:5-81
26-->Z:\5qren5\app\src\main\AndroidManifest.xml:17:22-78
27    <!-- إذن الموقع في الخلفية مطلوب لإشعارات أوقات الصلاة التلقائية -->
28    <!-- يتم استخدامه لحساب أوقات الصلاة الدقيقة حسب موقع المستخدم وإرسال إشعارات في الأوقات المناسبة -->
29    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
29-->Z:\5qren5\app\src\main\AndroidManifest.xml:20:5-85
29-->Z:\5qren5\app\src\main\AndroidManifest.xml:20:22-82
30
31    <!-- أذونات البوصلة والحساسات -->
32    <uses-feature
32-->Z:\5qren5\app\src\main\AndroidManifest.xml:23:5-98
33        android:name="android.hardware.sensor.accelerometer"
33-->Z:\5qren5\app\src\main\AndroidManifest.xml:23:19-71
34        android:required="true" />
34-->Z:\5qren5\app\src\main\AndroidManifest.xml:23:72-95
35    <uses-feature
35-->Z:\5qren5\app\src\main\AndroidManifest.xml:24:5-92
36        android:name="android.hardware.sensor.compass"
36-->Z:\5qren5\app\src\main\AndroidManifest.xml:24:19-65
37        android:required="true" />
37-->Z:\5qren5\app\src\main\AndroidManifest.xml:24:66-89
38
39    <!-- أذونات إضافية للإشعارات -->
40    <uses-permission android:name="android.permission.VIBRATE" />
40-->Z:\5qren5\app\src\main\AndroidManifest.xml:27:5-66
40-->Z:\5qren5\app\src\main\AndroidManifest.xml:27:22-63
41    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
41-->Z:\5qren5\app\src\main\AndroidManifest.xml:28:5-79
41-->Z:\5qren5\app\src\main\AndroidManifest.xml:28:22-76
42    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
42-->Z:\5qren5\app\src\main\AndroidManifest.xml:29:5-74
42-->Z:\5qren5\app\src\main\AndroidManifest.xml:29:22-71
43
44    <!-- أذونات إشعارات الوسائط الكاملة -->
45    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
45-->Z:\5qren5\app\src\main\AndroidManifest.xml:32:5-77
45-->Z:\5qren5\app\src\main\AndroidManifest.xml:32:22-74
46    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
46-->Z:\5qren5\app\src\main\AndroidManifest.xml:33:5-92
46-->Z:\5qren5\app\src\main\AndroidManifest.xml:33:22-89
47
48    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
49    <permission
49-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:7:5-9:47
50        android:name="com.qurany2019.quranyapp.permission.C2D_MESSAGE"
50-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:8:9-63
51        android:protectionLevel="signature" />
51-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:9:9-44
52
53    <uses-permission android:name="com.qurany2019.quranyapp.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
53-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:11:5-79
53-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:11:22-76
54    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
54-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:18:5-82
54-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:18:22-79
55    <uses-permission android:name="android.permission.WAKE_LOCK" />
55-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:19:5-68
55-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:19:22-65
56    <!--
57 Use to restore notifications the user hasn't interacted with.
58         They could be missed notifications if the user reboots their device if this isn't in place.
59    -->
60    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
60-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:30:5-81
60-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:30:22-78
61    <!-- Samsung -->
62    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
62-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:32:5-86
62-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:32:22-83
63    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
63-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:33:5-87
63-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:33:22-84
64    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
64-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:34:5-81
64-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:34:22-78
65    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
65-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:35:5-83
65-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:35:22-80
66    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
66-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:36:5-88
66-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:36:22-85
67    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
67-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:37:5-92
67-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:37:22-89
68    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
68-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:38:5-84
68-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:38:22-81
69    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
69-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:39:5-83
69-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:39:22-80
70    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
70-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:40:5-91
70-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:40:22-88
71    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
71-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:41:5-92
71-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:41:22-89
72    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
72-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:42:5-93
72-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:42:22-90
73    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
73-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:43:5-73
73-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:43:22-70
74    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
74-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:44:5-82
74-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:44:22-79
75    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
75-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:45:5-83
75-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:45:22-80
76    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
76-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:46:5-88
76-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:46:22-85
77    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
77-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:47:5-89
77-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:47:22-86
78    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
78-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:26:5-79
78-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:26:22-76
79    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
79-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:27:5-82
79-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:27:22-79
80    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
80-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:28:5-88
80-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:28:22-85
81    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
81-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:29:5-83
81-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:29:22-80
82    <queries>
82-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:35:5-51:15
83
84        <!-- For browser content -->
85        <intent>
85-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:38:9-44:18
86            <action android:name="android.intent.action.VIEW" />
86-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:17-69
86-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:25-66
87
88            <category android:name="android.intent.category.BROWSABLE" />
88-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:41:13-74
88-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:41:23-71
89
90            <data android:scheme="https" />
90-->Z:\5qren5\app\src\main\AndroidManifest.xml:81:17-50
90-->Z:\5qren5\app\src\main\AndroidManifest.xml:81:23-47
91        </intent>
92        <!-- End of browser content -->
93        <!-- For CustomTabsService -->
94        <intent>
94-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:47:9-49:18
95            <action android:name="android.support.customtabs.action.CustomTabsService" />
95-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:48:13-90
95-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:48:21-87
96        </intent>
97        <!-- End of CustomTabsService -->
98    </queries>
99
100    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
100-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:26:5-110
100-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:26:22-107
101
102    <permission
102-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b787db1b58c51bd47c5c8fb612fd5be\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
103        android:name="com.qurany2019.quranyapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
103-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b787db1b58c51bd47c5c8fb612fd5be\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
104        android:protectionLevel="signature" />
104-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b787db1b58c51bd47c5c8fb612fd5be\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
105
106    <uses-permission android:name="com.qurany2019.quranyapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
106-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b787db1b58c51bd47c5c8fb612fd5be\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
106-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b787db1b58c51bd47c5c8fb612fd5be\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
107
108    <application
108-->Z:\5qren5\app\src\main\AndroidManifest.xml:35:5-311:19
109        android:name="com.qurany2019.quranyapp.QuranApplication"
109-->Z:\5qren5\app\src\main\AndroidManifest.xml:36:9-41
110        android:allowBackup="true"
110-->Z:\5qren5\app\src\main\AndroidManifest.xml:37:9-35
111        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
111-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-3\4b787db1b58c51bd47c5c8fb612fd5be\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
112        android:debuggable="true"
113        android:extractNativeLibs="true"
114        android:icon="@mipmap/ic_launcher"
114-->Z:\5qren5\app\src\main\AndroidManifest.xml:38:9-43
115        android:label="@string/app_name"
115-->Z:\5qren5\app\src\main\AndroidManifest.xml:40:9-41
116        android:requestLegacyExternalStorage="true"
116-->Z:\5qren5\app\src\main\AndroidManifest.xml:43:9-52
117        android:roundIcon="@mipmap/ic_launcher_round"
117-->Z:\5qren5\app\src\main\AndroidManifest.xml:39:9-54
118        android:supportsRtl="true"
118-->Z:\5qren5\app\src\main\AndroidManifest.xml:41:9-35
119        android:testOnly="true"
120        android:theme="@style/Theme.QuranyApp"
120-->Z:\5qren5\app\src\main\AndroidManifest.xml:42:9-47
121        android:usesCleartextTraffic="true" >
121-->Z:\5qren5\app\src\main\AndroidManifest.xml:44:9-44
122
123        <!-- AdMob App ID -->
124        <meta-data
124-->Z:\5qren5\app\src\main\AndroidManifest.xml:48:9-50:69
125            android:name="com.google.android.gms.ads.APPLICATION_ID"
125-->Z:\5qren5\app\src\main\AndroidManifest.xml:49:13-69
126            android:value="ca-app-pub-7841751633097845~7935499105" />
126-->Z:\5qren5\app\src\main\AndroidManifest.xml:50:13-67
127
128        <!-- Register the Alarm Receiver -->
129        <receiver android:name="com.qurany2019.quranyapp.Receiver" />
129-->Z:\5qren5\app\src\main\AndroidManifest.xml:53:9-70
129-->Z:\5qren5\app\src\main\AndroidManifest.xml:53:19-67
130
131        <!-- Prayer Times Notification Receiver -->
132        <receiver
132-->Z:\5qren5\app\src\main\AndroidManifest.xml:56:9-62:20
133            android:name="com.qurany2019.quranyapp.PrayerNotificationReceiver"
133-->Z:\5qren5\app\src\main\AndroidManifest.xml:56:19-85
134            android:enabled="true"
134-->Z:\5qren5\app\src\main\AndroidManifest.xml:57:13-35
135            android:exported="false" >
135-->Z:\5qren5\app\src\main\AndroidManifest.xml:58:13-37
136            <intent-filter>
136-->Z:\5qren5\app\src\main\AndroidManifest.xml:59:13-61:29
137                <action android:name="com.qurany2019.quranyapp.PRAYER_NOTIFICATION" />
137-->Z:\5qren5\app\src\main\AndroidManifest.xml:60:17-87
137-->Z:\5qren5\app\src\main\AndroidManifest.xml:60:25-84
138            </intent-filter>
139        </receiver>
140
141        <!-- Azkar Reminder Receiver -->
142        <receiver
142-->Z:\5qren5\app\src\main\AndroidManifest.xml:65:9-71:20
143            android:name="com.qurany2019.quranyapp.AzkarReminderReceiver"
143-->Z:\5qren5\app\src\main\AndroidManifest.xml:65:19-80
144            android:enabled="true"
144-->Z:\5qren5\app\src\main\AndroidManifest.xml:66:13-35
145            android:exported="false" >
145-->Z:\5qren5\app\src\main\AndroidManifest.xml:67:13-37
146            <intent-filter>
146-->Z:\5qren5\app\src\main\AndroidManifest.xml:68:13-70:29
147                <action android:name="com.qurany2019.quranyapp.AZKAR_REMINDER" />
147-->Z:\5qren5\app\src\main\AndroidManifest.xml:69:17-82
147-->Z:\5qren5\app\src\main\AndroidManifest.xml:69:25-79
148            </intent-filter>
149        </receiver>
150
151        <!-- Boot Receiver to restart notifications -->
152        <receiver
152-->Z:\5qren5\app\src\main\AndroidManifest.xml:74:9-83:20
153            android:name="com.qurany2019.quranyapp.BootReceiver"
153-->Z:\5qren5\app\src\main\AndroidManifest.xml:74:19-71
154            android:enabled="true"
154-->Z:\5qren5\app\src\main\AndroidManifest.xml:75:13-35
155            android:exported="true" >
155-->Z:\5qren5\app\src\main\AndroidManifest.xml:76:13-36
156            <intent-filter android:priority="1000" >
156-->Z:\5qren5\app\src\main\AndroidManifest.xml:77:13-82:29
156-->Z:\5qren5\app\src\main\AndroidManifest.xml:77:28-51
157                <action android:name="android.intent.action.BOOT_COMPLETED" />
157-->Z:\5qren5\app\src\main\AndroidManifest.xml:78:17-79
157-->Z:\5qren5\app\src\main\AndroidManifest.xml:78:25-76
158                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
158-->Z:\5qren5\app\src\main\AndroidManifest.xml:79:17-84
158-->Z:\5qren5\app\src\main\AndroidManifest.xml:79:25-81
159                <action android:name="android.intent.action.PACKAGE_REPLACED" />
159-->Z:\5qren5\app\src\main\AndroidManifest.xml:80:17-81
159-->Z:\5qren5\app\src\main\AndroidManifest.xml:80:25-78
160
161                <data android:scheme="package" />
161-->Z:\5qren5\app\src\main\AndroidManifest.xml:81:17-50
161-->Z:\5qren5\app\src\main\AndroidManifest.xml:81:23-47
162            </intent-filter>
163        </receiver>
164
165        <activity
165-->Z:\5qren5\app\src\main\AndroidManifest.xml:85:9-95:20
166            android:name="com.qurany2019.quranyapp.Splash"
166-->Z:\5qren5\app\src\main\AndroidManifest.xml:86:13-59
167            android:exported="true"
167-->Z:\5qren5\app\src\main\AndroidManifest.xml:87:13-36
168            android:label="@string/app_name"
168-->Z:\5qren5\app\src\main\AndroidManifest.xml:88:13-45
169            android:screenOrientation="portrait"
169-->Z:\5qren5\app\src\main\AndroidManifest.xml:89:13-49
170            android:theme="@style/Theme.QuranyApp" >
170-->Z:\5qren5\app\src\main\AndroidManifest.xml:90:13-51
171            <intent-filter>
171-->Z:\5qren5\app\src\main\AndroidManifest.xml:91:13-94:29
172                <action android:name="android.intent.action.MAIN" />
172-->Z:\5qren5\app\src\main\AndroidManifest.xml:92:17-69
172-->Z:\5qren5\app\src\main\AndroidManifest.xml:92:25-66
173
174                <category android:name="android.intent.category.LAUNCHER" />
174-->Z:\5qren5\app\src\main\AndroidManifest.xml:93:17-77
174-->Z:\5qren5\app\src\main\AndroidManifest.xml:93:27-74
175            </intent-filter>
176        </activity>
177        <activity
177-->Z:\5qren5\app\src\main\AndroidManifest.xml:97:9-104:20
178            android:name="com.qurany2019.quranyapp.MainActivity"
178-->Z:\5qren5\app\src\main\AndroidManifest.xml:98:13-65
179            android:label="@string/title_activity_main"
179-->Z:\5qren5\app\src\main\AndroidManifest.xml:99:13-56
180            android:theme="@style/Theme.QuranyApp" >
180-->Z:\5qren5\app\src\main\AndroidManifest.xml:100:13-51
181            <meta-data
181-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
182                android:name="android.support.PARENT_ACTIVITY"
182-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
183                android:value="com.qurany2019.quranyapp.MainActivity" />
183-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
184        </activity>
185        <activity
185-->Z:\5qren5\app\src\main\AndroidManifest.xml:106:9-113:20
186            android:name="com.qurany2019.quranyapp.MainActivitySimple"
186-->Z:\5qren5\app\src\main\AndroidManifest.xml:107:13-71
187            android:label="@string/title_activity_main"
187-->Z:\5qren5\app\src\main\AndroidManifest.xml:108:13-56
188            android:theme="@style/Theme.QuranyApp" >
188-->Z:\5qren5\app\src\main\AndroidManifest.xml:109:13-51
189            <meta-data
189-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
190                android:name="android.support.PARENT_ACTIVITY"
190-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
191                android:value="com.qurany2019.quranyapp.MainActivitySimple" />
191-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
192        </activity>
193        <activity
193-->Z:\5qren5\app\src\main\AndroidManifest.xml:115:9-123:20
194            android:name="com.qurany2019.quranyapp.About"
194-->Z:\5qren5\app\src\main\AndroidManifest.xml:116:13-58
195            android:label="@string/title_activity_about"
195-->Z:\5qren5\app\src\main\AndroidManifest.xml:117:13-57
196            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
196-->Z:\5qren5\app\src\main\AndroidManifest.xml:118:13-79
197            android:theme="@style/Theme.QuranyApp" >
197-->Z:\5qren5\app\src\main\AndroidManifest.xml:119:13-51
198            <meta-data
198-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
199                android:name="android.support.PARENT_ACTIVITY"
199-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
200                android:value="com.qurany2019.quranyapp.MainActivity" />
200-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
201        </activity>
202        <activity
202-->Z:\5qren5\app\src\main\AndroidManifest.xml:125:9-133:20
203            android:name="com.qurany2019.quranyapp.Hisn"
203-->Z:\5qren5\app\src\main\AndroidManifest.xml:126:13-57
204            android:label="@string/title_activity_hisn"
204-->Z:\5qren5\app\src\main\AndroidManifest.xml:127:13-56
205            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
205-->Z:\5qren5\app\src\main\AndroidManifest.xml:128:13-79
206            android:theme="@style/Theme.QuranyApp" >
206-->Z:\5qren5\app\src\main\AndroidManifest.xml:129:13-51
207            <meta-data
207-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
208                android:name="android.support.PARENT_ACTIVITY"
208-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
209                android:value="com.qurany2019.quranyapp.MainActivity" />
209-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
210        </activity>
211        <activity
211-->Z:\5qren5\app\src\main\AndroidManifest.xml:135:9-136:54
212            android:name="com.qurany2019.quranyapp.Whtml"
212-->Z:\5qren5\app\src\main\AndroidManifest.xml:135:19-64
213            android:theme="@style/Theme.QuranyApp" />
213-->Z:\5qren5\app\src\main\AndroidManifest.xml:136:13-51
214        <activity
214-->Z:\5qren5\app\src\main\AndroidManifest.xml:138:9-146:20
215            android:name="com.qurany2019.quranyapp.Tazker"
215-->Z:\5qren5\app\src\main\AndroidManifest.xml:139:13-59
216            android:label="@string/title_activity_tazker"
216-->Z:\5qren5\app\src\main\AndroidManifest.xml:140:13-58
217            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
217-->Z:\5qren5\app\src\main\AndroidManifest.xml:141:13-79
218            android:theme="@style/Theme.QuranyApp" >
218-->Z:\5qren5\app\src\main\AndroidManifest.xml:142:13-51
219            <meta-data
219-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
220                android:name="android.support.PARENT_ACTIVITY"
220-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
221                android:value="com.qurany2019.quranyapp.MainActivity" />
221-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
222        </activity>
223        <activity
223-->Z:\5qren5\app\src\main\AndroidManifest.xml:148:9-151:54
224            android:name="com.qurany2019.quranyapp.AyaList"
224-->Z:\5qren5\app\src\main\AndroidManifest.xml:149:13-60
225            android:label="@string/title_quran"
225-->Z:\5qren5\app\src\main\AndroidManifest.xml:150:13-48
226            android:theme="@style/Theme.QuranyApp" />
226-->Z:\5qren5\app\src\main\AndroidManifest.xml:151:13-51
227        <activity
227-->Z:\5qren5\app\src\main\AndroidManifest.xml:153:9-157:46
228            android:name="com.qurany2019.quranyapp.managerdb"
228-->Z:\5qren5\app\src\main\AndroidManifest.xml:154:13-62
229            android:label="@string/title_quran"
229-->Z:\5qren5\app\src\main\AndroidManifest.xml:155:13-48
230            android:launchMode="singleTop"
230-->Z:\5qren5\app\src\main\AndroidManifest.xml:157:13-43
231            android:theme="@style/Theme.QuranyApp" />
231-->Z:\5qren5\app\src\main\AndroidManifest.xml:156:13-51
232        <activity
232-->Z:\5qren5\app\src\main\AndroidManifest.xml:159:9-162:54
233            android:name="com.qurany2019.quranyapp.RecitesName"
233-->Z:\5qren5\app\src\main\AndroidManifest.xml:160:13-64
234            android:label="@string/title_quran"
234-->Z:\5qren5\app\src\main\AndroidManifest.xml:161:13-48
235            android:theme="@style/Theme.QuranyApp" />
235-->Z:\5qren5\app\src\main\AndroidManifest.xml:162:13-51
236        <activity
236-->Z:\5qren5\app\src\main\AndroidManifest.xml:164:9-167:54
237            android:name="com.qurany2019.quranyapp.Sellings"
237-->Z:\5qren5\app\src\main\AndroidManifest.xml:165:13-61
238            android:label="@string/title_quran"
238-->Z:\5qren5\app\src\main\AndroidManifest.xml:166:13-48
239            android:theme="@style/Theme.QuranyApp" />
239-->Z:\5qren5\app\src\main\AndroidManifest.xml:167:13-51
240        <activity
240-->Z:\5qren5\app\src\main\AndroidManifest.xml:169:9-172:62
241            android:name="com.qurany2019.quranyapp.quranActivity"
241-->Z:\5qren5\app\src\main\AndroidManifest.xml:170:13-66
242            android:configChanges="orientation|screenSize"
242-->Z:\5qren5\app\src\main\AndroidManifest.xml:172:13-59
243            android:label="Read Quran" />
243-->Z:\5qren5\app\src\main\AndroidManifest.xml:171:13-39
244        <activity android:name="com.qurany2019.quranyapp.PrivcyActivity" />
244-->Z:\5qren5\app\src\main\AndroidManifest.xml:174:9-52
244-->Z:\5qren5\app\src\main\AndroidManifest.xml:174:19-49
245        <activity
245-->Z:\5qren5\app\src\main\AndroidManifest.xml:176:9-184:20
246            android:name="com.qurany2019.quranyapp.SettingsActivity"
246-->Z:\5qren5\app\src\main\AndroidManifest.xml:177:13-69
247            android:label="الإعدادات"
247-->Z:\5qren5\app\src\main\AndroidManifest.xml:178:13-38
248            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
248-->Z:\5qren5\app\src\main\AndroidManifest.xml:179:13-79
249            android:theme="@style/Theme.QuranyApp" >
249-->Z:\5qren5\app\src\main\AndroidManifest.xml:180:13-51
250            <meta-data
250-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
251                android:name="android.support.PARENT_ACTIVITY"
251-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
252                android:value="com.qurany2019.quranyapp.MainActivity" />
252-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
253        </activity>
254        <activity
254-->Z:\5qren5\app\src\main\AndroidManifest.xml:186:9-194:20
255            android:name="com.qurany2019.quranyapp.QiblaActivity"
255-->Z:\5qren5\app\src\main\AndroidManifest.xml:187:13-66
256            android:label="اتجاه القبلة"
256-->Z:\5qren5\app\src\main\AndroidManifest.xml:188:13-41
257            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
257-->Z:\5qren5\app\src\main\AndroidManifest.xml:189:13-79
258            android:theme="@style/Theme.QuranyApp" >
258-->Z:\5qren5\app\src\main\AndroidManifest.xml:190:13-51
259            <meta-data
259-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
260                android:name="android.support.PARENT_ACTIVITY"
260-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
261                android:value="com.qurany2019.quranyapp.MainActivity" />
261-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
262        </activity>
263        <activity
263-->Z:\5qren5\app\src\main\AndroidManifest.xml:196:9-204:20
264            android:name="com.qurany2019.quranyapp.PrayerTimesActivity"
264-->Z:\5qren5\app\src\main\AndroidManifest.xml:197:13-72
265            android:label="أوقات الصلاة"
265-->Z:\5qren5\app\src\main\AndroidManifest.xml:198:13-41
266            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
266-->Z:\5qren5\app\src\main\AndroidManifest.xml:199:13-79
267            android:theme="@style/Theme.QuranyApp" >
267-->Z:\5qren5\app\src\main\AndroidManifest.xml:200:13-51
268            <meta-data
268-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
269                android:name="android.support.PARENT_ACTIVITY"
269-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
270                android:value="com.qurany2019.quranyapp.MainActivity" />
270-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
271        </activity>
272        <activity
272-->Z:\5qren5\app\src\main\AndroidManifest.xml:206:9-214:20
273            android:name="com.qurany2019.quranyapp.AzkarActivity"
273-->Z:\5qren5\app\src\main\AndroidManifest.xml:207:13-66
274            android:label="@string/azkar_title"
274-->Z:\5qren5\app\src\main\AndroidManifest.xml:208:13-48
275            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
275-->Z:\5qren5\app\src\main\AndroidManifest.xml:209:13-79
276            android:theme="@style/Theme.QuranyApp" >
276-->Z:\5qren5\app\src\main\AndroidManifest.xml:210:13-51
277            <meta-data
277-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
278                android:name="android.support.PARENT_ACTIVITY"
278-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
279                android:value="com.qurany2019.quranyapp.MainActivity" />
279-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
280        </activity>
281        <activity
281-->Z:\5qren5\app\src\main\AndroidManifest.xml:216:9-224:20
282            android:name="com.qurany2019.quranyapp.AzkarDetailActivity"
282-->Z:\5qren5\app\src\main\AndroidManifest.xml:217:13-72
283            android:label="تفاصيل الأذكار"
283-->Z:\5qren5\app\src\main\AndroidManifest.xml:218:13-43
284            android:parentActivityName="com.qurany2019.quranyapp.AzkarActivity"
284-->Z:\5qren5\app\src\main\AndroidManifest.xml:219:13-80
285            android:theme="@style/Theme.QuranyApp" >
285-->Z:\5qren5\app\src\main\AndroidManifest.xml:220:13-51
286            <meta-data
286-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
287                android:name="android.support.PARENT_ACTIVITY"
287-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
288                android:value="com.qurany2019.quranyapp.AzkarActivity" />
288-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
289        </activity>
290        <activity
290-->Z:\5qren5\app\src\main\AndroidManifest.xml:226:9-234:20
291            android:name="com.qurany2019.quranyapp.MoreActivity"
291-->Z:\5qren5\app\src\main\AndroidManifest.xml:227:13-65
292            android:label="المزيد"
292-->Z:\5qren5\app\src\main\AndroidManifest.xml:228:13-35
293            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
293-->Z:\5qren5\app\src\main\AndroidManifest.xml:229:13-79
294            android:theme="@style/Theme.QuranyApp" >
294-->Z:\5qren5\app\src\main\AndroidManifest.xml:230:13-51
295            <meta-data
295-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
296                android:name="android.support.PARENT_ACTIVITY"
296-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
297                android:value="com.qurany2019.quranyapp.MainActivity" />
297-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
298        </activity>
299
300        <!-- أنشطة النظام الشامل -->
301        <activity
301-->Z:\5qren5\app\src\main\AndroidManifest.xml:237:9-245:20
302            android:name="com.qurany2019.quranyapp.AzkarStatisticsActivity"
302-->Z:\5qren5\app\src\main\AndroidManifest.xml:238:13-76
303            android:label="إحصائيات الأذكار"
303-->Z:\5qren5\app\src\main\AndroidManifest.xml:239:13-45
304            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
304-->Z:\5qren5\app\src\main\AndroidManifest.xml:240:13-79
305            android:theme="@style/Theme.QuranyApp" >
305-->Z:\5qren5\app\src\main\AndroidManifest.xml:241:13-51
306            <meta-data
306-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
307                android:name="android.support.PARENT_ACTIVITY"
307-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
308                android:value="com.qurany2019.quranyapp.MainActivity" />
308-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
309        </activity>
310        <activity
310-->Z:\5qren5\app\src\main\AndroidManifest.xml:247:9-255:20
311            android:name="com.qurany2019.quranyapp.AchievementsActivity"
311-->Z:\5qren5\app\src\main\AndroidManifest.xml:248:13-73
312            android:label="الإنجازات"
312-->Z:\5qren5\app\src\main\AndroidManifest.xml:249:13-38
313            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
313-->Z:\5qren5\app\src\main\AndroidManifest.xml:250:13-79
314            android:theme="@style/Theme.QuranyApp" >
314-->Z:\5qren5\app\src\main\AndroidManifest.xml:251:13-51
315            <meta-data
315-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
316                android:name="android.support.PARENT_ACTIVITY"
316-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
317                android:value="com.qurany2019.quranyapp.MainActivity" />
317-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
318        </activity>
319        <activity
319-->Z:\5qren5\app\src\main\AndroidManifest.xml:257:9-265:20
320            android:name="com.qurany2019.quranyapp.ReminderSettingsActivity"
320-->Z:\5qren5\app\src\main\AndroidManifest.xml:258:13-77
321            android:label="إعدادات التذكيرات"
321-->Z:\5qren5\app\src\main\AndroidManifest.xml:259:13-46
322            android:parentActivityName="com.qurany2019.quranyapp.SettingsActivity"
322-->Z:\5qren5\app\src\main\AndroidManifest.xml:260:13-83
323            android:theme="@style/Theme.QuranyApp" >
323-->Z:\5qren5\app\src\main\AndroidManifest.xml:261:13-51
324            <meta-data
324-->Z:\5qren5\app\src\main\AndroidManifest.xml:101:13-103:73
325                android:name="android.support.PARENT_ACTIVITY"
325-->Z:\5qren5\app\src\main\AndroidManifest.xml:102:17-63
326                android:value="com.qurany2019.quranyapp.SettingsActivity" />
326-->Z:\5qren5\app\src\main\AndroidManifest.xml:103:17-70
327        </activity>
328
329        <!-- خدمة إشعارات الوسائط مع أزرار التحكم -->
330        <service
330-->Z:\5qren5\app\src\main\AndroidManifest.xml:268:9-272:61
331            android:name="com.qurany2019.quranyapp.MediaNotificationService"
331-->Z:\5qren5\app\src\main\AndroidManifest.xml:269:13-53
332            android:enabled="true"
332-->Z:\5qren5\app\src\main\AndroidManifest.xml:270:13-35
333            android:exported="false"
333-->Z:\5qren5\app\src\main\AndroidManifest.xml:271:13-37
334            android:foregroundServiceType="mediaPlayback" />
334-->Z:\5qren5\app\src\main\AndroidManifest.xml:272:13-58
335
336        <!-- خدمة إشعارات بسيطة وفعالة -->
337        <service
337-->Z:\5qren5\app\src\main\AndroidManifest.xml:275:9-279:61
338            android:name="com.qurany2019.quranyapp.SimpleMediaNotification"
338-->Z:\5qren5\app\src\main\AndroidManifest.xml:276:13-52
339            android:enabled="true"
339-->Z:\5qren5\app\src\main\AndroidManifest.xml:277:13-35
340            android:exported="false"
340-->Z:\5qren5\app\src\main\AndroidManifest.xml:278:13-37
341            android:foregroundServiceType="mediaPlayback" />
341-->Z:\5qren5\app\src\main\AndroidManifest.xml:279:13-58
342
343        <!-- BroadcastReceiver لأوامر الوسائط -->
344        <receiver
344-->Z:\5qren5\app\src\main\AndroidManifest.xml:282:9-289:20
345            android:name="com.qurany2019.quranyapp.MediaActionReceiver"
345-->Z:\5qren5\app\src\main\AndroidManifest.xml:283:13-48
346            android:enabled="true"
346-->Z:\5qren5\app\src\main\AndroidManifest.xml:284:13-35
347            android:exported="false" >
347-->Z:\5qren5\app\src\main\AndroidManifest.xml:285:13-37
348            <intent-filter>
348-->Z:\5qren5\app\src\main\AndroidManifest.xml:286:13-288:29
349                <action android:name="com.qurany2019.quranyapp.MEDIA_ACTION" />
349-->Z:\5qren5\app\src\main\AndroidManifest.xml:287:17-80
349-->Z:\5qren5\app\src\main\AndroidManifest.xml:287:25-77
350            </intent-filter>
351        </receiver>
352
353        <!-- BroadcastReceiver للإشعار البسيط -->
354        <receiver
354-->Z:\5qren5\app\src\main\AndroidManifest.xml:292:9-299:20
355            android:name="com.qurany2019.quranyapp.SimpleMediaReceiver"
355-->Z:\5qren5\app\src\main\AndroidManifest.xml:293:13-48
356            android:enabled="true"
356-->Z:\5qren5\app\src\main\AndroidManifest.xml:294:13-35
357            android:exported="true" >
357-->Z:\5qren5\app\src\main\AndroidManifest.xml:295:13-36
358            <intent-filter>
358-->Z:\5qren5\app\src\main\AndroidManifest.xml:296:13-298:29
359                <action android:name="com.qurany2019.quranyapp.SIMPLE_MEDIA_CONTROL" />
359-->Z:\5qren5\app\src\main\AndroidManifest.xml:297:17-88
359-->Z:\5qren5\app\src\main\AndroidManifest.xml:297:25-85
360            </intent-filter>
361        </receiver>
362
363        <!-- BroadcastReceiver للتحكم المباشر -->
364        <receiver
364-->Z:\5qren5\app\src\main\AndroidManifest.xml:302:9-309:20
365            android:name="com.qurany2019.quranyapp.StaticMediaReceiver"
365-->Z:\5qren5\app\src\main\AndroidManifest.xml:303:13-48
366            android:enabled="true"
366-->Z:\5qren5\app\src\main\AndroidManifest.xml:304:13-35
367            android:exported="true" >
367-->Z:\5qren5\app\src\main\AndroidManifest.xml:305:13-36
368            <intent-filter>
368-->Z:\5qren5\app\src\main\AndroidManifest.xml:306:13-308:29
369                <action android:name="STATIC_MEDIA_CONTROL" />
369-->Z:\5qren5\app\src\main\AndroidManifest.xml:307:17-63
369-->Z:\5qren5\app\src\main\AndroidManifest.xml:307:25-60
370            </intent-filter>
371        </receiver>
372        <receiver
372-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:50:9-61:20
373            android:name="com.onesignal.FCMBroadcastReceiver"
373-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:51:13-62
374            android:exported="true"
374-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:52:13-36
375            android:permission="com.google.android.c2dm.permission.SEND" >
375-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:53:13-73
376
377            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
378            <intent-filter android:priority="999" >
378-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:56:13-60:29
378-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:56:28-50
379                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
379-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:17-81
379-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:25-78
380
381                <category android:name="com.qurany2019.quranyapp" />
381-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:59:17-61
381-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:59:27-58
382            </intent-filter>
383        </receiver>
384
385        <service
385-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:63:9-69:19
386            android:name="com.onesignal.HmsMessageServiceOneSignal"
386-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:64:13-68
387            android:exported="false" >
387-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:65:13-37
388            <intent-filter>
388-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:66:13-68:29
389                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
389-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:67:17-81
389-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:67:25-78
390            </intent-filter>
391        </service>
392
393        <activity
393-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:71:9-79:20
394            android:name="com.onesignal.NotificationOpenedActivityHMS"
394-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:72:13-71
395            android:exported="true"
395-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:73:13-36
396            android:noHistory="true"
396-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:74:13-37
397            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
397-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:75:13-72
398            <intent-filter>
398-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:76:13-78:29
399                <action android:name="android.intent.action.VIEW" />
399-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:17-69
399-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:77:25-66
400            </intent-filter>
401        </activity>
402
403        <service
403-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:81:9-83:40
404            android:name="com.onesignal.FCMIntentService"
404-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:82:13-58
405            android:exported="false" />
405-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:83:13-37
406        <service
406-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:84:9-87:72
407            android:name="com.onesignal.FCMIntentJobService"
407-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:85:13-61
408            android:exported="false"
408-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:86:13-37
409            android:permission="android.permission.BIND_JOB_SERVICE" />
409-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:87:13-69
410        <service
410-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:88:9-91:43
411            android:name="com.onesignal.SyncService"
411-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:89:13-53
412            android:exported="false"
412-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:90:13-37
413            android:stopWithTask="true" />
413-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:91:13-40
414        <service
414-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:92:9-95:72
415            android:name="com.onesignal.SyncJobService"
415-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:93:13-56
416            android:exported="false"
416-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:94:13-37
417            android:permission="android.permission.BIND_JOB_SERVICE" />
417-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:95:13-69
418
419        <activity
419-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:97:9-100:75
420            android:name="com.onesignal.PermissionsActivity"
420-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:98:13-61
421            android:exported="false"
421-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:99:13-37
422            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
422-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:100:13-72
423
424        <receiver
424-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:102:9-104:39
425            android:name="com.onesignal.NotificationDismissReceiver"
425-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:103:13-69
426            android:exported="true" />
426-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:104:13-36
427        <receiver
427-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:105:9-112:20
428            android:name="com.onesignal.BootUpReceiver"
428-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:106:13-56
429            android:exported="true" >
429-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:107:13-36
430            <intent-filter>
430-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:108:13-111:29
431                <action android:name="android.intent.action.BOOT_COMPLETED" />
431-->Z:\5qren5\app\src\main\AndroidManifest.xml:78:17-79
431-->Z:\5qren5\app\src\main\AndroidManifest.xml:78:25-76
432                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
432-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:110:17-82
432-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:110:25-79
433            </intent-filter>
434        </receiver>
435        <receiver
435-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:113:9-119:20
436            android:name="com.onesignal.UpgradeReceiver"
436-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:114:13-57
437            android:exported="true" >
437-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:115:13-36
438            <intent-filter>
438-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:116:13-118:29
439                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
439-->Z:\5qren5\app\src\main\AndroidManifest.xml:79:17-84
439-->Z:\5qren5\app\src\main\AndroidManifest.xml:79:25-81
440            </intent-filter>
441        </receiver>
442
443        <activity
443-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:121:9-127:75
444            android:name="com.onesignal.NotificationOpenedReceiver"
444-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:122:13-68
445            android:excludeFromRecents="true"
445-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:123:13-46
446            android:exported="true"
446-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:124:13-36
447            android:noHistory="true"
447-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:125:13-37
448            android:taskAffinity=""
448-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:126:13-36
449            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
449-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:127:13-72
450        <activity
450-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:128:9-133:75
451            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
451-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:129:13-85
452            android:excludeFromRecents="true"
452-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:130:13-46
453            android:exported="true"
453-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:131:13-36
454            android:noHistory="true"
454-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:132:13-37
455            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
455-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:133:13-72
456        <activity
456-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:56:9-61:43
457            android:name="com.google.android.gms.ads.AdActivity"
457-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:57:13-65
458            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
458-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:58:13-122
459            android:exported="false"
459-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:59:13-37
460            android:theme="@android:style/Theme.Translucent" />
460-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:60:13-61
461
462        <provider
462-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:63:9-68:43
463            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
463-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:64:13-76
464            android:authorities="com.qurany2019.quranyapp.mobileadsinitprovider"
464-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:65:13-73
465            android:exported="false"
465-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:66:13-37
466            android:initOrder="100" />
466-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:67:13-36
467
468        <service
468-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:70:9-74:43
469            android:name="com.google.android.gms.ads.AdService"
469-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:71:13-64
470            android:enabled="true"
470-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:72:13-35
471            android:exported="false" />
471-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:73:13-37
472
473        <activity
473-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:76:9-80:43
474            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
474-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:77:13-82
475            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
475-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:78:13-122
476            android:exported="false" />
476-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:79:13-37
477        <activity
477-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:81:9-88:43
478            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
478-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:82:13-82
479            android:excludeFromRecents="true"
479-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:83:13-46
480            android:exported="false"
480-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:84:13-37
481            android:launchMode="singleTask"
481-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:85:13-44
482            android:taskAffinity=""
482-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:86:13-36
483            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
483-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:87:13-72
484
485        <property
485-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:90:9-92:62
486            android:name="android.adservices.AD_SERVICES_CONFIG"
486-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:91:13-65
487            android:resource="@xml/gma_ad_services_config" />
487-->[com.google.android.gms:play-services-ads-lite:23.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8dc2b09e27d5b33dec3b58c3bbd93119\transformed\jetified-play-services-ads-lite-23.0.0\AndroidManifest.xml:92:13-59
488
489        <provider
489-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:29:9-37:20
490            android:name="androidx.startup.InitializationProvider"
490-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:30:13-67
491            android:authorities="com.qurany2019.quranyapp.androidx-startup"
491-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:31:13-68
492            android:exported="false" >
492-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:32:13-37
493            <meta-data
493-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:34:13-36:52
494                android:name="androidx.work.WorkManagerInitializer"
494-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:35:17-68
495                android:value="androidx.startup" />
495-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:36:17-49
496            <meta-data
496-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\476333e389f7f9b26bd2efa70aa4b0d2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
497                android:name="androidx.emoji2.text.EmojiCompatInitializer"
497-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\476333e389f7f9b26bd2efa70aa4b0d2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
498                android:value="androidx.startup" />
498-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\476333e389f7f9b26bd2efa70aa4b0d2\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
499            <meta-data
499-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\76863eeb05db1cf66fc27a47247b8943\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
500                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
500-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\76863eeb05db1cf66fc27a47247b8943\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
501                android:value="androidx.startup" />
501-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-3\76863eeb05db1cf66fc27a47247b8943\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
502            <meta-data
502-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
503                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
503-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
504                android:value="androidx.startup" />
504-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
505        </provider>
506
507        <service
507-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
508            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
508-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
509            android:directBootAware="false"
509-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
510            android:enabled="@bool/enable_system_alarm_service_default"
510-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
511            android:exported="false" />
511-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
512        <service
512-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
513            android:name="androidx.work.impl.background.systemjob.SystemJobService"
513-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
514            android:directBootAware="false"
514-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
515            android:enabled="@bool/enable_system_job_service_default"
515-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
516            android:exported="true"
516-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
517            android:permission="android.permission.BIND_JOB_SERVICE" />
517-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
518        <service
518-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
519            android:name="androidx.work.impl.foreground.SystemForegroundService"
519-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
520            android:directBootAware="false"
520-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
521            android:enabled="@bool/enable_system_foreground_service_default"
521-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
522            android:exported="false" />
522-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
523
524        <receiver
524-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
525            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
525-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
526            android:directBootAware="false"
526-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
527            android:enabled="true"
527-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
528            android:exported="false" />
528-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
529        <receiver
529-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
530            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
530-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
531            android:directBootAware="false"
531-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
532            android:enabled="false"
532-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
533            android:exported="false" >
533-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
534            <intent-filter>
534-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
535                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
535-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
535-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
536                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
536-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
536-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
537            </intent-filter>
538        </receiver>
539        <receiver
539-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
540            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
540-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
541            android:directBootAware="false"
541-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
542            android:enabled="false"
542-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
543            android:exported="false" >
543-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
544            <intent-filter>
544-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
545                <action android:name="android.intent.action.BATTERY_OKAY" />
545-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
545-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
546                <action android:name="android.intent.action.BATTERY_LOW" />
546-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
546-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
547            </intent-filter>
548        </receiver>
549        <receiver
549-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
550            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
550-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
551            android:directBootAware="false"
551-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
552            android:enabled="false"
552-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
553            android:exported="false" >
553-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
554            <intent-filter>
554-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
555                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
555-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
555-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
556                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
556-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
556-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
557            </intent-filter>
558        </receiver>
559        <receiver
559-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
560            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
560-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
561            android:directBootAware="false"
561-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
562            android:enabled="false"
562-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
563            android:exported="false" >
563-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
564            <intent-filter>
564-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
565                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
565-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
565-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
566            </intent-filter>
567        </receiver>
568        <receiver
568-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
569            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
569-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
570            android:directBootAware="false"
570-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
571            android:enabled="false"
571-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
572            android:exported="false" >
572-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
573            <intent-filter>
573-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
574                <action android:name="android.intent.action.BOOT_COMPLETED" />
574-->Z:\5qren5\app\src\main\AndroidManifest.xml:78:17-79
574-->Z:\5qren5\app\src\main\AndroidManifest.xml:78:25-76
575                <action android:name="android.intent.action.TIME_SET" />
575-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
575-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
576                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
576-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
576-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
577            </intent-filter>
578        </receiver>
579        <receiver
579-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
580            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
580-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
581            android:directBootAware="false"
581-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
582            android:enabled="@bool/enable_system_alarm_service_default"
582-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
583            android:exported="false" >
583-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
584            <intent-filter>
584-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
585                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
585-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
585-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
586            </intent-filter>
587        </receiver>
588        <receiver
588-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
589            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
589-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
590            android:directBootAware="false"
590-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
591            android:enabled="true"
591-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
592            android:exported="true"
592-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
593            android:permission="android.permission.DUMP" >
593-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
594            <intent-filter>
594-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
595                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
595-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
595-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\14efbdb2b5f22c9c8d3649e0fda2fa97\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
596            </intent-filter>
597        </receiver>
598
599        <service
599-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\473a36540a6a3f05afc81986fdae718c\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
600            android:name="androidx.room.MultiInstanceInvalidationService"
600-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\473a36540a6a3f05afc81986fdae718c\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
601            android:directBootAware="true"
601-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\473a36540a6a3f05afc81986fdae718c\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
602            android:exported="false" />
602-->[androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\473a36540a6a3f05afc81986fdae718c\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
603
604        <receiver
604-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:31:9-38:20
605            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
605-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:32:13-78
606            android:exported="true"
606-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:33:13-36
607            android:permission="com.google.android.c2dm.permission.SEND" >
607-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:34:13-73
608            <intent-filter>
608-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:35:13-37:29
609                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
609-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:17-81
609-->[com.onesignal:OneSignal:4.8.7] C:\Users\<USER>\.gradle\caches\transforms-3\f5612fd94849bca2e682789e2247f5e5\transformed\jetified-OneSignal-4.8.7\AndroidManifest.xml:57:25-78
610            </intent-filter>
611        </receiver>
612        <!--
613             FirebaseMessagingService performs security checks at runtime,
614             but set to not exported to explicitly avoid allowing another app to call it.
615        -->
616        <service
616-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:44:9-51:19
617            android:name="com.google.firebase.messaging.FirebaseMessagingService"
617-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:45:13-82
618            android:directBootAware="true"
618-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:46:13-43
619            android:exported="false" >
619-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:47:13-37
620            <intent-filter android:priority="-500" >
620-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:48:13-50:29
620-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:48:28-51
621                <action android:name="com.google.firebase.MESSAGING_EVENT" />
621-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:49:17-78
621-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:49:25-75
622            </intent-filter>
623        </service>
624        <service
624-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:52:9-58:19
625            android:name="com.google.firebase.components.ComponentDiscoveryService"
625-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:53:13-84
626            android:directBootAware="true"
626-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:34:13-43
627            android:exported="false" >
627-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:54:13-37
628            <meta-data
628-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:55:13-57:85
629                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
629-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:56:17-119
630                android:value="com.google.firebase.components.ComponentRegistrar" />
630-->[com.google.firebase:firebase-messaging:23.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\f417b61d0489d86751804a92dd2d7922\transformed\jetified-firebase-messaging-23.1.2\AndroidManifest.xml:57:17-82
631            <meta-data
631-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\436adc2df428272ba3ba5e25ae1f544d\transformed\jetified-play-services-measurement-api-21.2.2\AndroidManifest.xml:31:13-33:85
632                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
632-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\436adc2df428272ba3ba5e25ae1f544d\transformed\jetified-play-services-measurement-api-21.2.2\AndroidManifest.xml:32:17-139
633                android:value="com.google.firebase.components.ComponentRegistrar" />
633-->[com.google.android.gms:play-services-measurement-api:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\436adc2df428272ba3ba5e25ae1f544d\transformed\jetified-play-services-measurement-api-21.2.2\AndroidManifest.xml:33:17-82
634            <meta-data
634-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\8dcc29ee988a860bc005a3c38ffa2c1c\transformed\jetified-firebase-installations-17.1.3\AndroidManifest.xml:17:13-19:85
635                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
635-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\8dcc29ee988a860bc005a3c38ffa2c1c\transformed\jetified-firebase-installations-17.1.3\AndroidManifest.xml:18:17-127
636                android:value="com.google.firebase.components.ComponentRegistrar" />
636-->[com.google.firebase:firebase-installations:17.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\8dcc29ee988a860bc005a3c38ffa2c1c\transformed\jetified-firebase-installations-17.1.3\AndroidManifest.xml:19:17-82
637            <meta-data
637-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\73cdcc5f36552a4ee8afecb7b581d0be\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
638                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
638-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\73cdcc5f36552a4ee8afecb7b581d0be\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
639                android:value="com.google.firebase.components.ComponentRegistrar" />
639-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\transforms-3\73cdcc5f36552a4ee8afecb7b581d0be\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
640        </service>
641
642        <receiver
642-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:29:9-33:20
643            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
643-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:30:13-85
644            android:enabled="true"
644-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:31:13-35
645            android:exported="false" >
645-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:32:13-37
646        </receiver>
647
648        <service
648-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:35:9-38:40
649            android:name="com.google.android.gms.measurement.AppMeasurementService"
649-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:36:13-84
650            android:enabled="true"
650-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:37:13-35
651            android:exported="false" />
651-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:38:13-37
652        <service
652-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:39:9-43:72
653            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
653-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:40:13-87
654            android:enabled="true"
654-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:41:13-35
655            android:exported="false"
655-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:42:13-37
656            android:permission="android.permission.BIND_JOB_SERVICE" />
656-->[com.google.android.gms:play-services-measurement:21.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\c02263d9bf1d3c5cf390e9d50860252b\transformed\jetified-play-services-measurement-21.2.2\AndroidManifest.xml:43:13-69
657
658        <activity
658-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b2a011d45a9790c5d3d2d8a85cf94f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
659            android:name="com.google.android.gms.common.api.GoogleApiActivity"
659-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b2a011d45a9790c5d3d2d8a85cf94f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
660            android:exported="false"
660-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b2a011d45a9790c5d3d2d8a85cf94f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
661            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
661-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\16b2a011d45a9790c5d3d2d8a85cf94f\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
662
663        <provider
663-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:25:9-30:39
664            android:name="com.google.firebase.provider.FirebaseInitProvider"
664-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:26:13-77
665            android:authorities="com.qurany2019.quranyapp.firebaseinitprovider"
665-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:27:13-72
666            android:directBootAware="true"
666-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:28:13-43
667            android:exported="false"
667-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:29:13-37
668            android:initOrder="100" />
668-->[com.google.firebase:firebase-common:20.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\da65a218a6f244970e454d0b8712ee02\transformed\jetified-firebase-common-20.3.2\AndroidManifest.xml:30:13-36
669
670        <meta-data
670-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca4fbc13427668329376b62694d568b5\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
671            android:name="com.google.android.gms.version"
671-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca4fbc13427668329376b62694d568b5\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
672            android:value="@integer/google_play_services_version" />
672-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca4fbc13427668329376b62694d568b5\transformed\jetified-play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
673
674        <uses-library
674-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\a23b3279c497bc1c62c4725e3e0cfed3\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
675            android:name="android.ext.adservices"
675-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\a23b3279c497bc1c62c4725e3e0cfed3\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
676            android:required="false" />
676-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-3\a23b3279c497bc1c62c4725e3e0cfed3\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
677
678        <service
678-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\17f1cd8ce3c151d6997d64099d580790\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
679            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
679-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\17f1cd8ce3c151d6997d64099d580790\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
680            android:exported="false" >
680-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\17f1cd8ce3c151d6997d64099d580790\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
681            <meta-data
681-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\17f1cd8ce3c151d6997d64099d580790\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
682                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
682-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\17f1cd8ce3c151d6997d64099d580790\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
683                android:value="cct" />
683-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\17f1cd8ce3c151d6997d64099d580790\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
684        </service>
685        <service
685-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
686            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
686-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
687            android:exported="false"
687-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
688            android:permission="android.permission.BIND_JOB_SERVICE" >
688-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
689        </service>
690
691        <receiver
691-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
692            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
692-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
693            android:exported="false" />
693-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-3\fbc2146aeb5edf04140dcaa97bf3e19b\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
694        <receiver
694-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
695            android:name="androidx.profileinstaller.ProfileInstallReceiver"
695-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
696            android:directBootAware="false"
696-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
697            android:enabled="true"
697-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
698            android:exported="true"
698-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
699            android:permission="android.permission.DUMP" >
699-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
700            <intent-filter>
700-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
701                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
701-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
701-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
702            </intent-filter>
703            <intent-filter>
703-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
704                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
704-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
704-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
705            </intent-filter>
706            <intent-filter>
706-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
707                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
707-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
707-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
708            </intent-filter>
709            <intent-filter>
709-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
710                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
710-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
710-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\ff67009c55df8bc1d972d91accd3437d\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
711            </intent-filter>
712        </receiver>
713    </application>
714
715</manifest>

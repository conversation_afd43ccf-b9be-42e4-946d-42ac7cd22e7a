package com.qurany2019.quranyapp;

import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ImageView;
import android.content.Context;

/**
 * مساعد الانيميشن لمشغل القرآن
 */
public class AnimationHelper {
    
    private Context context;
    
    public AnimationHelper(Context context) {
        this.context = context;
    }
    
    /**
     * تطبيق انيميشن الدوران على الصورة
     */
    public void startRotationAnimation(ImageView imageView) {
        try {
            Animation rotateAnimation = AnimationUtils.loadAnimation(context, R.anim.rotate_animation);
            imageView.startAnimation(rotateAnimation);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * تطبيق انيميشن النبضة على الصورة
     */
    public void startPulseAnimation(ImageView imageView) {
        try {
            Animation pulseAnimation = AnimationUtils.loadAnimation(context, R.anim.pulse_animation);
            imageView.startAnimation(pulseAnimation);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * تطبيق انيميشن الإضاءة على العنصر
     */
    public void startGlowAnimation(View view) {
        try {
            Animation glowAnimation = AnimationUtils.loadAnimation(context, R.anim.glow_animation);
            view.startAnimation(glowAnimation);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * تطبيق انيميشن الضغط على الزر
     */
    public void startButtonClickAnimation(View button) {
        try {
            Animation clickAnimation = AnimationUtils.loadAnimation(context, R.anim.button_click_animation);
            button.startAnimation(clickAnimation);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * تطبيق انيميشن الظهور التدريجي
     */
    public void startFadeInAnimation(View view) {
        try {
            Animation fadeInAnimation = AnimationUtils.loadAnimation(context, R.anim.fade_in_animation);
            view.startAnimation(fadeInAnimation);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * إيقاف جميع الانيميشن على العنصر
     */
    public void stopAllAnimations(View view) {
        try {
            view.clearAnimation();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * تطبيق انيميشن مخصص للتشغيل
     */
    public void startPlayingAnimation(ImageView quranIcon, View... glowViews) {
        try {
            // دوران الصورة
            startRotationAnimation(quranIcon);
            
            // تأثير الإضاءة على العناصر المحيطة
            for (View glowView : glowViews) {
                startGlowAnimation(glowView);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * إيقاف انيميشن التشغيل
     */
    public void stopPlayingAnimation(ImageView quranIcon, View... glowViews) {
        try {
            // إيقاف دوران الصورة
            stopAllAnimations(quranIcon);
            
            // إيقاف تأثير الإضاءة
            for (View glowView : glowViews) {
                stopAllAnimations(glowView);
            }
            
            // تطبيق انيميشن النبضة البسيط
            startPulseAnimation(quranIcon);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

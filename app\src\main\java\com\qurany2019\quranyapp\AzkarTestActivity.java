package com.qurany2019.quranyapp;

import android.os.Bundle;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import java.util.Calendar;

/**
 * نشاط تجريبي لاختبار نظام إشعارات الأذكار
 */
public class AzkarTestActivity extends AppCompatActivity {
    
    private AzkarNotificationHelper notificationHelper;
    private TextView statusText;
    private Button testButton;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // إنشاء واجهة بسيطة للاختبار
        createSimpleUI();
        
        // إنشاء مساعد الإشعارات
        notificationHelper = new AzkarNotificationHelper(this);
        
        // عرض الحالة الحالية
        updateStatus();
    }
    
    private void createSimpleUI() {
        // إنشاء تخطيط بسيط برمجياً
        android.widget.LinearLayout layout = new android.widget.LinearLayout(this);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setPadding(50, 50, 50, 50);
        
        // نص الحالة
        statusText = new TextView(this);
        statusText.setTextSize(16);
        statusText.setPadding(0, 0, 0, 30);
        layout.addView(statusText);
        
        // زر الاختبار
        testButton = new Button(this);
        testButton.setText("اختبار التذكير الذكي");
        testButton.setOnClickListener(v -> testSmartReminder());
        layout.addView(testButton);
        
        setContentView(layout);
    }
    
    private void updateStatus() {
        String currentAzkar = notificationHelper.getCurrentAzkarSuggestion();
        Calendar now = Calendar.getInstance();
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        String status = "الوقت الحالي: " + hour + ":00\n";
        status += "الذكر المقترح: " + getAzkarName(currentAzkar) + "\n";
        status += "الرسالة: " + getTestMessage(currentAzkar);
        
        statusText.setText(status);
    }
    
    private String getAzkarName(String azkarType) {
        switch (azkarType) {
            case AzkarNotificationHelper.AZKAR_MORNING:
                return "أذكار الصباح";
            case AzkarNotificationHelper.AZKAR_EVENING:
                return "أذكار المساء";
            case AzkarNotificationHelper.AZKAR_SLEEP:
                return "أذكار النوم";
            case AzkarNotificationHelper.AZKAR_WAKE:
                return "أذكار الاستيقاظ";
            default:
                return "غير محدد";
        }
    }
    
    private String getTestMessage(String azkarType) {
        Calendar now = Calendar.getInstance();
        int hour = now.get(Calendar.HOUR_OF_DAY);
        
        switch (azkarType) {
            case AzkarNotificationHelper.AZKAR_MORNING:
                if (hour >= 6 && hour < 11) {
                    return "🌅 حان وقت أذكار الصباح - بارك الله في يومك";
                } else {
                    return "🌅 لم تقرأ أذكار الصباح بعد - اقرأها الآن";
                }
                
            case AzkarNotificationHelper.AZKAR_EVENING:
                if (hour >= 15 && hour < 19) {
                    return "🌆 حان وقت أذكار المساء - تقبل الله منك";
                } else {
                    return "🌆 لم تقرأ أذكار المساء بعد - اقرأها الآن";
                }
                
            case AzkarNotificationHelper.AZKAR_SLEEP:
                if (hour >= 22 || hour < 6) {
                    return "🌙 حان وقت أذكار النوم - ليلة مباركة";
                } else {
                    return "🌙 لا تنس أذكار النوم الليلة";
                }
                
            case AzkarNotificationHelper.AZKAR_WAKE:
                return "☀️ ابدأ يومك بأذكار الاستيقاظ";
                
            default:
                return "📿 حان وقت الأذكار - بارك الله فيك";
        }
    }
    
    private void testSmartReminder() {
        // تفعيل التذكير الذكي
        notificationHelper.activateSmartReminder();
        
        // تحديث الحالة
        updateStatus();
        
        // عرض رسالة تأكيد
        android.widget.Toast.makeText(this, "تم تفعيل التذكير الذكي!", android.widget.Toast.LENGTH_SHORT).show();
    }
}

<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/azkarCardView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:layoutDirection="rtl">

        <!-- العنوان (إذا وجد) -->
        <TextView
            android:id="@+id/azkarTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/islamic_green_dark"
            android:fontFamily="@font/arabic_font"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="8dp"
            android:visibility="gone" />

        <!-- محتوى الذكر -->
        <TextView
            android:id="@+id/azkarContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="18sp"
            android:textColor="@color/black"
            android:fontFamily="@font/arabic_font"
            android:lineSpacingExtra="4dp"
            android:gravity="center"
            android:layout_marginBottom="12dp" />

        <!-- شريط التقدم -->
        <ProgressBar
            android:id="@+id/azkarProgressBar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginBottom="8dp"
            android:progressTint="@color/islamic_green"
            android:progressBackgroundTint="@color/islamic_light_gray" />

        <!-- معلومات العداد والحالة -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <!-- عداد التقدم -->
            <TextView
                android:id="@+id/azkarCount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/arabic_font"
                android:gravity="start"
                android:textColor="@color/islamic_blue"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- حالة الإنجاز -->
            <TextView
                android:id="@+id/azkarStatus"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/arabic_font"
                android:gravity="end"
                android:textColor="@color/islamic_gray"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- أزرار التحكم -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <!-- زر الإعادة تعيين -->
            <Button
                android:id="@+id/resetButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:background="@drawable/button_reset_bg"
                android:fontFamily="@font/arabic_font"
                android:text="إعادة تعيين"
                android:textColor="@color/white"
                android:textSize="12sp" />

            <!-- زر التسبيح -->
            <Button
                android:id="@+id/incrementButton"
                android:layout_width="0dp"
                android:layout_height="40dp"
                android:layout_weight="2"
                android:layout_marginStart="8dp"
                android:background="@drawable/button_primary_bg"
                android:fontFamily="@font/arabic_font"
                android:text="تسبيح +1"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>

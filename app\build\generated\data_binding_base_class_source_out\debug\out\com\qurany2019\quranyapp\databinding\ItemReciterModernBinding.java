// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemReciterModernBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton playButton;

  @NonNull
  public final ImageView reciterIcon;

  @NonNull
  public final TextView surahCountText;

  @NonNull
  public final TextView txtRecitesName;

  private ItemReciterModernBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton playButton, @NonNull ImageView reciterIcon,
      @NonNull TextView surahCountText, @NonNull TextView txtRecitesName) {
    this.rootView = rootView;
    this.playButton = playButton;
    this.reciterIcon = reciterIcon;
    this.surahCountText = surahCountText;
    this.txtRecitesName = txtRecitesName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemReciterModernBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemReciterModernBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_reciter_modern, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemReciterModernBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.playButton;
      MaterialButton playButton = ViewBindings.findChildViewById(rootView, id);
      if (playButton == null) {
        break missingId;
      }

      id = R.id.reciterIcon;
      ImageView reciterIcon = ViewBindings.findChildViewById(rootView, id);
      if (reciterIcon == null) {
        break missingId;
      }

      id = R.id.surahCountText;
      TextView surahCountText = ViewBindings.findChildViewById(rootView, id);
      if (surahCountText == null) {
        break missingId;
      }

      id = R.id.txtRecitesName;
      TextView txtRecitesName = ViewBindings.findChildViewById(rootView, id);
      if (txtRecitesName == null) {
        break missingId;
      }

      return new ItemReciterModernBinding((MaterialCardView) rootView, playButton, reciterIcon,
          surahCountText, txtRecitesName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

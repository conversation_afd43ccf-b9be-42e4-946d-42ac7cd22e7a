package com.qurany2019.quranyapp.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.Ignore;

@Entity(tableName = "azkar_table")
public class AzkarEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "azkar_id")
    private String azkarId;

    @ColumnInfo(name = "title")
    private String title;

    @ColumnInfo(name = "content")
    private String content;

    @ColumnInfo(name = "target_count")
    private int targetCount;

    @ColumnInfo(name = "category")
    private String category; // morning, evening, sleep, wake, prayer

    @ColumnInfo(name = "order_index")
    private int orderIndex;

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Constructors
    public AzkarEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public AzkarEntity(String azkarId, String title, String content, int targetCount, String category, int orderIndex) {
        this.azkarId = azkarId;
        this.title = title;
        this.content = content;
        this.targetCount = targetCount;
        this.category = category;
        this.orderIndex = orderIndex;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAzkarId() {
        return azkarId;
    }

    public void setAzkarId(String azkarId) {
        this.azkarId = azkarId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getTargetCount() {
        return targetCount;
    }

    public void setTargetCount(int targetCount) {
        this.targetCount = targetCount;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    public void updateTimestamp() {
        this.updatedAt = System.currentTimeMillis();
    }

    @Override
    public String toString() {
        return "AzkarEntity{" +
                "id=" + id +
                ", azkarId='" + azkarId + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", targetCount=" + targetCount +
                ", category='" + category + '\'' +
                ", orderIndex=" + orderIndex +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}

package com.qurany2019.quranyapp;

import android.app.AlertDialog;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import android.content.Intent;
import android.widget.Toast;
import android.view.View;
import android.widget.TextView;
import android.widget.ImageView;
import android.widget.ListView;
import android.view.ViewGroup;
import android.view.LayoutInflater;
import android.widget.BaseAdapter;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.SearchView;
import android.app.SearchManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.ActivityNotFoundException;
import android.net.Uri;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import java.util.Locale;
import android.os.Build;
import android.content.pm.PackageManager;
import android.Manifest;
import androidx.core.content.ContextCompat;
import androidx.core.app.ActivityCompat;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import java.io.File;
import java.util.ArrayList;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import android.view.View;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;

import android.app.ProgressDialog;
import android.app.SearchManager;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.widget.Toast;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.Manifest;
import android.app.DownloadManager;
import android.database.Cursor;
import android.content.BroadcastReceiver;
import android.content.IntentFilter;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;

import androidx.appcompat.app.AppCompatActivity;

import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.SearchView;
import android.widget.TextView;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.content.ActivityNotFoundException;
import android.app.SearchManager;
import android.app.ProgressDialog;
import androidx.appcompat.app.AppCompatActivity;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.io.BufferedInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.File;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Locale;

/**
 * Created by java_dude on 06/06/18.
 */

public class AyaList extends AppCompatActivity {
    public static final int DIALOG_DOWNLOAD_PROGRESS = 0;
    private static final int PERMISSION_REQUEST_CODE = 123;
    ListView listAya;
    private AdView mAdView;

    public static ArrayList<AuthorClass> listrecitesAya = new ArrayList<AuthorClass>();
    public static ArrayList<String> recitersList = new ArrayList<String>();
    private ProgressDialog mProgressDialog;
    View LayoutLoading;
    ProgressBar progressBar;
    TextView downloadingText;
    static String RecitesName = "";
    String RecitesAYA = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق اللغة المحفوظة قبل إنشاء الواجهة
        applyLanguageSettings();
        // إعداد Edge-to-Edge مع Safe Area
        setupEdgeToEdge();

        setContentView(R.layout.activity_aya_list);

        //get Recites
        Bundle b = getIntent().getExtras();
        RecitesName = b.getString("RecitesName");

        // إعداد العناصر
        try {
            listAya = (ListView) findViewById(R.id.listView);
            LayoutLoading = findViewById(R.id.LayoutLoading);
            progressBar = (ProgressBar) findViewById(R.id.progressBar);
            downloadingText = (TextView) findViewById(R.id.downloadingText);
        } catch (Exception e) {
            // في حالة عدم وجود العناصر، استخدم قيم افتراضية
            android.util.Log.e("AyaList", "خطأ في العثور على العناصر: " + e.getMessage());
        }

        // إعداد النصوص في الهيدر إذا وجدت
        try {
            TextView reciterNameText = findViewById(R.id.reciterNameText);
            TextView surahCountText = findViewById(R.id.surahCountText);
            if (reciterNameText != null) {
                reciterNameText.setText(RecitesName);
            }
            if (surahCountText != null) {
                surahCountText.setText(getString(R.string.surah_count_text));
            }
        } catch (Exception e) {
            // تجاهل الأخطاء في العناصر الاختيارية
        }

        //get list of recites - العودة للطريقة القديمة السريعة
        listrecitesAya.clear();
        LnaguageClass lc = new LnaguageClass();
        listrecitesAya = lc.GuranAya(RecitesName);
        listAya.setAdapter(new VivzAdapter(listrecitesAya));

        LayoutLoading.setVisibility(android.view.View.GONE);

        // إعداد الإعلانات بشكل آمن
        try {
            mAdView = (AdView) findViewById(R.id.adView);
            if (mAdView != null) {
                AdRequest adRequest = new AdRequest.Builder().build();
                mAdView.loadAd(adRequest);
            }
        } catch (Exception e) {
            // تجاهل أخطاء الإعلانات
            android.util.Log.e("AyaList", getString(R.string.ads_loading_error) + ": " + e.getMessage());
        }

    }

    private void DisplayAya() {
        // فحص إذا كانت السورة محملة محلياً أولاً
        if (isFileDownloaded(RecitesAYA)) {
            // تشغيل الملف المحلي
            playLocalFile(RecitesAYA);
        } else {
            // تشغيل من الإنترنت
            Intent intent = new Intent(this, managerdb.class);
            intent.putExtra("RecitesName", RecitesName);
            intent.putExtra("RecitesAYA", RecitesAYA);
            intent.putExtra("IsLocalFile", false);
            startActivity(intent);
        }
    }

    // تشغيل الملف المحلي
    private void playLocalFile(String fileName) {
        String localPath = getLocalFilePath(fileName);

        Intent intent = new Intent(this, managerdb.class);
        intent.putExtra("RecitesName", RecitesName);
        intent.putExtra("RecitesAYA", RecitesAYA);
        intent.putExtra("LocalFilePath", localPath); // إضافة مسار الملف المحلي
        intent.putExtra("IsLocalFile", true); // علامة أن هذا ملف محلي
        startActivity(intent);

        // إظهار رسالة للمستخدم
        Toast.makeText(this, "🎵 تشغيل السورة من التخزين المحلي", Toast.LENGTH_SHORT).show();
    }

    public void LoadAya() {
        ListView list = (ListView) findViewById(R.id.listView);

//get list of recites
        LnaguageClass lc = new LnaguageClass();
        listrecitesAya = lc.GuranAya(RecitesName);
        listAya.setAdapter(new VivzAdapter(listrecitesAya));


    }


    SearchView searchView;
    Menu myMenu;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_aya_list, menu);
        myMenu = menu;
        // Associate searchable configuration with the SearchView
        SearchManager searchManager = (SearchManager) getSystemService(Context.SEARCH_SERVICE);
        searchView = (SearchView) menu.findItem(R.id.search).getActionView();
        searchView.setSearchableInfo(searchManager.getSearchableInfo(getComponentName()));
        //final Context co=this;
        searchView.setOnQueryTextListener(new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {

                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                ArrayList<AuthorClass> listrecitestemp = new ArrayList<AuthorClass>();
                for (AuthorClass listrecitesitem : listrecitesAya) {
                    if (listrecitesitem.RealName.contains(newText)) {
                        listrecitestemp.add(listrecitesitem);

                    }
                }
                listAya.setAdapter(new VivzAdapter(listrecitestemp));
                return false;
            }
        });
        //   searchView.setOnCloseListener(this);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        if (id == R.id.gbackmenu) {

            // rate app
            if (ISDonwloading != true) //it he isnot donlaidng know
                if (SaveSettings.IsRated == 0) {
                    DialogInterface.OnClickListener dialogClickListener = new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            switch (which) {
                                case DialogInterface.BUTTON_POSITIVE:
                                    //Yes button clicked
                                    Uri uri = Uri.parse("market://details?id=" + SaveSettings.APPURL);
                                    Intent goToMarket = new Intent(Intent.ACTION_VIEW, uri);
                                    // To count with Play market backstack, After pressing back button,
                                    // to taken back to our application, we need to add following flags to intent.
                                    goToMarket.addFlags(
                                            Intent.FLAG_ACTIVITY_MULTIPLE_TASK);
                                    try {
                                        startActivity(goToMarket);
                                    } catch (ActivityNotFoundException e) {
                                        startActivity(new Intent(Intent.ACTION_VIEW,
                                                Uri.parse("http://play.google.com/store/apps/details?id=" + SaveSettings.APPURL)));
                                    }
                                    SaveSettings.IsRated = 1;
                                    SaveSettings sv = new SaveSettings(getApplicationContext());
                                    sv.SaveData();
                                    finish();
                                    break;

                                case DialogInterface.BUTTON_NEGATIVE:
                                    //No button clicked
                                    finish();
                                    break;
                            }
                        }
                    };

                    MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(this);
                    builder.setMessage(getResources().getString(R.string.rateq)).setPositiveButton("Yes", dialogClickListener)
                            .setNegativeButton("No", dialogClickListener).show();
                } else {
                    finish();
                }
        }

        return super.onOptionsItemSelected(item);
    }

    // دالة لتطبيق إعدادات اللغة
    private void applyLanguageSettings() {
        SharedPreferences prefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = prefs.getBoolean("is_arabic", true);

        // تحديث SaveSettings.LanguageSelect ليتطابق مع اللغة المحفوظة
        SaveSettings.LanguageSelect = isArabic ? 1 : 2;

        // تطبيق اللغة على النشاط الحالي
        setLocale(isArabic ? "ar" : "en");
    }

    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    // إزالة Edge-to-Edge لإظهار شريط الأزرار بشكل طبيعي
    private void setupEdgeToEdge() {
        // تم إزالة جميع إعدادات fullscreen لإظهار شريط الأزرار بشكل طبيعي
    }

    /// file download
    public void startDownload(String ImgUrl, String ServerName) {
        // فحص الأذونات أولاً
        if (!checkPermissions()) {
            requestPermissions();
            return;
        }

        // فحص الاتصال بالإنترنت
        if (!isNetworkAvailable()) {
            Toast.makeText(this, "لا يوجد اتصال بالإنترنت", Toast.LENGTH_SHORT).show();
            return;
        }

        // RecitesAYA يجب أن يكون الفهرس وليس ServerName
        // RecitesAYA = ServerName; // هذا خطأ!
        String url = ImgUrl;

        // استخدام Thread بدلاً من AsyncTask المهجورة
        new Thread(() -> downloadFile(url)).start();
    }

    // فحص الأذونات
    private boolean checkPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ - نحتاج READ_MEDIA_AUDIO
            return ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_AUDIO)
                    == PackageManager.PERMISSION_GRANTED;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6-12 - نحتاج WRITE_EXTERNAL_STORAGE
            return ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    == PackageManager.PERMISSION_GRANTED;
        }
        return true; // Android 5 وأقل لا يحتاج أذونات
    }

    // طلب الأذونات
    private void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.READ_MEDIA_AUDIO},
                PERMISSION_REQUEST_CODE);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,
                            Manifest.permission.READ_EXTERNAL_STORAGE},
                PERMISSION_REQUEST_CODE);
        }
    }

    // فحص الاتصال بالإنترنت
    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager =
            (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    // معالجة نتيجة طلب الأذونات
    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "تم منح الأذونات بنجاح", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "الأذونات مطلوبة للتنزيل", Toast.LENGTH_LONG).show();
            }
        }
    }

    // دالة التنزيل البسيطة والفعالة
    private void downloadFile(String urlString) {
        // الحصول على اسم السورة من الرقم
        String surahNameTemp = "السورة الكريمة";
        try {
            int surahIndex = Integer.parseInt(RecitesAYA);
            if (surahIndex >= 0 && surahIndex < listrecitesAya.size()) {
                String fullName = listrecitesAya.get(surahIndex).RealName;
                if (fullName.startsWith("سورة ")) {
                    surahNameTemp = fullName.substring(5); // إزالة "سورة "
                } else {
                    surahNameTemp = fullName;
                }
            }
        } catch (Exception e) {
            // في حالة الخطأ، استخدم الاسم الافتراضي
        }
        final String surahName = surahNameTemp;
        new Thread(() -> {
            try {
                // إظهار شريط التقدم
                runOnUiThread(() -> {
                    LayoutLoading.setVisibility(android.view.View.VISIBLE);
                    progressBar.setProgress(0);
                    ISDonwloading = true;
                    // تحديث نص شريط التقدم
                    if (downloadingText != null) {
                        downloadingText.setText("جاري تنزيل سورة " + surahName + "...");
                    }
                    android.widget.Toast.makeText(AyaList.this, "📥 جاري تنزيل سورة " + surahName + "...", android.widget.Toast.LENGTH_SHORT).show();
                });

                // إنشاء مجلد التخزين في مجلد الموسيقى العام
                File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
                File quranAppDir = new File(musicDir, "قرآني");
                File audioDir = new File(quranAppDir, RecitesName);
                if (!audioDir.exists()) {
                    audioDir.mkdirs();
                }

                // إنشاء اسم الملف الجميل مع اسم السورة والقارئ
                String beautifulFileName = createBeautifulFileName(surahName, RecitesName);
                File outputFile = new File(audioDir, beautifulFileName);

                // حذف الملف إذا كان موجوداً
                if (outputFile.exists()) {
                    outputFile.delete();
                }

                // إنشاء OkHttp client
                okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
                okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(urlString)
                    .build();

                // تنفيذ الطلب
                okhttp3.Response response = client.newCall(request).execute();

                if (!response.isSuccessful()) {
                    throw new java.io.IOException("فشل في الاتصال: " + response);
                }

                okhttp3.ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    throw new java.io.IOException("لا توجد بيانات للتنزيل");
                }

                long contentLength = responseBody.contentLength();
                java.io.InputStream inputStream = responseBody.byteStream();
                java.io.FileOutputStream outputStream = new java.io.FileOutputStream(outputFile);

                byte[] buffer = new byte[4096];
                long downloadedBytes = 0;
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;

                    // تحديث شريط التقدم
                    if (contentLength > 0) {
                        final int progress = (int) ((downloadedBytes * 100) / contentLength);
                        runOnUiThread(() -> progressBar.setProgress(progress));
                    }
                }

                outputStream.close();
                inputStream.close();
                response.close();

                // التنزيل انتهى بنجاح
                runOnUiThread(() -> {
                    LayoutLoading.setVisibility(android.view.View.GONE);
                    ISDonwloading = false;
                    LoadAya(); // إعادة تحميل القائمة
                    android.widget.Toast.makeText(AyaList.this, "✅ تم تنزيل سورة " + surahName + " بنجاح\n🎧 جاهزة للاستماع بدون إنترنت", android.widget.Toast.LENGTH_LONG).show();
                });

            } catch (Exception e) {
                // معالجة الأخطاء
                runOnUiThread(() -> {
                    LayoutLoading.setVisibility(android.view.View.GONE);
                    ISDonwloading = false;
                    String errorMessage = "❌ فشل في التنزيل: ";
                    if (e instanceof java.io.IOException) {
                        errorMessage += "مشكلة في الاتصال أو التخزين";
                    } else {
                        errorMessage += e.getMessage();
                    }
                    android.widget.Toast.makeText(AyaList.this, errorMessage, android.widget.Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }

    // فحص وجود الملف المحلي باستخدام نظام Cache الذكي
    private boolean isFileDownloaded(String fileName) {
        try {
            File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
            File quranAppDir = new File(musicDir, "قرآني");
            File audioDir = new File(quranAppDir, RecitesName);

            // تحديث cache الملفات
            updateLocalFilesCache(audioDir, RecitesName);

            // استخدام نفس نظام البحث الذكي من LnaguageClass
            return isFileInCache(RecitesName, fileName);

        } catch (Exception e) {
            android.util.Log.e("FILE_DOWNLOAD_CHECK", "خطأ في فحص الملف: " + e.getMessage());
            return false;
        }
    }

    // Cache للملفات المحلية (نفس النظام من LnaguageClass)
    private static java.util.Map<String, java.util.Set<String>> localFilesCache = new java.util.HashMap<>();
    private static long lastCacheUpdate = 0;
    private static final long CACHE_VALIDITY = 30000; // 30 ثانية

    /**
     * تحديث cache الملفات المحلية
     */
    private void updateLocalFilesCache(File audioDir, String reciterName) {
        try {
            long currentTime = System.currentTimeMillis();

            if (currentTime - lastCacheUpdate > CACHE_VALIDITY || !localFilesCache.containsKey(reciterName)) {
                java.util.Set<String> files = new java.util.HashSet<>();

                if (audioDir.exists()) {
                    String[] fileList = audioDir.list();
                    if (fileList != null) {
                        android.util.Log.d("AYALIST_CACHE", "📁 فحص مجلد: " + audioDir.getAbsolutePath());
                        for (String fileName : fileList) {
                            android.util.Log.d("AYALIST_CACHE", "📄 ملف موجود: " + fileName);
                            if (fileName.endsWith(".mp3")) {
                                String baseName = fileName.substring(0, fileName.lastIndexOf(".mp3"));
                                files.add(baseName);
                                android.util.Log.d("AYALIST_CACHE", "✅ تم إضافة للـ cache: " + baseName);
                            }
                        }
                    }
                }

                localFilesCache.put(reciterName, files);
                lastCacheUpdate = currentTime;
                android.util.Log.d("AYALIST_CACHE", "تم تحديث cache للقارئ: " + reciterName + " - عدد الملفات: " + files.size());
            }
        } catch (Exception e) {
            android.util.Log.e("AYALIST_CACHE", "خطأ في تحديث cache: " + e.getMessage());
        }
    }

    /**
     * فحص وجود الملف في cache (نفس النظام من LnaguageClass)
     */
    private boolean isFileInCache(String reciterName, String surahCode) {
        try {
            java.util.Set<String> files = localFilesCache.get(reciterName);
            if (files != null) {
                android.util.Log.d("AYALIST_CACHE", "🔍 البحث عن: " + surahCode + " في cache");
                android.util.Log.d("AYALIST_CACHE", "📋 الملفات المتاحة: " + files.toString());

                // البحث المباشر بالرقم
                boolean found = files.contains(surahCode);
                if (found) {
                    android.util.Log.d("AYALIST_CACHE", "✅ تم العثور على الملف بالرقم");
                    return true;
                }

                // البحث باسم السورة
                String surahName = getSurahNameByCode(surahCode);
                if (surahName != null) {
                    android.util.Log.d("AYALIST_CACHE", "🔍 البحث باسم السورة: " + surahName);
                    for (String fileName : files) {
                        if (fileName.contains(surahName)) {
                            android.util.Log.d("AYALIST_CACHE", "✅ تم العثور على الملف باسم السورة: " + fileName);
                            return true;
                        }
                    }
                }

                android.util.Log.d("AYALIST_CACHE", "❌ لم يتم العثور على الملف");
                return false;
            }
        } catch (Exception e) {
            android.util.Log.e("AYALIST_CACHE", "خطأ في فحص cache: " + e.getMessage());
        }
        return false;
    }

    /**
     * الحصول على اسم السورة من الرقم
     */
    private String getSurahNameByCode(String surahCode) {
        try {
            int code = Integer.parseInt(surahCode);
            String[] surahNames = {
                "الفاتحة", "البقرة", "ال عمران", "النساء", "المائدة", "الانعام", "الأعراف", "الأنفال", "التوبة", "يونس",
                "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
                "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
                "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
                "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
                "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
                "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
                "نوح", "الجن", "المزمل", "المدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
                "التكوير", "الانفطار", "المطففين", "الانشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
                "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
                "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
                "المسد", "الاخلاص", "الفلق", "الناس"
            };

            if (code >= 1 && code <= surahNames.length) {
                return surahNames[code - 1].trim();
            }
        } catch (Exception e) {
            android.util.Log.e("SURAH_NAME", "خطأ في تحويل رقم السورة: " + e.getMessage());
        }
        return null;
    }

    // الحصول على الاسم الكامل للقارئ من ServerName
    private String getFullReciterName(String serverName) {
        LnaguageClass lc = new LnaguageClass();
        ArrayList<AuthorClass> recitersList = lc.AutherList();

        for (AuthorClass reciter : recitersList) {
            if (reciter.ServerName.equals(serverName)) {
                return reciter.RealName;
            }
        }

        // إذا لم نجد الاسم، نعيد ServerName كما هو
        return serverName;
    }

    // إنشاء اسم ملف جميل مع اسم السورة والقارئ
    private String createBeautifulFileName(String surahName, String reciterServerName) {
        // الحصول على الاسم الكامل للقارئ
        String fullReciterName = getFullReciterName(reciterServerName);

        // تنظيف الأسماء من الأحرف غير المسموحة في أسماء الملفات
        String cleanSurahName = surahName.replaceAll("[\\\\/:*?\"<>|]", "");
        String cleanReciterName = fullReciterName.replaceAll("[\\\\/:*?\"<>|]", "");

        // إنشاء اسم الملف الجميل
        return cleanSurahName + " - " + cleanReciterName + ".mp3";
    }

    // فحص وجود الملف بالاسم الجميل
    private boolean isFileDownloadedByName(String fileName) {
        File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
        File quranAppDir = new File(musicDir, "قرآني");
        File audioDir = new File(quranAppDir, RecitesName);
        File audioFile = new File(audioDir, fileName);
        return audioFile.exists() && audioFile.length() > 0;
    }

    // الحصول على مسار الملف المحلي
    private String getLocalFilePath(String fileName) {
        try {
            File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
            File quranAppDir = new File(musicDir, "قرآني");
            File audioDir = new File(quranAppDir, RecitesName);

            // البحث عن الملف الحقيقي في المجلد
            if (audioDir.exists() && audioDir.isDirectory()) {
                File[] files = audioDir.listFiles();
                if (files != null) {
                    // الحصول على اسم السورة من الرقم
                    String surahName = getSurahNameByCode(fileName);

                    for (File file : files) {
                        String fileNameStr = file.getName();
                        if (fileNameStr.endsWith(".mp3")) {
                            // البحث المتقدم - عدة طرق للمطابقة
                            if (isFileMatch(fileNameStr, fileName, surahName)) {
                                android.util.Log.d("LOCAL_FILE_PATH", "✅ تم العثور على الملف: " + file.getAbsolutePath());
                                return file.getAbsolutePath();
                            }
                        }
                    }
                }
            }

            // إذا لم يتم العثور على الملف، استخدم المسار القديم
            File audioFile = new File(audioDir, fileName + ".mp3");
            android.util.Log.d("LOCAL_FILE_PATH", getString(R.string.local_file_not_found) + ": " + audioFile.getAbsolutePath());
            return audioFile.getAbsolutePath();

        } catch (Exception e) {
            android.util.Log.e("LOCAL_FILE_PATH", getString(R.string.file_search_error) + ": " + e.getMessage());
            File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
            File quranAppDir = new File(musicDir, "قرآني");
            File audioDir = new File(quranAppDir, RecitesName);
            File audioFile = new File(audioDir, fileName + ".mp3");
            return audioFile.getAbsolutePath();
        }
    }

    // دالة البحث المتقدم - تدعم جميع تنسيقات أسماء الملفات
    private boolean isFileMatch(String fileName, String surahCode, String surahName) {
        try {
            // تنظيف اسم الملف من الامتداد
            String cleanFileName = fileName.replace(".mp3", "").toLowerCase().trim();

            // 1. البحث بالرقم المباشر (001, 002, etc.)
            if (cleanFileName.contains(surahCode.toLowerCase())) {
                android.util.Log.d("FILE_MATCH", "✅ مطابقة بالرقم: " + fileName);
                return true;
            }

            // 2. البحث باسم السورة
            if (surahName != null) {
                String cleanSurahName = surahName.toLowerCase().trim();
                if (cleanFileName.contains(cleanSurahName)) {
                    android.util.Log.d("FILE_MATCH", "✅ مطابقة باسم السورة: " + fileName);
                    return true;
                }

                // 3. البحث بدون مسافات
                String noSpaceSurah = cleanSurahName.replace(" ", "");
                String noSpaceFile = cleanFileName.replace(" ", "");
                if (noSpaceFile.contains(noSpaceSurah)) {
                    android.util.Log.d("FILE_MATCH", "✅ مطابقة بدون مسافات: " + fileName);
                    return true;
                }

                // 4. البحث مع كلمة "سورة"
                if (cleanFileName.contains("سورة " + cleanSurahName) ||
                    cleanFileName.contains("سورة" + cleanSurahName)) {
                    android.util.Log.d("FILE_MATCH", "✅ مطابقة مع كلمة سورة: " + fileName);
                    return true;
                }
            }

            android.util.Log.d("FILE_MATCH", "❌ لا توجد مطابقة: " + fileName);
            return false;

        } catch (Exception e) {
            android.util.Log.e("FILE_MATCH", "خطأ في المطابقة: " + e.getMessage());
            return false;
        }
    }



    public boolean ISDonwloading = false;



    //=====================================
    class VivzAdapter extends BaseAdapter {


        ArrayList<AuthorClass> listrecitesLocal;

        VivzAdapter(ArrayList<AuthorClass> listrecites) {

            listrecitesLocal = new ArrayList<AuthorClass>();
            listrecitesLocal = listrecites;

        }


        @Override
        public int getCount() {
            return this.listrecitesLocal.size();
        }

        @Override
        public String getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int i, View view, ViewGroup viewGroup) {
            LayoutInflater mInflater = getLayoutInflater();
            View row = mInflater.inflate(R.layout.single_rowayalist, null);

            TextView title = (TextView) row.findViewById(R.id.textView1);
            TextView cost = (TextView) row.findViewById(R.id.textView2);
            TextView surahNumber = (TextView) row.findViewById(R.id.surahNumber);
            android.view.View image = row.findViewById(R.id.imageView); // زر التشغيل
            android.view.View budownload = row.findViewById(R.id.button); // زر التنزيل
            ImageView quranCoverImage = (ImageView) row.findViewById(R.id.quranCoverImage); // صورة غلاف القرآن

            final AuthorClass temp = this.listrecitesLocal.get(i);
            final String ServerName = temp.ServerName;

            // تحديد رقم السورة وإظهار صورة غلاف القرآن لكل السور
            int surahIndex = i + 1;
            if (surahNumber != null) {
                surahNumber.setText(String.valueOf(surahIndex));
            }

            // إظهار صورة غلاف القرآن لكل السور
            if (quranCoverImage != null) {
                quranCoverImage.setVisibility(android.view.View.VISIBLE);
            }

            // تحديث اسم السورة (إزالة كلمة "سورة")
            String surahName = temp.RealName;
            if (surahName.startsWith("سورة ")) {
                surahName = surahName.substring(5); // إزالة "سورة "
            }

            // تحديث النصوص
            title.setText(surahName);
            /*
            //check if SD availbel
            Boolean isSDPresent = android.os.Environment.getExternalStorageState().equals(android.os.Environment.MEDIA_MOUNTED);

            if(isSDPresent)
            {
                // yes SD-card is present
               // budownload.setEnabled(true);
                budownload.setVisibility(View.VISIBLE);
            }
            else
            {  // No SD-card is present;
                budownload.setVisibility(View.GONE);

            }*/
            // فحص إذا كانت السورة محملة محلياً
            String surahNameForFile = temp.RealName;
            if (surahNameForFile.startsWith("سورة ")) {
                surahNameForFile = surahNameForFile.substring(5); // إزالة "سورة "
            }
            String beautifulFileName = createBeautifulFileName(surahNameForFile, RecitesName);
            boolean isDownloaded = isFileDownloadedByName(beautifulFileName);

            // إعداد الأزرار والنصوص حسب حالة التنزيل
            if (isDownloaded) {
                // السورة محملة - إخفاء زر التنزيل
                budownload.setVisibility(android.view.View.GONE);
                cost.setText(getString(R.string.ready_to_play));
            } else {
                // السورة غير محملة - إظهار زر التنزيل
                budownload.setVisibility(android.view.View.VISIBLE);
                cost.setText(getString(R.string.tap_to_download));
            }

            // زر التشغيل يظهر دائماً
            image.setVisibility(android.view.View.VISIBLE);

            // download file
            budownload.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!ISDonwloading) {
                        // العثور على السورة الصحيحة وتحديد RecitesAYA
                        for (int i = 0; i < listrecitesAya.size(); i++) {
                            if (listrecitesAya.get(i).RealName.equals(temp.RealName)) {
                                RecitesAYA = String.valueOf(i);
                                // تحميل السورة الصحيحة
                                startDownload(temp.ImgUrl, temp.ServerName);
                                break;
                            }
                        }
                    }
                }
            });
            //=====================================================
            image.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!ISDonwloading) {
                        for (int i = 0; i < listrecitesAya.size(); i++) {
                            if (listrecitesAya.get(i).RealName.equals(temp.RealName)) {
                                RecitesAYA = String.valueOf(i);

                                // فحص إذا كانت السورة محملة محلياً باستخدام رقم السورة
                                String surahCode = String.format("%03d", i + 1); // تحويل الفهرس إلى رقم السورة
                                android.util.Log.d("PLAY_BUTTON", "🎵 محاولة تشغيل السورة: " + surahCode + " - " + temp.RealName);

                                if (isFileDownloaded(surahCode)) {
                                    // تشغيل الملف المحلي
                                    android.util.Log.d("PLAY_BUTTON", "✅ ملف محلي موجود، تشغيل محلي");
                                    playLocalFile(surahCode);
                                } else {
                                    // تشغيل من الإنترنت
                                    android.util.Log.d("PLAY_BUTTON", "🌐 ملف غير موجود محلياً، تشغيل من الإنترنت");
                                    DisplayAya();
                                }
                                break;
                            }
                        }
                    }
                }
            });
            title.setOnClickListener(new android.view.View.OnClickListener() {
                @Override
                public void onClick(android.view.View v) {

                    //get aya
                    if (ISDonwloading != true)
                        for (int i = 0; i < listrecitesAya.size(); i++) {
                            if (listrecitesAya.get(i).RealName.equals(temp.RealName)) {
                                RecitesAYA = String.valueOf(i);// ServerName;

                                // فحص إذا كانت السورة محملة محلياً باستخدام رقم السورة
                                String surahCode = String.format("%03d", i + 1); // تحويل الفهرس إلى رقم السورة
                                android.util.Log.d("TITLE_CLICK", "🎵 محاولة تشغيل السورة: " + surahCode + " - " + temp.RealName);

                                if (isFileDownloaded(surahCode)) {
                                    // تشغيل الملف المحلي
                                    android.util.Log.d("TITLE_CLICK", "✅ ملف محلي موجود، تشغيل محلي");
                                    playLocalFile(surahCode);
                                } else {
                                    // تشغيل من الإنترنت
                                    android.util.Log.d("TITLE_CLICK", "🌐 ملف غير موجود محلياً، تشغيل من الإنترنت");
                                    DisplayAya();
                                }
                                break;
                            }

                        }
                }
            });


            // تم نقل تحديث النصوص للأعلى
            // cost.setText(temp.StateName); // تم نقل هذا للأعلى مع منطق التحقق
            //image.setImageResource(temp.ImgUrl);

            return row;


        }


    }
}

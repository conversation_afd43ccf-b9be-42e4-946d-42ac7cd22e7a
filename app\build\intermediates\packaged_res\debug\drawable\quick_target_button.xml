<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/islamicGreenLight" />
            <corners android:radius="20dp" />
            <stroke
                android:width="2dp"
                android:color="@color/islamicGreenDark" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="20dp" />
            <stroke
                android:width="1dp"
                android:color="@color/islamicGreenLight" />
        </shape>
    </item>
    
</selector>

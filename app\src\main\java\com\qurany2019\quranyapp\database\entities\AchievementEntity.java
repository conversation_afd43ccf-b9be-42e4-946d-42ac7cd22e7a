package com.qurany2019.quranyapp.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.Index;
import androidx.room.Ignore;

@Entity(
    tableName = "achievements_table",
    indices = {@Index("achievement_type"), @Index("is_unlocked")}
)
public class AchievementEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "achievement_id")
    private String achievementId;

    @ColumnInfo(name = "title")
    private String title;

    @ColumnInfo(name = "description")
    private String description;

    @ColumnInfo(name = "achievement_type")
    private String achievementType; // daily, weekly, monthly, milestone, streak

    @ColumnInfo(name = "target_value")
    private int targetValue;

    @ColumnInfo(name = "current_value")
    private int currentValue;

    @ColumnInfo(name = "is_unlocked")
    private boolean isUnlocked;

    @ColumnInfo(name = "unlock_date")
    private long unlockDate;

    @ColumnInfo(name = "icon_resource")
    private String iconResource;

    @ColumnInfo(name = "reward_points")
    private int rewardPoints;

    @ColumnInfo(name = "category")
    private String category; // morning, evening, sleep, wake, prayer, general

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Constructors
    public AchievementEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public AchievementEntity(String achievementId, String title, String description,
                           String achievementType, int targetValue, String category) {
        this.achievementId = achievementId;
        this.title = title;
        this.description = description;
        this.achievementType = achievementType;
        this.targetValue = targetValue;
        this.currentValue = 0;
        this.isUnlocked = false;
        this.unlockDate = 0;
        this.iconResource = "";
        this.rewardPoints = 0;
        this.category = category;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getAchievementId() {
        return achievementId;
    }

    public void setAchievementId(String achievementId) {
        this.achievementId = achievementId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAchievementType() {
        return achievementType;
    }

    public void setAchievementType(String achievementType) {
        this.achievementType = achievementType;
    }

    public int getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(int targetValue) {
        this.targetValue = targetValue;
    }

    public int getCurrentValue() {
        return currentValue;
    }

    public void setCurrentValue(int currentValue) {
        this.currentValue = currentValue;
        if (currentValue >= targetValue && !isUnlocked) {
            unlock();
        }
        this.updatedAt = System.currentTimeMillis();
    }

    public boolean isUnlocked() {
        return isUnlocked;
    }

    public void setUnlocked(boolean unlocked) {
        isUnlocked = unlocked;
        if (unlocked && unlockDate == 0) {
            this.unlockDate = System.currentTimeMillis();
        }
        this.updatedAt = System.currentTimeMillis();
    }

    public long getUnlockDate() {
        return unlockDate;
    }

    public void setUnlockDate(long unlockDate) {
        this.unlockDate = unlockDate;
    }

    public String getIconResource() {
        return iconResource;
    }

    public void setIconResource(String iconResource) {
        this.iconResource = iconResource;
    }

    public int getRewardPoints() {
        return rewardPoints;
    }

    public void setRewardPoints(int rewardPoints) {
        this.rewardPoints = rewardPoints;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    public void incrementProgress(int amount) {
        this.currentValue += amount;
        if (this.currentValue >= targetValue && !isUnlocked) {
            unlock();
        }
        this.updatedAt = System.currentTimeMillis();
    }

    public void unlock() {
        this.isUnlocked = true;
        this.unlockDate = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    public int getProgressPercentage() {
        if (targetValue == 0) return 0;
        return Math.min(100, (currentValue * 100) / targetValue);
    }

    public boolean isCompleted() {
        return currentValue >= targetValue;
    }

    public int getRemainingValue() {
        return Math.max(0, targetValue - currentValue);
    }

    public String getProgressText() {
        return currentValue + "/" + targetValue;
    }

    @Override
    public String toString() {
        return "AchievementEntity{" +
                "id=" + id +
                ", achievementId='" + achievementId + '\'' +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", achievementType='" + achievementType + '\'' +
                ", targetValue=" + targetValue +
                ", currentValue=" + currentValue +
                ", isUnlocked=" + isUnlocked +
                ", unlockDate=" + unlockDate +
                ", iconResource='" + iconResource + '\'' +
                ", rewardPoints=" + rewardPoints +
                ", category='" + category + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}

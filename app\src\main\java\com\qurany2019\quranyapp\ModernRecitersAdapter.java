package com.qurany2019.quranyapp;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;

import java.util.ArrayList;

public class ModernRecitersAdapter extends RecyclerView.Adapter<ModernRecitersAdapter.ReciterViewHolder> {

    private ArrayList<AuthorClass> recitersList;
    private ArrayList<AuthorClass> originalRecitersList;
    private Context context;
    private OnReciterClickListener listener;

    public interface OnReciterClickListener {
        void onReciterClick(AuthorClass reciter, int position);
        void onPlayClick(AuthorClass reciter, int position);
    }

    public ModernRecitersAdapter(Context context, ArrayList<AuthorClass> recitersList) {
        this.context = context;
        this.recitersList = recitersList;
        this.originalRecitersList = new ArrayList<>(recitersList);
    }

    public void setOnReciterClickListener(OnReciterClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public ReciterViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_reciter_modern, parent, false);
        return new ReciterViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ReciterViewHolder holder, int position) {
        AuthorClass reciter = recitersList.get(position);

        // تعيين البيانات
        holder.reciterName.setText(reciter.RealName);
        
        // عرض عدد السور (افتراضي 114 سورة)
        holder.surahCount.setText("114 سورة");

        // تأثير الانيميشن عند الظهور
        animateItemAppearance(holder.itemView, position);

        // أحداث النقر
        holder.playButton.setOnClickListener(v -> {
            animateButton(holder.playButton);
            if (listener != null) {
                listener.onPlayClick(reciter, position);
            }
        });

        // النقر على العنصر كاملاً
        holder.itemView.setOnClickListener(v -> {
            animateCard(holder.itemView);
            if (listener != null) {
                listener.onReciterClick(reciter, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return recitersList.size();
    }

    // فلترة القائمة حسب البحث
    public void filter(String searchText) {
        recitersList.clear();
        if (searchText.isEmpty()) {
            recitersList.addAll(originalRecitersList);
        } else {
            searchText = searchText.toLowerCase().trim();
            for (AuthorClass reciter : originalRecitersList) {
                if (reciter.RealName.toLowerCase().contains(searchText)) {
                    recitersList.add(reciter);
                }
            }
        }
        notifyDataSetChanged();
    }

    // انيميشن ظهور العناصر
    private void animateItemAppearance(View view, int position) {
        view.setAlpha(0f);
        view.setTranslationY(100f);
        
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f);
        ObjectAnimator slideUp = ObjectAnimator.ofFloat(view, "translationY", 100f, 0f);
        
        fadeIn.setDuration(300);
        slideUp.setDuration(300);
        
        // تأخير تدريجي للعناصر
        long delay = position * 50L;
        fadeIn.setStartDelay(delay);
        slideUp.setStartDelay(delay);
        
        fadeIn.setInterpolator(new AccelerateDecelerateInterpolator());
        slideUp.setInterpolator(new AccelerateDecelerateInterpolator());
        
        fadeIn.start();
        slideUp.start();
    }

    // انيميشن النقر على الزر
    private void animateButton(View button) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(button, "scaleX", 1f, 0.9f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(button, "scaleY", 1f, 0.9f, 1f);
        
        scaleX.setDuration(150);
        scaleY.setDuration(150);
        
        scaleX.start();
        scaleY.start();
    }

    // انيميشن النقر على البطاقة
    private void animateCard(View card) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(card, "scaleX", 1f, 0.98f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(card, "scaleY", 1f, 0.98f, 1f);
        
        scaleX.setDuration(100);
        scaleY.setDuration(100);
        
        scaleX.start();
        scaleY.start();
    }

    // ViewHolder
    public static class ReciterViewHolder extends RecyclerView.ViewHolder {
        TextView reciterName, surahCount;
        ImageView reciterIcon;
        MaterialButton playButton;
        MaterialCardView cardView;

        public ReciterViewHolder(@NonNull View itemView) {
            super(itemView);
            reciterName = itemView.findViewById(R.id.txtRecitesName);
            surahCount = itemView.findViewById(R.id.surahCountText);
            reciterIcon = itemView.findViewById(R.id.reciterIcon);
            playButton = itemView.findViewById(R.id.playButton);
            cardView = (MaterialCardView) itemView;
        }
    }
}

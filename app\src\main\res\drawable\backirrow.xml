<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24"
    android:autoMirrored="true">

    <!-- خلفية دائرية خفيفة -->
    <path
        android:fillColor="#10000000"
        android:pathData="M12,12m-10,0a10,10 0,1 1,20 0a10,10 0,1 1,-20 0"/>

    <!-- السهم الرئيسي - متوازن تماماً -->
    <path
        android:fillColor="?attr/colorOnSurface"
        android:strokeColor="?attr/colorOnSurface"
        android:strokeWidth="0.5"
        android:pathData="M15.41,7.41L14,6l-6,6 6,6 1.41,-1.41L10.83,12z"/>

    <!-- تأثير الإضاءة الداخلية -->
    <path
        android:fillColor="#30FFFFFF"
        android:pathData="M14.5,7.5L13.5,6.5l-5,5 5,5 1,-1L10,11.5z"/>

</vector>

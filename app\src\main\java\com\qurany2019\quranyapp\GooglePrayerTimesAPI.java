package com.qurany2019.quranyapp;

import android.content.Context;
import android.util.Log;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import org.json.JSONException;
import org.json.JSONObject;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

/**
 * خدمة Aladhan Prayer Times API الرسمية
 * تستخدم API Aladhan الموثوق عالمياً حسب التوثيق الرسمي
 * https://aladhan.com/calculation-methods
 */
public class GooglePrayerTimesAPI {

    private static final String TAG = "AladhanPrayerTimesAPI";

    // استخدام Aladhan API الرسمي (مجاني ودقيق جداً)
    private static final String API_BASE_URL = "https://api.aladhan.com/v1/timings";

    // API بديل للإحداثيات
    private static final String BACKUP_API_URL = "https://api.aladhan.com/v1/timingsByCoordinates";

    private final Context context;
    private final RequestQueue requestQueue;

    public interface PrayerTimesCallback {
        void onSuccess(PrayerTimesData data);
        void onError(String error);
    }

    public static class PrayerTimesData {
        public String fajr;
        public String sunrise;
        public String dhuhr;
        public String asr;
        public String maghrib;
        public String isha;
        public String cityName;
        public String countryName;
        public String hijriDate;
        public String nextPrayer;
        public String currentTime;

        public PrayerTimesData(String fajr, String sunrise, String dhuhr, String asr,
                              String maghrib, String isha, String cityName, String countryName) {
            this.fajr = formatTime(fajr);
            this.sunrise = formatTime(sunrise);
            this.dhuhr = formatTime(dhuhr);
            this.asr = formatTime(asr);
            this.maghrib = formatTime(maghrib);
            this.isha = formatTime(isha);
            this.cityName = cityName != null ? cityName : "مدينة غير معروفة";
            this.countryName = countryName != null ? countryName : "دولة غير معروفة";
            this.nextPrayer = calculateNextPrayer();
            this.currentTime = getCurrentTime();
        }

        private String formatTime(String time) {
            if (time == null || time.isEmpty()) return "غير متاح";

            try {
                // إزالة المنطقة الزمنية إذا كانت موجودة
                String cleanTime = time.split(" ")[0];

                // تحويل من 24 ساعة إلى 12 ساعة مع AM/PM
                SimpleDateFormat input = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
                SimpleDateFormat output = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

                String formattedTime = output.format(input.parse(cleanTime));
                // إرجاع الوقت بـ AM/PM الإنجليزية - سيتم تطبيق الترجمة في PrayerTimesActivity
                return formattedTime;
            } catch (Exception e) {
                Log.e(TAG, "Error formatting time: " + time, e);
                return time;
            }
        }

        private String getCurrentTime() {
            SimpleDateFormat sdf = new SimpleDateFormat("h:mm a", Locale.ENGLISH);
            return sdf.format(Calendar.getInstance().getTime());
            // سيتم تطبيق الترجمة في PrayerTimesActivity
        }

        private String calculateNextPrayer() {
            Calendar now = Calendar.getInstance();
            String currentTime = String.format(Locale.ENGLISH, "%02d:%02d",
                now.get(Calendar.HOUR_OF_DAY), now.get(Calendar.MINUTE));

            // سيتم استخدام نظام الترجمة في الكود المستدعي
            String[] prayerNames = {"Fajr", "Sunrise", "Dhuhr", "Asr", "Maghrib", "Isha"};
            String[] prayerTimes = {
                extractTime(fajr), extractTime(sunrise), extractTime(dhuhr),
                extractTime(asr), extractTime(maghrib), extractTime(isha)
            };

            for (int i = 0; i < prayerTimes.length; i++) {
                if (currentTime.compareTo(prayerTimes[i]) < 0) {
                    return "الصلاة القادمة: " + prayerNames[i] + " - " +
                           (i == 0 ? fajr : i == 1 ? sunrise : i == 2 ? dhuhr :
                            i == 3 ? asr : i == 4 ? maghrib : isha);
                }
            }

            // إذا انتهت جميع الصلوات، فالقادمة هي فجر اليوم التالي
            return "Next Prayer: Fajr (tomorrow) - " + fajr;
        }

        private String extractTime(String formattedTime) {
            try {
                // استخراج الوقت من النص المنسق
                String time = formattedTime.replace("ص", "AM").replace("م", "PM");
                SimpleDateFormat input = new SimpleDateFormat("h:mm a", Locale.ENGLISH);
                SimpleDateFormat output = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
                return output.format(input.parse(time));
            } catch (Exception e) {
                return "00:00";
            }
        }
    }

    public GooglePrayerTimesAPI(Context context) {
        this.context = context;
        this.requestQueue = Volley.newRequestQueue(context);
    }

    /**
     * الحصول على أوقات الصلاة من Google API
     */
    public void getPrayerTimes(double latitude, double longitude, String cityName, PrayerTimesCallback callback) {
        // الحصول على التاريخ الحالي
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy", Locale.getDefault());
        String date = dateFormat.format(calendar.getTime());

        // بناء URL حسب التوثيق الرسمي لـ Aladhan API
        // method=4 = Umm Al-Qura University, Makkah (الأكثر دقة للمنطقة العربية)
        // school=0 = Shafi (المذهب الشافعي)
        String url = String.format(Locale.US,
            "%s/%s?latitude=%.6f&longitude=%.6f&method=4&school=0",
            API_BASE_URL, date, latitude, longitude);

        Log.d(TAG, "Requesting prayer times from: " + url);

        // إنشاء طلب JSON
        JsonObjectRequest request = new JsonObjectRequest(
            Request.Method.GET,
            url,
            null,
            new Response.Listener<JSONObject>() {
                @Override
                public void onResponse(JSONObject response) {
                    Log.d(TAG, "API Response received: " + response.toString());
                    parseAndCallback(response, cityName, callback);
                }
            },
            new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    Log.e(TAG, "API Error: " + error.getMessage());
                    callback.onError("خطأ في الاتصال بخدمة أوقات الصلاة. تحقق من الاتصال بالإنترنت.");
                }
            }
        );

        // إضافة الطلب إلى القائمة
        requestQueue.add(request);
    }



    /**
     * تحليل استجابة API الأساسي
     */
    private void parseAndCallback(JSONObject response, String cityName, PrayerTimesCallback callback) {
        try {
            JSONObject data = response.getJSONObject("data");
            JSONObject timings = data.getJSONObject("timings");

            String fajr = timings.getString("Fajr");
            String sunrise = timings.getString("Sunrise");
            String dhuhr = timings.getString("Dhuhr");
            String asr = timings.getString("Asr");
            String maghrib = timings.getString("Maghrib");
            String isha = timings.getString("Isha");

            // محاولة الحصول على معلومات إضافية
            String hijriDate = "";
            try {
                JSONObject dateInfo = data.getJSONObject("date");
                JSONObject hijri = dateInfo.getJSONObject("hijri");
                hijriDate = hijri.getString("date");
            } catch (Exception e) {
                Log.w(TAG, "Could not parse Hijri date");
            }

            PrayerTimesData prayerData = new PrayerTimesData(fajr, sunrise, dhuhr, asr, maghrib, isha, cityName, "");
            prayerData.hijriDate = hijriDate;

            Log.d(TAG, "Prayer times parsed successfully for " + cityName);
            callback.onSuccess(prayerData);

        } catch (JSONException e) {
            Log.e(TAG, "JSON parsing error", e);
            callback.onError("خطأ في تحليل بيانات أوقات الصلاة");
        }
    }


}

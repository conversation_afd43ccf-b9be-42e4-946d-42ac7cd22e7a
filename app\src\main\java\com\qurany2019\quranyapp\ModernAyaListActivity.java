package com.qurany2019.quranyapp;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;

import java.io.File;
import java.util.ArrayList;

public class ModernAyaListActivity extends AppCompatActivity implements ModernSurahAdapter.OnSurahClickListener {

    // UI Components
    private RecyclerView surahRecyclerView;
    private ModernSurahAdapter adapter;
    private EditText searchEditText;
    private TextView reciterNameText, surahCountText, downloadingText, progressPercentage;
    private MaterialCardView downloadProgressCard;
    private LinearProgressIndicator modernProgressBar;
    private CollapsingToolbarLayout collapsingToolbar;
    private Toolbar toolbar;
    private AdView adView;

    // Data
    private ArrayList<AuthorClass> surahList;
    private ArrayList<AuthorClass> originalSurahList;
    private String reciterName;
    private String currentDownloadingSurah = "";
    private boolean isDownloading = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_aya_list_modern);

        // الحصول على اسم القارئ
        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            reciterName = bundle.getString("RecitesName", "");
        }

        initViews();
        setupToolbar();
        setupRecyclerView();
        setupSearch();
        loadSurahData();
        setupAds();
    }

    private void initViews() {
        // Toolbar & AppBar
        toolbar = findViewById(R.id.toolbar);
        collapsingToolbar = findViewById(R.id.collapsingToolbar);
        reciterNameText = findViewById(R.id.reciterNameText);
        surahCountText = findViewById(R.id.surahCountText);

        // RecyclerView & Search
        surahRecyclerView = findViewById(R.id.surahRecyclerView);
        searchEditText = findViewById(R.id.searchEditText);

        // Download Progress
        downloadProgressCard = findViewById(R.id.downloadProgressCard);
        downloadingText = findViewById(R.id.downloadingText);
        progressPercentage = findViewById(R.id.progressPercentage);
        modernProgressBar = findViewById(R.id.modernProgressBar);

        // Ads
        adView = findViewById(R.id.adView);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }

        // تعيين اسم القارئ
        reciterNameText.setText(reciterName);
        collapsingToolbar.setTitle(reciterName);

        // النقر على زر الرجوع
        toolbar.setNavigationOnClickListener(v -> {
            animateBackButton();
            onBackPressed();
        });
    }

    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        surahRecyclerView.setLayoutManager(layoutManager);

        // إضافة انيميشن للعناصر
        surahRecyclerView.setItemAnimator(new androidx.recyclerview.widget.DefaultItemAnimator());
    }

    private void setupSearch() {
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (adapter != null) {
                    adapter.filter(s.toString());
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }

    private void loadSurahData() {
        // تحميل قائمة السور
        LnaguageClass lc = new LnaguageClass();
        surahList = lc.GuranAya(reciterName);
        originalSurahList = new ArrayList<>(surahList);

        // تحديث عدد السور
        surahCountText.setText(surahList.size() + " سورة");

        // إعداد الـ Adapter
        adapter = new ModernSurahAdapter(this, surahList, reciterName);
        adapter.setOnSurahClickListener(this);
        surahRecyclerView.setAdapter(adapter);

        // انيميشن ظهور القائمة
        animateRecyclerView();
    }

    private void setupAds() {
        AdRequest adRequest = new AdRequest.Builder().build();
        adView.loadAd(adRequest);
    }

    @Override
    public void onPlayClick(AuthorClass surah, int position) {
        if (isDownloading) {
            Toast.makeText(this, "انتظر انتهاء التنزيل الحالي", Toast.LENGTH_SHORT).show();
            return;
        }

        // فحص إذا كانت السورة محملة محلياً
        if (isFileDownloaded(surah.ServerName)) {
            // تشغيل الملف المحلي
            playLocalFile(surah, position);
        } else {
            // تشغيل من الإنترنت
            playOnlineFile(surah, position);
        }
    }

    @Override
    public void onDownloadClick(AuthorClass surah, int position) {
        if (isDownloading) {
            Toast.makeText(this, "يتم تنزيل سورة أخرى حالياً", Toast.LENGTH_SHORT).show();
            return;
        }

        startDownload(surah, position);
    }

    private void playLocalFile(AuthorClass surah, int position) {
        String localPath = getLocalFilePath(surah.ServerName);

        Intent intent = new Intent(this, managerdb.class);
        intent.putExtra("RecitesName", reciterName);
        intent.putExtra("RecitesAYA", String.valueOf(position));
        intent.putExtra("LocalFilePath", localPath);
        intent.putExtra("IsLocalFile", true);
        startActivity(intent);

        Toast.makeText(this, "🎵 تشغيل من التخزين المحلي", Toast.LENGTH_SHORT).show();
    }

    private void playOnlineFile(AuthorClass surah, int position) {
        Intent intent = new Intent(this, managerdb.class);
        intent.putExtra("RecitesName", reciterName);
        intent.putExtra("RecitesAYA", String.valueOf(position));
        intent.putExtra("IsLocalFile", false);
        startActivity(intent);
    }

    private void startDownload(AuthorClass surah, int position) {
        currentDownloadingSurah = surah.RealName;
        isDownloading = true;

        // إظهار شريط التقدم العائم
        showDownloadProgress();

        // بدء التنزيل
        downloadFile(surah.ImgUrl, surah.ServerName, position);
    }

    private void downloadFile(String url, String fileName, int position) {
        new Thread(() -> {
            try {
                // تحديث الواجهة - بداية التنزيل
                runOnUiThread(() -> {
                    downloadingText.setText("جاري تنزيل " + currentDownloadingSurah + "...");
                    modernProgressBar.setProgress(0);
                });

                // إنشاء مجلد التخزين
                File audioDir = new File(getExternalFilesDir(null), "قرآني/" + reciterName);
                if (!audioDir.exists()) {
                    audioDir.mkdirs();
                }

                File outputFile = new File(audioDir, fileName + ".mp3");

                // حذف الملف إذا كان موجوداً
                if (outputFile.exists()) {
                    outputFile.delete();
                }

                // استخدام OkHttp للتنزيل
                okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
                okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(url)
                    .build();

                okhttp3.Response response = client.newCall(request).execute();

                if (!response.isSuccessful()) {
                    throw new java.io.IOException("فشل في الاتصال: " + response);
                }

                okhttp3.ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    throw new java.io.IOException("لا توجد بيانات للتنزيل");
                }

                long contentLength = responseBody.contentLength();
                java.io.InputStream inputStream = responseBody.byteStream();
                java.io.FileOutputStream outputStream = new java.io.FileOutputStream(outputFile);

                byte[] buffer = new byte[4096];
                long downloadedBytes = 0;
                int bytesRead;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    downloadedBytes += bytesRead;

                    // تحديث شريط التقدم
                    if (contentLength > 0) {
                        final int progress = (int) ((downloadedBytes * 100) / contentLength);
                        runOnUiThread(() -> {
                            modernProgressBar.setProgress(progress);
                            progressPercentage.setText(progress + "%");
                        });
                    }
                }

                outputStream.close();
                inputStream.close();
                response.close();

                // التنزيل انتهى بنجاح
                runOnUiThread(() -> {
                    hideDownloadProgress();
                    isDownloading = false;
                    adapter.notifyItemChanged(position);
                    Toast.makeText(ModernAyaListActivity.this,
                        "✅ تم تنزيل " + currentDownloadingSurah + " بنجاح!",
                        Toast.LENGTH_LONG).show();
                });

            } catch (Exception e) {
                // معالجة الأخطاء
                runOnUiThread(() -> {
                    hideDownloadProgress();
                    isDownloading = false;
                    Toast.makeText(ModernAyaListActivity.this,
                        "❌ فشل في تنزيل " + currentDownloadingSurah + ": " + e.getMessage(),
                        Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }

    // فحص وجود الملف المحلي باستخدام نظام Cache الذكي
    private boolean isFileDownloaded(String fileName) {
        try {
            File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
            File quranAppDir = new File(musicDir, "قرآني");
            File audioDir = new File(quranAppDir, reciterName);

            // تحديث cache الملفات
            updateLocalFilesCache(audioDir, reciterName);

            // استخدام نفس نظام البحث الذكي
            return isFileInCache(reciterName, fileName);

        } catch (Exception e) {
            android.util.Log.e("MODERN_FILE_CHECK", "خطأ في فحص الملف: " + e.getMessage());
            return false;
        }
    }

    // Cache للملفات المحلية (نفس النظام من AyaList)
    private static java.util.Map<String, java.util.Set<String>> localFilesCache = new java.util.HashMap<>();
    private static long lastCacheUpdate = 0;
    private static final long CACHE_VALIDITY = 30000; // 30 ثانية

    /**
     * تحديث cache الملفات المحلية
     */
    private void updateLocalFilesCache(File audioDir, String reciterName) {
        try {
            long currentTime = System.currentTimeMillis();

            if (currentTime - lastCacheUpdate > CACHE_VALIDITY || !localFilesCache.containsKey(reciterName)) {
                java.util.Set<String> files = new java.util.HashSet<>();

                if (audioDir.exists()) {
                    String[] fileList = audioDir.list();
                    if (fileList != null) {
                        android.util.Log.d("MODERN_CACHE", "📁 فحص مجلد: " + audioDir.getAbsolutePath());
                        for (String fileName : fileList) {
                            android.util.Log.d("MODERN_CACHE", "📄 ملف موجود: " + fileName);
                            if (fileName.endsWith(".mp3")) {
                                String baseName = fileName.substring(0, fileName.lastIndexOf(".mp3"));
                                files.add(baseName);
                                android.util.Log.d("MODERN_CACHE", "✅ تم إضافة للـ cache: " + baseName);
                            }
                        }
                    }
                }

                localFilesCache.put(reciterName, files);
                lastCacheUpdate = currentTime;
                android.util.Log.d("MODERN_CACHE", "تم تحديث cache للقارئ: " + reciterName + " - عدد الملفات: " + files.size());
            }
        } catch (Exception e) {
            android.util.Log.e("MODERN_CACHE", "خطأ في تحديث cache: " + e.getMessage());
        }
    }

    /**
     * فحص وجود الملف في cache
     */
    private boolean isFileInCache(String reciterName, String surahCode) {
        try {
            java.util.Set<String> files = localFilesCache.get(reciterName);
            if (files != null) {
                android.util.Log.d("MODERN_CACHE", "🔍 البحث عن: " + surahCode + " في cache");
                android.util.Log.d("MODERN_CACHE", "📋 الملفات المتاحة: " + files.toString());

                // البحث المباشر بالرقم
                boolean found = files.contains(surahCode);
                if (found) {
                    android.util.Log.d("MODERN_CACHE", "✅ تم العثور على الملف بالرقم");
                    return true;
                }

                // البحث باسم السورة
                String surahName = getSurahNameByCode(surahCode);
                if (surahName != null) {
                    android.util.Log.d("MODERN_CACHE", "🔍 البحث باسم السورة: " + surahName);
                    for (String fileName : files) {
                        if (fileName.contains(surahName)) {
                            android.util.Log.d("MODERN_CACHE", "✅ تم العثور على الملف باسم السورة: " + fileName);
                            return true;
                        }
                    }
                }

                android.util.Log.d("MODERN_CACHE", "❌ لم يتم العثور على الملف");
                return false;
            }
        } catch (Exception e) {
            android.util.Log.e("MODERN_CACHE", "خطأ في فحص cache: " + e.getMessage());
        }
        return false;
    }

    /**
     * الحصول على اسم السورة من الرقم
     */
    private String getSurahNameByCode(String surahCode) {
        try {
            int code = Integer.parseInt(surahCode);
            String[] surahNames = {
                "الفاتحة", "البقرة", "ال عمران", "النساء", "المائدة", "الانعام", "الأعراف", "الأنفال", "التوبة", "يونس",
                "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
                "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
                "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
                "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
                "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
                "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
                "نوح", "الجن", "المزمل", "المدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
                "التكوير", "الانفطار", "المطففين", "الانشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
                "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
                "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
                "المسد", "الاخلاص", "الفلق", "الناس"
            };

            if (code >= 1 && code <= surahNames.length) {
                return surahNames[code - 1].trim();
            }
        } catch (Exception e) {
            android.util.Log.e("MODERN_SURAH_NAME", "خطأ في تحويل رقم السورة: " + e.getMessage());
        }
        return null;
    }

    // الحصول على مسار الملف المحلي
    private String getLocalFilePath(String fileName) {
        File audioDir = new File(getExternalFilesDir(null), "قرآني/" + reciterName);
        File audioFile = new File(audioDir, fileName + ".mp3");
        return audioFile.getAbsolutePath();
    }

    // إظهار شريط التقدم العائم
    private void showDownloadProgress() {
        downloadProgressCard.setVisibility(View.VISIBLE);
        ObjectAnimator slideDown = ObjectAnimator.ofFloat(downloadProgressCard, "translationY", -200f, 0f);
        slideDown.setDuration(300);
        slideDown.setInterpolator(new AccelerateDecelerateInterpolator());
        slideDown.start();
    }

    // إخفاء شريط التقدم العائم
    private void hideDownloadProgress() {
        ObjectAnimator slideUp = ObjectAnimator.ofFloat(downloadProgressCard, "translationY", 0f, -200f);
        slideUp.setDuration(300);
        slideUp.setInterpolator(new AccelerateDecelerateInterpolator());
        slideUp.start();

        slideUp.addListener(new android.animation.AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(android.animation.Animator animation) {
                downloadProgressCard.setVisibility(View.GONE);
            }
        });
    }

    // انيميشن زر الرجوع
    private void animateBackButton() {
        ObjectAnimator rotation = ObjectAnimator.ofFloat(toolbar.getNavigationIcon(), "rotation", 0f, 360f);
        rotation.setDuration(300);
        rotation.start();
    }

    // انيميشن ظهور RecyclerView
    private void animateRecyclerView() {
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(surahRecyclerView, "alpha", 0f, 1f);
        ObjectAnimator slideUp = ObjectAnimator.ofFloat(surahRecyclerView, "translationY", 100f, 0f);

        fadeIn.setDuration(500);
        slideUp.setDuration(500);

        fadeIn.setInterpolator(new AccelerateDecelerateInterpolator());
        slideUp.setInterpolator(new AccelerateDecelerateInterpolator());

        fadeIn.start();
        slideUp.start();
    }

    @Override
    protected void onDestroy() {
        if (adView != null) {
            adView.destroy();
        }
        super.onDestroy();
    }
}

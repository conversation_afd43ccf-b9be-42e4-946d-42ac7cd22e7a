package com.qurany2019.quranyapp;

import android.media.MediaPlayer;
import android.util.Log;

/**
 * مدير MediaPlayer المشترك - Singleton Pattern
 * يضمن أن جميع أجزاء التطبيق تتحكم في نفس MediaPlayer
 */
public class MediaPlayerManager {

    private static final String TAG = "MediaPlayerManager";
    private static MediaPlayerManager instance;
    private static MediaPlayer mediaPlayer;
    private static boolean isPlaying = false;

    // معلومات التشغيل الحالية
    private static String currentTitle = "";
    private static String currentReciter = "";
    private static int currentIndex = 0;

    // Callback للتحديثات
    public interface MediaPlayerCallback {
        void onPlayStateChanged(boolean isPlaying);
        void onSongChanged(int index);
    }

    private static MediaPlayerCallback callback;

    private MediaPlayerManager() {
        // Private constructor for Singleton
    }

    /**
     * الحصول على Instance الوحيد
     */
    public static synchronized MediaPlayerManager getInstance() {
        if (instance == null) {
            instance = new MediaPlayerManager();
        }
        return instance;
    }

    /**
     * تعيين MediaPlayer من التطبيق الرئيسي
     */
    public static void setMediaPlayer(MediaPlayer mp) {
        mediaPlayer = mp;
        Log.d(TAG, "تم تعيين MediaPlayer من التطبيق الرئيسي");
    }

    /**
     * الحصول على MediaPlayer
     */
    public static MediaPlayer getMediaPlayer() {
        return mediaPlayer;
    }

    /**
     * تشغيل الصوت
     */
    public static void play() {
        try {
            if (mediaPlayer != null && !isPlaying) {
                mediaPlayer.start();
                isPlaying = true;
                Log.d(TAG, "تم تشغيل الصوت");

                if (callback != null) {
                    callback.onPlayStateChanged(true);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في تشغيل الصوت: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إيقاف الصوت
     */
    public static void pause() {
        try {
            if (mediaPlayer != null && isPlaying) {
                mediaPlayer.pause();
                isPlaying = false;
                Log.d(TAG, "تم إيقاف الصوت");

                if (callback != null) {
                    callback.onPlayStateChanged(false);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في إيقاف الصوت: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إيقاف كامل
     */
    public static void stop() {
        try {
            if (mediaPlayer != null) {
                if (isPlaying) {
                    mediaPlayer.stop();
                }
                isPlaying = false;
                Log.d(TAG, "تم إيقاف الصوت كامل");

                if (callback != null) {
                    callback.onPlayStateChanged(false);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في الإيقاف الكامل: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * فحص حالة التشغيل - يستخدم نفس الطريقة الآمنة
     */
    public static boolean isPlaying() {
        try {
            return mediaPlayer != null && mediaPlayer.isPlaying();
        } catch (IllegalStateException e) {
            // MediaPlayer في حالة غير صالحة
            return false;
        } catch (Exception e) {
            Log.e(TAG, "خطأ في فحص حالة التشغيل: " + e.getMessage());
            return false;
        }
    }

    /**
     * تحديث معلومات التشغيل
     */
    public static void updateInfo(String title, String reciter, int index) {
        currentTitle = title != null ? title : "";
        currentReciter = reciter != null ? reciter : "";
        currentIndex = index;
        Log.d(TAG, "تم تحديث المعلومات: " + title + " - " + reciter);
    }

    /**
     * الحصول على العنوان الحالي
     */
    public static String getCurrentTitle() {
        return currentTitle;
    }

    /**
     * الحصول على القارئ الحالي
     */
    public static String getCurrentReciter() {
        return currentReciter;
    }

    /**
     * الحصول على الفهرس الحالي
     */
    public static int getCurrentIndex() {
        return currentIndex;
    }

    /**
     * تسجيل Callback للتحديثات
     */
    public static void setCallback(MediaPlayerCallback callback) {
        MediaPlayerManager.callback = callback;
    }

    /**
     * تنظيف الموارد
     */
    public static void release() {
        try {
            if (mediaPlayer != null) {
                if (isPlaying) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
                mediaPlayer = null;
                isPlaying = false;
                Log.d(TAG, "تم تحرير MediaPlayer");
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في تحرير MediaPlayer: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * إعادة تعيين MediaPlayer
     */
    public static void reset() {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.reset();
                isPlaying = false;
                Log.d(TAG, "تم إعادة تعيين MediaPlayer");
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في إعادة تعيين MediaPlayer: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * الحصول على الموضع الحالي
     */
    public static int getCurrentPosition() {
        try {
            if (mediaPlayer != null) {
                return mediaPlayer.getCurrentPosition();
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في الحصول على الموضع: " + e.getMessage());
        }
        return 0;
    }

    /**
     * الحصول على المدة الكاملة
     */
    public static int getDuration() {
        try {
            if (mediaPlayer != null) {
                return mediaPlayer.getDuration();
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في الحصول على المدة: " + e.getMessage());
        }
        return 0;
    }

    /**
     * الانتقال إلى موضع معين
     */
    public static void seekTo(int position) {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.seekTo(position);
                Log.d(TAG, "تم الانتقال إلى الموضع: " + position);
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في الانتقال: " + e.getMessage());
        }
    }
}

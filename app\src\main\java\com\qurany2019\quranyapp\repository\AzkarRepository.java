package com.qurany2019.quranyapp.repository;

import android.app.Application;
import android.os.AsyncTask;
import androidx.lifecycle.LiveData;

import com.qurany2019.quranyapp.database.AzkarDatabase;
import com.qurany2019.quranyapp.database.entities.AzkarEntity;
import com.qurany2019.quranyapp.database.entities.AzkarProgressEntity;
import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;
import com.qurany2019.quranyapp.database.dao.AzkarDao;
import com.qurany2019.quranyapp.database.dao.AzkarProgressDao;
import com.qurany2019.quranyapp.database.dao.AzkarStatisticsDao;
import com.qurany2019.quranyapp.database.dao.AchievementDao;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AzkarRepository {
    private AzkarDao azkarDao;
    private AzkarProgressDao azkarProgressDao;
    private AzkarStatisticsDao azkarStatisticsDao;
    private AchievementDao achievementDao;
    
    private LiveData<List<AzkarEntity>> allAzkar;
    private ExecutorService executor;
    
    // Date formatter
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    public AzkarRepository(Application application) {
        AzkarDatabase database = AzkarDatabase.getInstance(application);
        azkarDao = database.azkarDao();
        azkarProgressDao = database.azkarProgressDao();
        azkarStatisticsDao = database.azkarStatisticsDao();
        achievementDao = database.achievementDao();
        
        allAzkar = azkarDao.getAllAzkarLiveData();
        executor = Executors.newFixedThreadPool(4);
    }

    // ==================== AZKAR OPERATIONS ====================
    
    public LiveData<List<AzkarEntity>> getAllAzkar() {
        return allAzkar;
    }
    
    public LiveData<List<AzkarEntity>> getAzkarByCategory(String category) {
        return azkarDao.getAzkarByCategoryLiveData(category);
    }
    
    public void insertAzkar(AzkarEntity azkar) {
        executor.execute(() -> azkarDao.insertAzkar(azkar));
    }
    
    public void insertAllAzkar(List<AzkarEntity> azkarList) {
        executor.execute(() -> azkarDao.insertAllAzkar(azkarList));
    }
    
    public void updateAzkar(AzkarEntity azkar) {
        executor.execute(() -> azkarDao.updateAzkar(azkar));
    }
    
    public void deleteAzkar(AzkarEntity azkar) {
        executor.execute(() -> azkarDao.deleteAzkar(azkar));
    }

    // ==================== PROGRESS OPERATIONS ====================
    
    public LiveData<List<AzkarProgressEntity>> getTodayProgress() {
        String today = dateFormat.format(new Date());
        return azkarProgressDao.getProgressByDateLiveData(today);
    }
    
    public LiveData<AzkarProgressEntity> getAzkarProgress(int azkarId) {
        String today = dateFormat.format(new Date());
        return azkarProgressDao.getProgressByAzkarAndDateLiveData(azkarId, today);
    }
    
    public void insertProgress(AzkarProgressEntity progress) {
        executor.execute(() -> azkarProgressDao.insertProgress(progress));
    }
    
    public void updateProgress(AzkarProgressEntity progress) {
        executor.execute(() -> azkarProgressDao.updateProgress(progress));
    }
    
    public void incrementAzkarProgress(int azkarId) {
        executor.execute(() -> {
            String today = dateFormat.format(new Date());
            long timestamp = System.currentTimeMillis();
            
            // Check if progress exists for today
            AzkarProgressEntity existingProgress = azkarProgressDao.getProgressByAzkarAndDate(azkarId, today);
            
            if (existingProgress == null) {
                // Create new progress entry
                AzkarEntity azkar = azkarDao.getAzkarById(azkarId);
                if (azkar != null) {
                    AzkarProgressEntity newProgress = new AzkarProgressEntity(azkarId, azkar.getTargetCount(), today);
                    newProgress.incrementCount();
                    azkarProgressDao.insertProgress(newProgress);
                }
            } else {
                // Update existing progress
                existingProgress.incrementCount();
                azkarProgressDao.updateProgress(existingProgress);
            }
            
            // Update statistics
            updateDailyStatistics(today);
        });
    }
    
    public void resetAzkarProgress(int azkarId) {
        executor.execute(() -> {
            String today = dateFormat.format(new Date());
            long timestamp = System.currentTimeMillis();
            azkarProgressDao.resetProgress(azkarId, today, timestamp);
            
            // Update statistics
            updateDailyStatistics(today);
        });
    }

    // ==================== STATISTICS OPERATIONS ====================
    
    public LiveData<List<AzkarStatisticsEntity>> getTodayStatistics() {
        String today = dateFormat.format(new Date());
        return azkarStatisticsDao.getStatisticsByDateLiveData(today);
    }
    
    public LiveData<AzkarStatisticsEntity> getCategoryStatistics(String category) {
        String today = dateFormat.format(new Date());
        return azkarStatisticsDao.getStatisticsByDateAndCategoryLiveData(today, category);
    }
    
    private void updateDailyStatistics(String date) {
        // This method updates daily statistics based on progress
        executor.execute(() -> {
            List<AzkarProgressEntity> dayProgress = azkarProgressDao.getProgressByDate(date);
            
            // Calculate overall statistics
            int totalAzkar = dayProgress.size();
            int completedAzkar = 0;
            int totalTasbih = 0;
            long totalDuration = 0;
            
            for (AzkarProgressEntity progress : dayProgress) {
                if (progress.isCompleted()) {
                    completedAzkar++;
                }
                totalTasbih += progress.getCurrentCount();
                totalDuration += progress.getSessionDuration();
            }
            
            float completionPercentage = totalAzkar > 0 ? ((float) completedAzkar / totalAzkar) * 100 : 0;
            
            // Update or create statistics entry
            AzkarStatisticsEntity stats = azkarStatisticsDao.getStatisticsByDateAndCategory(date, "all");
            if (stats == null) {
                stats = new AzkarStatisticsEntity(date, "all");
            }
            
            stats.setTotalAzkarCount(totalAzkar);
            stats.setCompletedAzkarCount(completedAzkar);
            stats.setTotalTasbihCount(totalTasbih);
            stats.setCompletionPercentage(completionPercentage);
            stats.setSessionDuration(totalDuration);
            
            azkarStatisticsDao.insertStatistics(stats);
        });
    }

    // ==================== ACHIEVEMENT OPERATIONS ====================
    
    public LiveData<List<AchievementEntity>> getAllAchievements() {
        return achievementDao.getAllAchievementsLiveData();
    }
    
    public LiveData<List<AchievementEntity>> getUnlockedAchievements() {
        return achievementDao.getUnlockedAchievementsLiveData();
    }
    
    public void insertAchievement(AchievementEntity achievement) {
        executor.execute(() -> achievementDao.insertAchievement(achievement));
    }
    
    public void updateAchievementProgress(String achievementId, int increment) {
        executor.execute(() -> {
            long timestamp = System.currentTimeMillis();
            achievementDao.incrementAchievementProgress(achievementId, increment, timestamp);
        });
    }
    
    public void unlockAchievement(String achievementId) {
        executor.execute(() -> {
            long timestamp = System.currentTimeMillis();
            achievementDao.unlockAchievement(achievementId, timestamp, timestamp);
        });
    }

    // ==================== UTILITY METHODS ====================
    
    public void initializeDefaultData() {
        executor.execute(() -> {
            // Check if data already exists
            int azkarCount = azkarDao.getAzkarCount();
            if (azkarCount == 0) {
                // Initialize default azkar data
                initializeDefaultAzkar();
                initializeDefaultAchievements();
            }
        });
    }
    
    private void initializeDefaultAzkar() {
        // This will be implemented with actual azkar data
        // For now, we'll add some sample data
    }
    
    private void initializeDefaultAchievements() {
        // Initialize default achievements
        // This will be implemented with actual achievement data
    }
    
    public String getCurrentDate() {
        return dateFormat.format(new Date());
    }
    
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}

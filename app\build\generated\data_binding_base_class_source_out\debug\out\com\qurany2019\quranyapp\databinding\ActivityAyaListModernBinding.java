// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAyaListModernBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AdView adView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final CollapsingToolbarLayout collapsingToolbar;

  @NonNull
  public final MaterialCardView downloadProgressCard;

  @NonNull
  public final TextView downloadingText;

  @NonNull
  public final LinearProgressIndicator modernProgressBar;

  @NonNull
  public final TextView progressPercentage;

  @NonNull
  public final ImageView quranIcon;

  @NonNull
  public final TextView reciterNameText;

  @NonNull
  public final EditText searchEditText;

  @NonNull
  public final TextView surahCountText;

  @NonNull
  public final RecyclerView surahRecyclerView;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAyaListModernBinding(@NonNull CoordinatorLayout rootView, @NonNull AdView adView,
      @NonNull AppBarLayout appBarLayout, @NonNull CollapsingToolbarLayout collapsingToolbar,
      @NonNull MaterialCardView downloadProgressCard, @NonNull TextView downloadingText,
      @NonNull LinearProgressIndicator modernProgressBar, @NonNull TextView progressPercentage,
      @NonNull ImageView quranIcon, @NonNull TextView reciterNameText,
      @NonNull EditText searchEditText, @NonNull TextView surahCountText,
      @NonNull RecyclerView surahRecyclerView, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.adView = adView;
    this.appBarLayout = appBarLayout;
    this.collapsingToolbar = collapsingToolbar;
    this.downloadProgressCard = downloadProgressCard;
    this.downloadingText = downloadingText;
    this.modernProgressBar = modernProgressBar;
    this.progressPercentage = progressPercentage;
    this.quranIcon = quranIcon;
    this.reciterNameText = reciterNameText;
    this.searchEditText = searchEditText;
    this.surahCountText = surahCountText;
    this.surahRecyclerView = surahRecyclerView;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAyaListModernBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAyaListModernBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_aya_list_modern, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAyaListModernBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.appBarLayout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.collapsingToolbar;
      CollapsingToolbarLayout collapsingToolbar = ViewBindings.findChildViewById(rootView, id);
      if (collapsingToolbar == null) {
        break missingId;
      }

      id = R.id.downloadProgressCard;
      MaterialCardView downloadProgressCard = ViewBindings.findChildViewById(rootView, id);
      if (downloadProgressCard == null) {
        break missingId;
      }

      id = R.id.downloadingText;
      TextView downloadingText = ViewBindings.findChildViewById(rootView, id);
      if (downloadingText == null) {
        break missingId;
      }

      id = R.id.modernProgressBar;
      LinearProgressIndicator modernProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (modernProgressBar == null) {
        break missingId;
      }

      id = R.id.progressPercentage;
      TextView progressPercentage = ViewBindings.findChildViewById(rootView, id);
      if (progressPercentage == null) {
        break missingId;
      }

      id = R.id.quranIcon;
      ImageView quranIcon = ViewBindings.findChildViewById(rootView, id);
      if (quranIcon == null) {
        break missingId;
      }

      id = R.id.reciterNameText;
      TextView reciterNameText = ViewBindings.findChildViewById(rootView, id);
      if (reciterNameText == null) {
        break missingId;
      }

      id = R.id.searchEditText;
      EditText searchEditText = ViewBindings.findChildViewById(rootView, id);
      if (searchEditText == null) {
        break missingId;
      }

      id = R.id.surahCountText;
      TextView surahCountText = ViewBindings.findChildViewById(rootView, id);
      if (surahCountText == null) {
        break missingId;
      }

      id = R.id.surahRecyclerView;
      RecyclerView surahRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (surahRecyclerView == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAyaListModernBinding((CoordinatorLayout) rootView, adView, appBarLayout,
          collapsingToolbar, downloadProgressCard, downloadingText, modernProgressBar,
          progressPercentage, quranIcon, reciterNameText, searchEditText, surahCountText,
          surahRecyclerView, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

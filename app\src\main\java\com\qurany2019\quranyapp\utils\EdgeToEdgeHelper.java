package com.qurany2019.quranyapp.utils;

import android.app.Activity;
import android.os.Build;
import android.view.View;
import android.view.Window;

/**
 * فئة مساعدة لتطبيق ميزة Edge-to-Edge في التطبيق
 * نسخة مبسطة تتجنب مشاكل التوافق
 */
public class EdgeToEdgeHelper {

    /**
     * إعداد Edge-to-Edge للنشاط
     * @param activity النشاط المراد تطبيق الميزة عليه
     * @param rootView العنصر الجذر (يمكن أن يكون null)
     */
    public static void setupEdgeToEdge(Activity activity, View rootView) {
        try {
            // تطبيق Edge-to-Edge بطريقة آمنة
            if (Build.VERSION.SDK_INT >= 30) { // Android 11+
                activity.getWindow().setDecorFitsSystemWindows(false);
            } else if (Build.VERSION.SDK_INT >= 21) { // Android 5+
                // استخدام الطريقة التقليدية للإصدارات القديمة
                activity.getWindow().getDecorView().setSystemUiVisibility(
                    0x00000100 | // SYSTEM_UI_FLAG_LAYOUT_STABLE
                    0x00000200 | // SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    0x00000400   // SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                );
            }

            // إعداد ألوان أشرطة النظام
            setupSystemBars(activity);

        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    /**
     * إعداد ألوان أشرطة النظام
     * @param activity النشاط
     */
    private static void setupSystemBars(Activity activity) {
        try {
            if (Build.VERSION.SDK_INT >= 21) { // Android 5+
                Window window = activity.getWindow();
                window.addFlags(0x80000000); // FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS
                window.setStatusBarColor(0xFF2E7D32); // اللون الأخضر الإسلامي الداكن
                window.setNavigationBarColor(0xFF000000); // اللون الأسود
            }
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }
}
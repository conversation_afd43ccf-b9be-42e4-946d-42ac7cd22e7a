// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class SingleRowayalistBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final RelativeLayout button;

  @NonNull
  public final RelativeLayout imageView;

  @NonNull
  public final ImageView quranCoverImage;

  @NonNull
  public final ImageView statusIcon;

  @NonNull
  public final TextView surahNumber;

  @NonNull
  public final TextView textView1;

  @NonNull
  public final TextView textView2;

  private SingleRowayalistBinding(@NonNull CardView rootView, @NonNull RelativeLayout button,
      @NonNull RelativeLayout imageView, @NonNull ImageView quranCoverImage,
      @NonNull ImageView statusIcon, @NonNull TextView surahNumber, @NonNull TextView textView1,
      @NonNull TextView textView2) {
    this.rootView = rootView;
    this.button = button;
    this.imageView = imageView;
    this.quranCoverImage = quranCoverImage;
    this.statusIcon = statusIcon;
    this.surahNumber = surahNumber;
    this.textView1 = textView1;
    this.textView2 = textView2;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static SingleRowayalistBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static SingleRowayalistBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.single_rowayalist, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static SingleRowayalistBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button;
      RelativeLayout button = ViewBindings.findChildViewById(rootView, id);
      if (button == null) {
        break missingId;
      }

      id = R.id.imageView;
      RelativeLayout imageView = ViewBindings.findChildViewById(rootView, id);
      if (imageView == null) {
        break missingId;
      }

      id = R.id.quranCoverImage;
      ImageView quranCoverImage = ViewBindings.findChildViewById(rootView, id);
      if (quranCoverImage == null) {
        break missingId;
      }

      id = R.id.statusIcon;
      ImageView statusIcon = ViewBindings.findChildViewById(rootView, id);
      if (statusIcon == null) {
        break missingId;
      }

      id = R.id.surahNumber;
      TextView surahNumber = ViewBindings.findChildViewById(rootView, id);
      if (surahNumber == null) {
        break missingId;
      }

      id = R.id.textView1;
      TextView textView1 = ViewBindings.findChildViewById(rootView, id);
      if (textView1 == null) {
        break missingId;
      }

      id = R.id.textView2;
      TextView textView2 = ViewBindings.findChildViewById(rootView, id);
      if (textView2 == null) {
        break missingId;
      }

      return new SingleRowayalistBinding((CardView) rootView, button, imageView, quranCoverImage,
          statusIcon, surahNumber, textView1, textView2);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

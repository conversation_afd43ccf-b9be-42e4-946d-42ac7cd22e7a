<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_aya_list_modern" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_aya_list_modern.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_aya_list_modern_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="230" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="83" endOffset="53"/></Target><Target id="@+id/collapsingToolbar" view="com.google.android.material.appbar.CollapsingToolbarLayout"><Expressions/><location startLine="17" startOffset="8" endLine="81" endOffset="68"/></Target><Target id="@+id/quranIcon" view="ImageView"><Expressions/><location startLine="34" startOffset="16" endLine="41" endOffset="57"/></Target><Target id="@+id/reciterNameText" view="TextView"><Expressions/><location startLine="44" startOffset="16" endLine="54" endOffset="46"/></Target><Target id="@+id/surahCountText" view="TextView"><Expressions/><location startLine="57" startOffset="16" endLine="67" endOffset="41"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="72" startOffset="12" endLine="79" endOffset="59"/></Target><Target id="@+id/searchEditText" view="EditText"><Expressions/><location startLine="120" startOffset="20" endLine="130" endOffset="45"/></Target><Target id="@+id/surahRecyclerView" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="137" startOffset="12" endLine="142" endOffset="60"/></Target><Target id="@+id/downloadProgressCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="149" startOffset="4" endLine="211" endOffset="55"/></Target><Target id="@+id/downloadingText" view="TextView"><Expressions/><location startLine="178" startOffset="16" endLine="186" endOffset="61"/></Target><Target id="@+id/progressPercentage" view="TextView"><Expressions/><location startLine="188" startOffset="16" endLine="195" endOffset="46"/></Target><Target id="@+id/modernProgressBar" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="199" startOffset="12" endLine="207" endOffset="37"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="221" startOffset="8" endLine="226" endOffset="67"/></Target></Targets></Layout>
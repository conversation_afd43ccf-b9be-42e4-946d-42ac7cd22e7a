<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.qurany2019.quranyapp"
    android:versionCode="4"
    android:versionName="4" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.wake_lock" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" /> <!-- الإضافة المطلوبة الجديدة -->


    <!-- أذونات القبلة وأوقات الصلاة -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!-- إذن الموقع في الخلفية مطلوب لإشعارات أوقات الصلاة التلقائية -->
    <!-- يتم استخدامه لحساب أوقات الصلاة الدقيقة حسب موقع المستخدم وإرسال إشعارات في الأوقات المناسبة -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />

    <!-- أذونات البوصلة والحساسات -->
    <uses-feature
        android:name="android.hardware.sensor.accelerometer"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.sensor.compass"
        android:required="true" />

    <!-- أذونات إضافية للإشعارات -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />

    <!-- أذونات إشعارات الوسائط الكاملة -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />

    <!-- Create a unique permission for your app and use it so only your app can receive your OneSignal messages. -->
    <permission
        android:name="com.qurany2019.quranyapp.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.qurany2019.quranyapp.permission.C2D_MESSAGE" /> <!-- c2dm RECEIVE are basic requirements for push messages through Google's FCM -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" /> <!-- Required, makes sure notifications are delivered on time. -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <!--
 Use to restore notifications the user hasn't interacted with.
         They could be missed notifications if the user reboots their device if this isn't in place.
    -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- START: ShortcutBadger -->
    <!-- Samsung -->
    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- HTC -->
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- Sony -->
    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- Apex -->
    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- Solid -->
    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- Huawei -->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- ZUK -->
    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- OPPO -->
    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- EvMe -->
    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" /> <!-- Android package visibility setting -->
    <queries>

        <!-- For browser content -->
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
        <!-- End of browser content -->
        <!-- For CustomTabsService -->
        <intent>
            <action android:name="android.support.customtabs.action.CustomTabsService" />
        </intent>
        <!-- End of CustomTabsService -->
    </queries>

    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />

    <permission
        android:name="com.qurany2019.quranyapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.qurany2019.quranyapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.qurany2019.quranyapp.QuranApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.QuranyApp"
        android:usesCleartextTraffic="true" >

        <!-- AdMob App ID -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7841751633097845~7935499105" />

        <!-- Register the Alarm Receiver -->
        <receiver android:name="com.qurany2019.quranyapp.Receiver" />

        <!-- Prayer Times Notification Receiver -->
        <receiver
            android:name="com.qurany2019.quranyapp.PrayerNotificationReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.PRAYER_NOTIFICATION" />
            </intent-filter>
        </receiver>

        <!-- Azkar Reminder Receiver -->
        <receiver
            android:name="com.qurany2019.quranyapp.AzkarReminderReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.AZKAR_REMINDER" />
            </intent-filter>
        </receiver>

        <!-- Boot Receiver to restart notifications -->
        <receiver
            android:name="com.qurany2019.quranyapp.BootReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter android:priority="1000" >
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.qurany2019.quranyapp.Splash"
            android:exported="true"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.QuranyApp" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.MainActivity"
            android:label="@string/title_activity_main"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.MainActivitySimple"
            android:label="@string/title_activity_main"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivitySimple" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.About"
            android:label="@string/title_activity_about"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.Hisn"
            android:label="@string/title_activity_hisn"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.Whtml"
            android:theme="@style/Theme.QuranyApp" />
        <activity
            android:name="com.qurany2019.quranyapp.Tazker"
            android:label="@string/title_activity_tazker"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.AyaList"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp" />
        <activity
            android:name="com.qurany2019.quranyapp.managerdb"
            android:label="@string/title_quran"
            android:launchMode="singleTop"
            android:theme="@style/Theme.QuranyApp" />
        <activity
            android:name="com.qurany2019.quranyapp.RecitesName"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp" />
        <activity
            android:name="com.qurany2019.quranyapp.Sellings"
            android:label="@string/title_quran"
            android:theme="@style/Theme.QuranyApp" />
        <activity
            android:name="com.qurany2019.quranyapp.quranActivity"
            android:configChanges="orientation|screenSize"
            android:label="Read Quran" />
        <activity android:name="com.qurany2019.quranyapp.PrivcyActivity" />
        <activity
            android:name="com.qurany2019.quranyapp.SettingsActivity"
            android:label="الإعدادات"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.QiblaActivity"
            android:label="اتجاه القبلة"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.PrayerTimesActivity"
            android:label="أوقات الصلاة"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.AzkarActivity"
            android:label="@string/azkar_title"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.AzkarDetailActivity"
            android:label="تفاصيل الأذكار"
            android:parentActivityName="com.qurany2019.quranyapp.AzkarActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.AzkarActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.MoreActivity"
            android:label="المزيد"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>

        <!-- أنشطة النظام الشامل -->
        <activity
            android:name="com.qurany2019.quranyapp.AzkarStatisticsActivity"
            android:label="إحصائيات الأذكار"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.AchievementsActivity"
            android:label="الإنجازات"
            android:parentActivityName="com.qurany2019.quranyapp.MainActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.MainActivity" />
        </activity>
        <activity
            android:name="com.qurany2019.quranyapp.ReminderSettingsActivity"
            android:label="إعدادات التذكيرات"
            android:parentActivityName="com.qurany2019.quranyapp.SettingsActivity"
            android:theme="@style/Theme.QuranyApp" >
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value="com.qurany2019.quranyapp.SettingsActivity" />
        </activity>

        <!-- خدمة إشعارات الوسائط مع أزرار التحكم -->
        <service
            android:name="com.qurany2019.quranyapp.MediaNotificationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

        <!-- خدمة إشعارات بسيطة وفعالة -->
        <service
            android:name="com.qurany2019.quranyapp.SimpleMediaNotification"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />

        <!-- BroadcastReceiver لأوامر الوسائط -->
        <receiver
            android:name="com.qurany2019.quranyapp.MediaActionReceiver"
            android:enabled="true"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.MEDIA_ACTION" />
            </intent-filter>
        </receiver>

        <!-- BroadcastReceiver للإشعار البسيط -->
        <receiver
            android:name="com.qurany2019.quranyapp.SimpleMediaReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="com.qurany2019.quranyapp.SIMPLE_MEDIA_CONTROL" />
            </intent-filter>
        </receiver>

        <!-- BroadcastReceiver للتحكم المباشر -->
        <receiver
            android:name="com.qurany2019.quranyapp.StaticMediaReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="STATIC_MEDIA_CONTROL" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.onesignal.FCMBroadcastReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >

            <!-- High priority so OneSignal payloads can be filtered from other FCM receivers -->
            <intent-filter android:priority="999" >
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />

                <category android:name="com.qurany2019.quranyapp" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.onesignal.HmsMessageServiceOneSignal"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <activity
            android:name="com.onesignal.NotificationOpenedActivityHMS"
            android:exported="true"
            android:noHistory="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
            </intent-filter>
        </activity>

        <service
            android:name="com.onesignal.FCMIntentService"
            android:exported="false" />
        <service
            android:name="com.onesignal.FCMIntentJobService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="com.onesignal.SyncService"
            android:exported="false"
            android:stopWithTask="true" />
        <service
            android:name="com.onesignal.SyncJobService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <activity
            android:name="com.onesignal.PermissionsActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <receiver
            android:name="com.onesignal.NotificationDismissReceiver"
            android:exported="true" />
        <receiver
            android:name="com.onesignal.BootUpReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.onesignal.UpgradeReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.onesignal.NotificationOpenedReceiver"
            android:excludeFromRecents="true"
            android:exported="true"
            android:noHistory="true"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.onesignal.NotificationOpenedReceiverAndroid22AndOlder"
            android:excludeFromRecents="true"
            android:exported="true"
            android:noHistory="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
        <activity
            android:name="com.google.android.gms.ads.AdActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent" />

        <provider
            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
            android:authorities="com.qurany2019.quranyapp.mobileadsinitprovider"
            android:exported="false"
            android:initOrder="100" />

        <service
            android:name="com.google.android.gms.ads.AdService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
            android:exported="false" />
        <activity
            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:launchMode="singleTask"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.qurany2019.quranyapp.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <receiver
            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
            android:enabled="true"
            android:exported="false" >
        </receiver>

        <service
            android:name="com.google.android.gms.measurement.AppMeasurementService"
            android:enabled="true"
            android:exported="false" />
        <service
            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
            android:enabled="true"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.qurany2019.quranyapp.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <uses-library
            android:name="android.ext.adservices"
            android:required="false" />

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>
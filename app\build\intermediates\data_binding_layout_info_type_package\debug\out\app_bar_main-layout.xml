<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="app_bar_main" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\app_bar_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.appbar.AppBarLayout"><Targets><Target tag="layout/app_bar_main_0" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="1" startOffset="0" endLine="17" endOffset="49"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="7" startOffset="4" endLine="16" endOffset="62"/></Target></Targets></Layout>
<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- الظل العلوي فقط -->
    <item android:top="0dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#20000000"
                android:centerColor="#10000000"
                android:endColor="#05000000"
                android:angle="90" />
            <corners
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>

    <!-- الخلفية الرئيسية المتلاصقة -->
    <item android:bottom="0dp">
        <shape android:shape="rectangle">
            <!-- خلفية متدرجة عصرية مع ألوان إسلامية -->
            <gradient
                android:startColor="#FFFFFF"
                android:centerColor="#F8FFFE"
                android:endColor="#F0FDF4"
                android:angle="135"
                android:type="linear" />

            <!-- بدون زوايا مدورة للالتصاق -->
            <corners
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />

            <!-- حدود علوية فقط -->
            <stroke
                android:width="2dp"
                android:color="#E5F3E5" />
        </shape>
    </item>

    <!-- تأثير الإضاءة الداخلية -->
    <item
        android:bottom="0dp"
        android:top="2dp"
        android:left="0dp"
        android:right="0dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#15FFFFFF"
                android:centerColor="#08FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="90"
                android:type="linear" />
            <corners
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>

    <!-- خط إضاءة علوي -->
    <item
        android:bottom="0dp"
        android:top="2dp"
        android:left="16dp"
        android:right="16dp">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#25FFFFFF"
                android:endColor="#00FFFFFF"
                android:angle="90" />
            <corners
                android:topLeftRadius="0dp"
                android:topRightRadius="0dp"
                android:bottomLeftRadius="0dp"
                android:bottomRightRadius="0dp" />
        </shape>
    </item>

</layer-list>

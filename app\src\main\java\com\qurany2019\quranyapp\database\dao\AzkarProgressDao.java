package com.qurany2019.quranyapp.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;
import androidx.lifecycle.LiveData;

import com.qurany2019.quranyapp.database.entities.AzkarProgressEntity;

import java.util.List;

@Dao
public interface AzkarProgressDao {

    // Insert operations
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertProgress(AzkarProgressEntity progress);

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insertAllProgress(List<AzkarProgressEntity> progressList);

    // Update operations
    @Update
    int updateProgress(AzkarProgressEntity progress);

    // Delete operations
    @Delete
    int deleteProgress(AzkarProgressEntity progress);

    @Query("DELETE FROM azkar_progress_table WHERE id = :progressId")
    int deleteProgressById(int progressId);

    @Query("DELETE FROM azkar_progress_table WHERE azkar_id = :azkarId")
    int deleteProgressByAzkarId(int azkarId);

    @Query("DELETE FROM azkar_progress_table WHERE date = :date")
    int deleteProgressByDate(String date);

    @Query("DELETE FROM azkar_progress_table WHERE date < :date")
    int deleteProgressOlderThan(String date);

    @Query("DELETE FROM azkar_progress_table")
    int deleteAllProgress();

    // Select operations
    @Query("SELECT * FROM azkar_progress_table WHERE id = :progressId")
    AzkarProgressEntity getProgressById(int progressId);

    @Query("SELECT * FROM azkar_progress_table WHERE azkar_id = :azkarId AND date = :date")
    AzkarProgressEntity getProgressByAzkarAndDate(int azkarId, String date);

    @Query("SELECT * FROM azkar_progress_table WHERE azkar_id = :azkarId AND date = :date")
    LiveData<AzkarProgressEntity> getProgressByAzkarAndDateLiveData(int azkarId, String date);

    @Query("SELECT * FROM azkar_progress_table WHERE date = :date ORDER BY azkar_id")
    List<AzkarProgressEntity> getProgressByDate(String date);

    @Query("SELECT * FROM azkar_progress_table WHERE date = :date ORDER BY azkar_id")
    LiveData<List<AzkarProgressEntity>> getProgressByDateLiveData(String date);

    @Query("SELECT * FROM azkar_progress_table WHERE azkar_id = :azkarId ORDER BY date DESC")
    List<AzkarProgressEntity> getProgressByAzkarId(int azkarId);

    @Query("SELECT * FROM azkar_progress_table WHERE azkar_id = :azkarId ORDER BY date DESC LIMIT 1")
    AzkarProgressEntity getProgressByAzkarId(String azkarId);

    @Query("SELECT * FROM azkar_progress_table WHERE azkar_id = :azkarId ORDER BY date DESC")
    LiveData<List<AzkarProgressEntity>> getProgressByAzkarIdLiveData(int azkarId);

    @Query("SELECT * FROM azkar_progress_table ORDER BY date DESC, azkar_id")
    List<AzkarProgressEntity> getAllProgress();

    @Query("SELECT * FROM azkar_progress_table ORDER BY date DESC, azkar_id")
    LiveData<List<AzkarProgressEntity>> getAllProgressLiveData();

    // Completion queries
    @Query("SELECT * FROM azkar_progress_table WHERE is_completed = 1 AND date = :date")
    List<AzkarProgressEntity> getCompletedProgressByDate(String date);

    @Query("SELECT * FROM azkar_progress_table WHERE is_completed = 1 AND date = :date")
    LiveData<List<AzkarProgressEntity>> getCompletedProgressByDateLiveData(String date);

    @Query("SELECT * FROM azkar_progress_table WHERE is_completed = 0 AND date = :date")
    List<AzkarProgressEntity> getIncompleteProgressByDate(String date);

    @Query("SELECT * FROM azkar_progress_table WHERE is_completed = 0 AND date = :date")
    LiveData<List<AzkarProgressEntity>> getIncompleteProgressByDateLiveData(String date);

    // Count operations
    @Query("SELECT COUNT(*) FROM azkar_progress_table WHERE date = :date")
    int getProgressCountByDate(String date);

    @Query("SELECT COUNT(*) FROM azkar_progress_table WHERE date = :date AND is_completed = 1")
    int getCompletedCountByDate(String date);

    @Query("SELECT COUNT(*) FROM azkar_progress_table WHERE date = :date AND is_completed = 0")
    int getIncompleteCountByDate(String date);

    @Query("SELECT COUNT(*) FROM azkar_progress_table WHERE azkar_id = :azkarId AND is_completed = 1")
    int getCompletedCountByAzkarId(int azkarId);

    @Query("SELECT SUM(current_count) FROM azkar_progress_table WHERE is_completed = 1")
    int getTotalCompletedCount();

    // Statistics queries
    @Query("SELECT AVG(current_count) FROM azkar_progress_table WHERE date = :date")
    double getAverageProgressByDate(String date);

    @Query("SELECT SUM(current_count) FROM azkar_progress_table WHERE date = :date")
    int getTotalCountByDate(String date);

    @Query("SELECT AVG(session_duration) FROM azkar_progress_table WHERE date = :date AND is_completed = 1")
    double getAverageSessionDurationByDate(String date);

    @Query("SELECT SUM(session_duration) FROM azkar_progress_table WHERE date = :date")
    long getTotalSessionDurationByDate(String date);

    // Date range queries
    @Query("SELECT * FROM azkar_progress_table WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    List<AzkarProgressEntity> getProgressBetweenDates(String startDate, String endDate);

    @Query("SELECT * FROM azkar_progress_table WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    LiveData<List<AzkarProgressEntity>> getProgressBetweenDatesLiveData(String startDate, String endDate);

    @Query("SELECT COUNT(*) FROM azkar_progress_table WHERE date BETWEEN :startDate AND :endDate AND is_completed = 1")
    int getCompletedCountBetweenDates(String startDate, String endDate);

    // Recent operations
    @Query("SELECT * FROM azkar_progress_table ORDER BY updated_at DESC LIMIT :limit")
    List<AzkarProgressEntity> getRecentProgress(int limit);

    @Query("SELECT * FROM azkar_progress_table WHERE is_completed = 1 ORDER BY completion_time DESC LIMIT :limit")
    List<AzkarProgressEntity> getRecentCompletions(int limit);

    // Streak calculations
    @Query("SELECT DISTINCT date FROM azkar_progress_table WHERE is_completed = 1 ORDER BY date DESC")
    List<String> getCompletionDates();

    @Query("SELECT COUNT(DISTINCT date) FROM azkar_progress_table WHERE is_completed = 1 AND date BETWEEN :startDate AND :endDate")
    int getCompletionDaysCount(String startDate, String endDate);

    // Performance queries
    @Query("SELECT MIN(session_duration) FROM azkar_progress_table WHERE is_completed = 1 AND session_duration > 0")
    long getBestCompletionTime();

    @Query("SELECT MAX(session_duration) FROM azkar_progress_table WHERE is_completed = 1")
    long getWorstCompletionTime();

    @Query("SELECT * FROM azkar_progress_table WHERE session_duration = (SELECT MIN(session_duration) FROM azkar_progress_table WHERE is_completed = 1 AND session_duration > 0)")
    AzkarProgressEntity getBestPerformance();

    // Maintenance operations
    @Query("UPDATE azkar_progress_table SET updated_at = :timestamp WHERE date = :date")
    int updateDateTimestamp(String date, long timestamp);

    @Query("SELECT * FROM azkar_progress_table WHERE updated_at < :timestamp")
    List<AzkarProgressEntity> getProgressOlderThan(long timestamp);

    // Validation
    @Query("SELECT COUNT(*) FROM azkar_progress_table WHERE azkar_id = :azkarId AND date = :date")
    int checkProgressExists(int azkarId, String date);

    // Custom increment operation
    @Query("UPDATE azkar_progress_table SET current_count = current_count + 1, updated_at = :timestamp WHERE azkar_id = :azkarId AND date = :date")
    int incrementProgress(int azkarId, String date, long timestamp);

    @Query("UPDATE azkar_progress_table SET is_completed = 1, completion_time = :completionTime, session_duration = :sessionDuration, updated_at = :timestamp WHERE azkar_id = :azkarId AND date = :date")
    int markAsCompleted(int azkarId, String date, long completionTime, long sessionDuration, long timestamp);

    // Reset operations
    @Query("UPDATE azkar_progress_table SET current_count = 0, is_completed = 0, start_time = 0, completion_time = 0, session_duration = 0, updated_at = :timestamp WHERE azkar_id = :azkarId AND date = :date")
    int resetProgress(int azkarId, String date, long timestamp);
}

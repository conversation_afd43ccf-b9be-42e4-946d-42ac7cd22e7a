package com.qurany2019.quranyapp;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import java.util.Random;

public class AzkarNotificationManager {
    private static final String CHANNEL_ID = "azkar_progress_channel";
    private static final String CHANNEL_NAME = "تقدم الأذكار";
    private static final String CHANNEL_DESCRIPTION = "إشعارات تقدم وإنجاز الأذكار";
    
    private Context context;
    private NotificationManagerCompat notificationManager;
    private Random random;

    // Notification types
    public static final int TYPE_AZKAR_COMPLETED = 1;
    public static final int TYPE_SECTION_COMPLETED = 2;
    public static final int TYPE_ALL_COMPLETED = 3;
    public static final int TYPE_ACHIEVEMENT = 4;
    public static final int TYPE_REMINDER = 5;

    public AzkarNotificationManager(Context context) {
        this.context = context;
        this.notificationManager = NotificationManagerCompat.from(context);
        this.random = new Random();
        createNotificationChannel();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableVibration(true);
            channel.setVibrationPattern(new long[]{100, 200, 100});
            
            NotificationManager manager = context.getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }

    // Show notification when a single azkar is completed
    public void showAzkarCompletedNotification(String azkarTitle) {
        String[] messages = {
            "بارك الله فيك! أكملت: " + azkarTitle,
            "أحسنت! تم إنجاز: " + azkarTitle,
            "جزاك الله خيراً! أنهيت: " + azkarTitle,
            "تقبل الله منك: " + azkarTitle
        };
        
        String message = messages[random.nextInt(messages.length)];
        showNotification(TYPE_AZKAR_COMPLETED, "ذكر مكتمل ✓", message, "🤲");
    }

    // Show notification when a section is completed
    public void showSectionCompletedNotification(String sectionName) {
        String[] messages = {
            "مبارك! أكملت قسم " + sectionName + " بالكامل",
            "أحسنت! انتهيت من " + sectionName,
            "بارك الله فيك! أتممت " + sectionName,
            "جزاك الله خيراً! أكملت " + sectionName
        };
        
        String[] duas = {
            "تقبل الله منك وأعطاك من فضله",
            "بارك الله في عمرك وعملك", 
            "جعلك الله من الذاكرين الشاكرين",
            "أثابك الله وزادك من فضله"
        };
        
        String message = messages[random.nextInt(messages.length)];
        String dua = duas[random.nextInt(duas.length)];
        
        showNotification(TYPE_SECTION_COMPLETED, "قسم مكتمل 🌟", message + "\n" + dua, "🎉");
    }

    // Show notification when all azkar are completed
    public void showAllCompletedNotification() {
        String[] messages = {
            "تهانينا! أكملت جميع الأذكار اليوم",
            "مبارك! انتهيت من كل الأذكار",
            "أحسنت! أتممت جميع الأذكار",
            "بارك الله فيك! أكملت كل شيء"
        };
        
        String[] duas = {
            "تقبل الله منك وبارك فيك\nجعلك من الذاكرين الشاكرين",
            "أثابك الله وزادك من فضله\nوجعل عملك في ميزان حسناتك",
            "بارك الله في عمرك وعملك\nوجعلك من أهل الجنة",
            "جزاك الله خيراً وأعطاك من فضله\nورزقك الثبات على دينه"
        };
        
        String message = messages[random.nextInt(messages.length)];
        String dua = duas[random.nextInt(duas.length)];
        
        showNotification(TYPE_ALL_COMPLETED, "جميع الأذكار مكتملة! 👑", message + "\n\n" + dua, "🏆");
    }

    // Show achievement notification
    public void showAchievementNotification(String achievementTitle, String description) {
        showNotification(TYPE_ACHIEVEMENT, "إنجاز جديد! 🏅", achievementTitle + "\n" + description, "⭐");
    }

    // Show reminder notification
    public void showReminderNotification(String title, String message) {
        showNotification(TYPE_REMINDER, title, message, "🔔");
    }

    // Generic notification method
    private void showNotification(int type, String title, String message, String emoji) {
        Intent intent = new Intent(context, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 
            type, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification) // You'll need to add this icon
            .setContentTitle(emoji + " " + title)
            .setContentText(message)
            .setStyle(new NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setVibrate(new long[]{100, 200, 100});

        // Set different colors for different types
        switch (type) {
            case TYPE_AZKAR_COMPLETED:
                builder.setColor(0xFF4CAF50); // Green
                break;
            case TYPE_SECTION_COMPLETED:
                builder.setColor(0xFF2196F3); // Blue
                break;
            case TYPE_ALL_COMPLETED:
                builder.setColor(0xFFFF9800); // Orange
                break;
            case TYPE_ACHIEVEMENT:
                builder.setColor(0xFF9C27B0); // Purple
                break;
            case TYPE_REMINDER:
                builder.setColor(0xFF607D8B); // Blue Grey
                break;
        }

        // Show the notification
        int notificationId = type * 1000 + random.nextInt(1000);
        notificationManager.notify(notificationId, builder.build());
    }

    // Show progress notification (for ongoing azkar)
    public void showProgressNotification(String sectionName, int completed, int total) {
        String title = "جاري القراءة... " + sectionName;
        String message = "أكملت " + completed + " من " + total + " أذكار";
        
        Intent intent = new Intent(context, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context, 
            0, 
            intent, 
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setProgress(total, completed, false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setColor(0xFF4CAF50);

        notificationManager.notify(999, builder.build());
    }

    // Cancel progress notification
    public void cancelProgressNotification() {
        notificationManager.cancel(999);
    }

    // Check if notifications are enabled
    public boolean areNotificationsEnabled() {
        return notificationManager.areNotificationsEnabled();
    }

    // Show motivational messages based on progress
    public void showMotivationalMessage(int progressPercentage, String sectionName) {
        if (progressPercentage == 25) {
            showNotification(TYPE_REMINDER, "ربع الطريق! 💪", 
                "أكملت 25% من " + sectionName + "\nاستمر بارك الله فيك", "🌟");
        } else if (progressPercentage == 50) {
            showNotification(TYPE_REMINDER, "نصف الطريق! 🎯", 
                "أكملت نصف " + sectionName + "\nأنت تقوم بعمل رائع", "⭐");
        } else if (progressPercentage == 75) {
            showNotification(TYPE_REMINDER, "أوشكت على الانتهاء! 🚀", 
                "أكملت 75% من " + sectionName + "\nقليل وتنتهي", "🔥");
        }
    }
}

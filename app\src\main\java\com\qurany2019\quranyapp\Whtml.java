package com.qurany2019.quranyapp;


import android.view.View;
import android.content.Intent;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;
import androidx.fragment.app.FragmentTransaction;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.ActionBar;
import android.os.Bundle;
import android.webkit.WebView;
import androidx.appcompat.app.ActionBar;

/**
 * Created by java_dude on 06/06/18.
 */
public class Whtml extends AppCompatActivity implements  ActionBar.TabListener  {
    private AdView mAdView;
    WebView webView;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // إزالة fullscreen mode لإظهار شريط الأزرار بشكل طبيعي

        setContentView(R.layout.activity_whtml);
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);
        mAdView = (AdView) findViewById(R.id.adView);
        webView = (WebView) findViewById(R.id.web);
        Intent intent = getIntent();
       int  page = intent.getExtras().getInt("h");
        page++;
        webView.loadUrl("file:///android_asset/html/"+ page +".html");

    }


    @Override
    public void onTabSelected(ActionBar.Tab tab, FragmentTransaction ft) {

    }

    @Override
    public void onTabUnselected(ActionBar.Tab tab, FragmentTransaction ft) {

    }

    @Override
    public void onTabReselected(ActionBar.Tab tab, FragmentTransaction ft) {

    }
}
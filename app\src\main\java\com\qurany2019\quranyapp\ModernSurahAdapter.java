package com.qurany2019.quranyapp;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.ImageView;
import android.widget.TextView;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import java.io.File;
import java.util.ArrayList;

public class ModernSurahAdapter extends RecyclerView.Adapter<ModernSurahAdapter.SurahViewHolder> {

    private ArrayList<AuthorClass> surahList;
    private Context context;
    private OnSurahClickListener listener;
    private String reciterName;

    public interface OnSurahClickListener {
        void onPlayClick(AuthorClass surah, int position);
        void onDownloadClick(AuthorClass surah, int position);
    }

    public ModernSurahAdapter(Context context, ArrayList<AuthorClass> surahList, String reciterName) {
        this.context = context;
        this.surahList = surahList;
        this.reciterName = reciterName;
    }

    public void setOnSurahClickListener(OnSurahClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public SurahViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_surah_modern, parent, false);
        return new SurahViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SurahViewHolder holder, int position) {
        AuthorClass surah = surahList.get(position);

        // تعيين البيانات
        holder.surahNumber.setText(String.valueOf(position + 1));
        holder.surahNameArabic.setText(surah.RealName);

        // فحص حالة التنزيل
        boolean isDownloaded = isFileDownloaded(surah.ServerName);

        if (isDownloaded) {
            // السورة محملة
            holder.statusIcon.setImageResource(R.drawable.ic_check_circle);
            holder.statusIcon.setColorFilter(context.getResources().getColor(R.color.islamicGreenLight));
            holder.statusText.setText(context.getString(R.string.ready_to_play));
            holder.statusText.setTextColor(context.getResources().getColor(R.color.islamicGreenLight));
            holder.downloadButton.setVisibility(View.GONE);
        } else {
            // السورة غير محملة
            holder.statusIcon.setImageResource(R.drawable.ic_cloud_download);
            holder.statusIcon.setColorFilter(context.getResources().getColor(R.color.colorAccent));
            holder.statusText.setText(context.getString(R.string.tap_to_download));
            holder.statusText.setTextColor(context.getResources().getColor(R.color.textColorSecondary));
            holder.downloadButton.setVisibility(View.VISIBLE);
        }

        // إخفاء شريط التقدم افتراضياً
        holder.itemProgressBar.setVisibility(View.GONE);

        // أحداث النقر
        holder.playButton.setOnClickListener(v -> {
            animateButton(holder.playButton);
            if (listener != null) {
                listener.onPlayClick(surah, position);
            }
        });

        holder.downloadButton.setOnClickListener(v -> {
            animateButton(holder.downloadButton);
            if (listener != null) {
                listener.onDownloadClick(surah, position);
            }
        });

        // النقر على العنصر كاملاً
        holder.itemView.setOnClickListener(v -> {
            animateCard(holder.itemView);
            if (listener != null) {
                listener.onPlayClick(surah, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return surahList.size();
    }

    // فحص وجود الملف المحلي باستخدام نظام Cache الذكي
    private boolean isFileDownloaded(String fileName) {
        try {
            // تحديث cache مرة واحدة فقط عند الحاجة
            ensureCacheUpdated();

            // استخدام نفس نظام البحث الذكي
            return isFileInCache(reciterName, fileName);

        } catch (Exception e) {
            android.util.Log.e("ADAPTER_FILE_CHECK", "خطأ في فحص الملف: " + e.getMessage());
            return false;
        }
    }

    // التأكد من تحديث cache مرة واحدة فقط
    private void ensureCacheUpdated() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCacheUpdate > CACHE_VALIDITY || !localFilesCache.containsKey(reciterName)) {
            File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
            File quranAppDir = new File(musicDir, "قرآني");
            File audioDir = new File(quranAppDir, reciterName);
            updateLocalFilesCache(audioDir, reciterName);
        }
    }

    // Cache للملفات المحلية (نفس النظام من AyaList)
    private static java.util.Map<String, java.util.Set<String>> localFilesCache = new java.util.HashMap<>();
    private static long lastCacheUpdate = 0;
    private static final long CACHE_VALIDITY = 30000; // 30 ثانية

    /**
     * تحديث cache الملفات المحلية
     */
    private void updateLocalFilesCache(File audioDir, String reciterName) {
        try {
            long currentTime = System.currentTimeMillis();

            if (currentTime - lastCacheUpdate > CACHE_VALIDITY || !localFilesCache.containsKey(reciterName)) {
                java.util.Set<String> files = new java.util.HashSet<>();

                if (audioDir.exists()) {
                    String[] fileList = audioDir.list();
                    if (fileList != null) {
                        android.util.Log.d("ADAPTER_CACHE", "📁 فحص مجلد: " + audioDir.getAbsolutePath());
                        for (String fileName : fileList) {
                            android.util.Log.d("ADAPTER_CACHE", "📄 ملف موجود: " + fileName);
                            if (fileName.endsWith(".mp3")) {
                                String baseName = fileName.substring(0, fileName.lastIndexOf(".mp3"));
                                files.add(baseName);
                                android.util.Log.d("ADAPTER_CACHE", "✅ تم إضافة للـ cache: " + baseName);
                            }
                        }
                    }
                }

                localFilesCache.put(reciterName, files);
                lastCacheUpdate = currentTime;
                android.util.Log.d("ADAPTER_CACHE", "تم تحديث cache للقارئ: " + reciterName + " - عدد الملفات: " + files.size());
            }
        } catch (Exception e) {
            android.util.Log.e("ADAPTER_CACHE", "خطأ في تحديث cache: " + e.getMessage());
        }
    }

    /**
     * فحص وجود الملف في cache
     */
    private boolean isFileInCache(String reciterName, String surahCode) {
        try {
            java.util.Set<String> files = localFilesCache.get(reciterName);
            if (files != null) {
                android.util.Log.d("ADAPTER_CACHE", "🔍 البحث عن: " + surahCode + " في cache");
                android.util.Log.d("ADAPTER_CACHE", "📋 الملفات المتاحة: " + files.toString());

                // البحث المباشر بالرقم
                boolean found = files.contains(surahCode);
                if (found) {
                    android.util.Log.d("ADAPTER_CACHE", "✅ تم العثور على الملف بالرقم");
                    return true;
                }

                // البحث باسم السورة
                String surahName = getSurahNameByCode(surahCode);
                if (surahName != null) {
                    android.util.Log.d("ADAPTER_CACHE", "🔍 البحث باسم السورة: " + surahName);
                    for (String fileName : files) {
                        if (fileName.contains(surahName)) {
                            android.util.Log.d("ADAPTER_CACHE", "✅ تم العثور على الملف باسم السورة: " + fileName);
                            return true;
                        }
                    }
                }

                android.util.Log.d("ADAPTER_CACHE", "❌ لم يتم العثور على الملف");
                return false;
            }
        } catch (Exception e) {
        }
        return false;
    }

    /**
     * الحصول على اسم السورة من الرقم
     */
    private String getSurahNameByCode(String surahCode) {
        try {
            int code = Integer.parseInt(surahCode);
            String[] surahNames = {
                "الفاتحة", "البقرة", "ال عمران", "النساء", "المائدة", "الانعام", "الأعراف", "الأنفال", "التوبة", "يونس",
                "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
                "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
                "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
                "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
                "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
                "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
                "نوح", "الجن", "المزمل", "المدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
                "التكوير", "الانفطار", "المطففين", "الانشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
                "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
                "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
                "المسد", "الاخلاص", "الفلق", "الناس"
            };

            if (code >= 1 && code <= surahNames.length) {
                return surahNames[code - 1].trim();
            }
        } catch (Exception e) {
        }
        return null;
    }

    // تحديث شريط التقدم لعنصر معين
    public void updateProgress(int position, int progress) {
        if (position >= 0 && position < surahList.size()) {
            notifyItemChanged(position);
        }
    }

    // إظهار شريط التقدم لعنصر معين
    public void showProgress(int position) {
        if (position >= 0 && position < surahList.size()) {
            notifyItemChanged(position);
        }
    }

    // إخفاء شريط التقدم لعنصر معين
    public void hideProgress(int position) {
        if (position >= 0 && position < surahList.size()) {
            notifyItemChanged(position);
        }
    }

    // انيميشن للأزرار
    private void animateButton(View button) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(button, "scaleX", 1f, 0.9f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(button, "scaleY", 1f, 0.9f, 1f);
        scaleX.setDuration(150);
        scaleY.setDuration(150);
        scaleX.setInterpolator(new AccelerateDecelerateInterpolator());
        scaleY.setInterpolator(new AccelerateDecelerateInterpolator());
        scaleX.start();
        scaleY.start();
    }

    // انيميشن للبطاقة
    private void animateCard(View card) {
        ObjectAnimator elevation = ObjectAnimator.ofFloat(card, "elevation", 4f, 8f, 4f);
        elevation.setDuration(200);
        elevation.setInterpolator(new AccelerateDecelerateInterpolator());
        elevation.start();
    }

    // فلترة القائمة للبحث
    public void filter(String query) {
        ArrayList<AuthorClass> filteredList = new ArrayList<>();

        if (query.isEmpty()) {
            filteredList.addAll(surahList);
        } else {
            for (AuthorClass surah : surahList) {
                if (surah.RealName.toLowerCase().contains(query.toLowerCase())) {
                    filteredList.add(surah);
                }
            }
        }

        surahList = filteredList;
        notifyDataSetChanged();
    }

    // ViewHolder
    public static class SurahViewHolder extends RecyclerView.ViewHolder {
        TextView surahNumber, surahNameArabic, statusText;
        ImageView statusIcon;
        MaterialButton downloadButton;
        FloatingActionButton playButton;
        LinearProgressIndicator itemProgressBar;

        public SurahViewHolder(@NonNull View itemView) {
            super(itemView);
            surahNumber = itemView.findViewById(R.id.surahNumber);
            surahNameArabic = itemView.findViewById(R.id.surahNameArabic);
            statusText = itemView.findViewById(R.id.statusText);
            statusIcon = itemView.findViewById(R.id.statusIcon);
            downloadButton = itemView.findViewById(R.id.downloadButton);
            playButton = itemView.findViewById(R.id.playButton);
            itemProgressBar = itemView.findViewById(R.id.itemProgressBar);
        }
    }
}

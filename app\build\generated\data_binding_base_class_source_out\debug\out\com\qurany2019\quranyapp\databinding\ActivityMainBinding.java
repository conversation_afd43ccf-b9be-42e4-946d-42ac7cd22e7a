// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.google.android.material.navigation.NavigationView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final DrawerLayout rootView;

  @NonNull
  public final LinearLayout adSection;

  @NonNull
  public final AdView adView;

  @NonNull
  public final LinearLayout btnMoreApps;

  @NonNull
  public final LinearLayout btnQuranListen;

  @NonNull
  public final LinearLayout btnQuranSuras;

  @NonNull
  public final LinearLayout buttonsContainer;

  @NonNull
  public final DrawerLayout drawerLayout;

  @NonNull
  public final LinearLayout languageToggleContainer;

  @NonNull
  public final TextView mainTitle;

  @NonNull
  public final ImageView menuIcon;

  @NonNull
  public final NavigationView navView;

  @NonNull
  public final LinearLayout topBar;

  @NonNull
  public final TextView tvLanguageAr;

  @NonNull
  public final TextView tvLanguageEn;

  private ActivityMainBinding(@NonNull DrawerLayout rootView, @NonNull LinearLayout adSection,
      @NonNull AdView adView, @NonNull LinearLayout btnMoreApps,
      @NonNull LinearLayout btnQuranListen, @NonNull LinearLayout btnQuranSuras,
      @NonNull LinearLayout buttonsContainer, @NonNull DrawerLayout drawerLayout,
      @NonNull LinearLayout languageToggleContainer, @NonNull TextView mainTitle,
      @NonNull ImageView menuIcon, @NonNull NavigationView navView, @NonNull LinearLayout topBar,
      @NonNull TextView tvLanguageAr, @NonNull TextView tvLanguageEn) {
    this.rootView = rootView;
    this.adSection = adSection;
    this.adView = adView;
    this.btnMoreApps = btnMoreApps;
    this.btnQuranListen = btnQuranListen;
    this.btnQuranSuras = btnQuranSuras;
    this.buttonsContainer = buttonsContainer;
    this.drawerLayout = drawerLayout;
    this.languageToggleContainer = languageToggleContainer;
    this.mainTitle = mainTitle;
    this.menuIcon = menuIcon;
    this.navView = navView;
    this.topBar = topBar;
    this.tvLanguageAr = tvLanguageAr;
    this.tvLanguageEn = tvLanguageEn;
  }

  @Override
  @NonNull
  public DrawerLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.adSection;
      LinearLayout adSection = ViewBindings.findChildViewById(rootView, id);
      if (adSection == null) {
        break missingId;
      }

      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.btnMoreApps;
      LinearLayout btnMoreApps = ViewBindings.findChildViewById(rootView, id);
      if (btnMoreApps == null) {
        break missingId;
      }

      id = R.id.btnQuranListen;
      LinearLayout btnQuranListen = ViewBindings.findChildViewById(rootView, id);
      if (btnQuranListen == null) {
        break missingId;
      }

      id = R.id.btnQuranSuras;
      LinearLayout btnQuranSuras = ViewBindings.findChildViewById(rootView, id);
      if (btnQuranSuras == null) {
        break missingId;
      }

      id = R.id.buttonsContainer;
      LinearLayout buttonsContainer = ViewBindings.findChildViewById(rootView, id);
      if (buttonsContainer == null) {
        break missingId;
      }

      DrawerLayout drawerLayout = (DrawerLayout) rootView;

      id = R.id.languageToggleContainer;
      LinearLayout languageToggleContainer = ViewBindings.findChildViewById(rootView, id);
      if (languageToggleContainer == null) {
        break missingId;
      }

      id = R.id.mainTitle;
      TextView mainTitle = ViewBindings.findChildViewById(rootView, id);
      if (mainTitle == null) {
        break missingId;
      }

      id = R.id.menuIcon;
      ImageView menuIcon = ViewBindings.findChildViewById(rootView, id);
      if (menuIcon == null) {
        break missingId;
      }

      id = R.id.nav_view;
      NavigationView navView = ViewBindings.findChildViewById(rootView, id);
      if (navView == null) {
        break missingId;
      }

      id = R.id.topBar;
      LinearLayout topBar = ViewBindings.findChildViewById(rootView, id);
      if (topBar == null) {
        break missingId;
      }

      id = R.id.tvLanguageAr;
      TextView tvLanguageAr = ViewBindings.findChildViewById(rootView, id);
      if (tvLanguageAr == null) {
        break missingId;
      }

      id = R.id.tvLanguageEn;
      TextView tvLanguageEn = ViewBindings.findChildViewById(rootView, id);
      if (tvLanguageEn == null) {
        break missingId;
      }

      return new ActivityMainBinding((DrawerLayout) rootView, adSection, adView, btnMoreApps,
          btnQuranListen, btnQuranSuras, buttonsContainer, drawerLayout, languageToggleContainer,
          mainTitle, menuIcon, navView, topBar, tvLanguageAr, tvLanguageEn);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.qurany2019.quranyapp.database;

import android.content.Context;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.qurany2019.quranyapp.database.entities.AzkarEntity;
import com.qurany2019.quranyapp.database.entities.AzkarProgressEntity;
import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;
import com.qurany2019.quranyapp.database.entities.AchievementEntity;
import com.qurany2019.quranyapp.database.dao.AzkarDao;
import com.qurany2019.quranyapp.database.dao.AzkarProgressDao;
import com.qurany2019.quranyapp.database.dao.AzkarStatisticsDao;
import com.qurany2019.quranyapp.database.dao.AchievementDao;

@Database(
    entities = {
        AzkarEntity.class,
        AzkarProgressEntity.class,
        AzkarStatisticsEntity.class,
        AchievementEntity.class
    },
    version = 1,
    exportSchema = false
)
public abstract class AzkarDatabase extends RoomDatabase {

    // Database name
    private static final String DATABASE_NAME = "azkar_database";

    // Singleton instance
    private static volatile AzkarDatabase INSTANCE;

    // Abstract methods to get DAOs
    public abstract AzkarDao azkarDao();
    public abstract AzkarProgressDao azkarProgressDao();
    public abstract AzkarStatisticsDao azkarStatisticsDao();
    public abstract AchievementDao achievementDao();

    // Get database instance (Singleton pattern)
    public static AzkarDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AzkarDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            AzkarDatabase.class,
                            DATABASE_NAME
                    )
                    .addCallback(roomCallback)
                    .fallbackToDestructiveMigration() // For development only
                    .build();
                }
            }
        }
        return INSTANCE;
    }

    // Database callback for initial setup
    private static RoomDatabase.Callback roomCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(SupportSQLiteDatabase db) {
            super.onCreate(db);
            // Database created for the first time
            // We can populate initial data here if needed
        }

        @Override
        public void onOpen(SupportSQLiteDatabase db) {
            super.onOpen(db);
            // Database opened
        }
    };

    // Migration from version 1 to 2 (example for future use)
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Add migration logic here when needed
            // Example: database.execSQL("ALTER TABLE azkar_table ADD COLUMN new_column TEXT");
        }
    };

    // Close database
    public static void closeDatabase() {
        if (INSTANCE != null && INSTANCE.isOpen()) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }

    // Clear all data (for testing or reset purposes)
    public void clearAllData() {
        azkarDao().deleteAllAzkar();
        azkarProgressDao().deleteAllProgress();
        azkarStatisticsDao().deleteAllStatistics();
        achievementDao().deleteAllAchievements();
    }

    // Database health check
    public boolean isDatabaseHealthy() {
        try {
            // Simple query to check if database is accessible
            azkarDao().getAzkarCount();
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // Get database size information
    public long getDatabaseSize(Context context) {
        try {
            return context.getDatabasePath(DATABASE_NAME).length();
        } catch (Exception e) {
            return 0;
        }
    }

    // Vacuum database (optimize storage)
    public void vacuumDatabase() {
        try {
            getOpenHelper().getWritableDatabase().execSQL("VACUUM");
        } catch (Exception e) {
            // Handle error
        }
    }

    // Check if database exists
    public static boolean databaseExists(Context context) {
        return context.getDatabasePath(DATABASE_NAME).exists();
    }

    // Get database version
    public int getDatabaseVersion() {
        return getOpenHelper().getReadableDatabase().getVersion();
    }

    // Database statistics
    public DatabaseStats getDatabaseStats() {
        DatabaseStats stats = new DatabaseStats();
        try {
            stats.totalAzkar = azkarDao().getAzkarCount();
            stats.totalProgress = azkarProgressDao().getProgressCountByDate(getCurrentDate());
            stats.totalAchievements = achievementDao().getTotalAchievementsCount();
            stats.unlockedAchievements = achievementDao().getUnlockedAchievementsCount();
        } catch (Exception e) {
            // Handle error
        }
        return stats;
    }

    // Helper method to get current date
    private String getCurrentDate() {
        return java.text.DateFormat.getDateInstance().format(new java.util.Date());
    }

    // Inner class for database statistics
    public static class DatabaseStats {
        public int totalAzkar = 0;
        public int totalProgress = 0;
        public int totalAchievements = 0;
        public int unlockedAchievements = 0;

        @Override
        public String toString() {
            return "DatabaseStats{" +
                    "totalAzkar=" + totalAzkar +
                    ", totalProgress=" + totalProgress +
                    ", totalAchievements=" + totalAchievements +
                    ", unlockedAchievements=" + unlockedAchievements +
                    '}';
        }
    }
}

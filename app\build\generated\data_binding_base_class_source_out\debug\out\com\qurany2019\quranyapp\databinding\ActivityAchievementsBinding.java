// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.tabs.TabLayout;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAchievementsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ProgressBar achievementProgressBar;

  @NonNull
  public final TabLayout achievementTabs;

  @NonNull
  public final RecyclerView achievementsRecyclerView;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView totalAchievements;

  @NonNull
  public final TextView totalPoints;

  @NonNull
  public final TextView unlockedAchievements;

  private ActivityAchievementsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull ProgressBar achievementProgressBar, @NonNull TabLayout achievementTabs,
      @NonNull RecyclerView achievementsRecyclerView, @NonNull TextView progressText,
      @NonNull Toolbar toolbar, @NonNull TextView totalAchievements, @NonNull TextView totalPoints,
      @NonNull TextView unlockedAchievements) {
    this.rootView = rootView;
    this.achievementProgressBar = achievementProgressBar;
    this.achievementTabs = achievementTabs;
    this.achievementsRecyclerView = achievementsRecyclerView;
    this.progressText = progressText;
    this.toolbar = toolbar;
    this.totalAchievements = totalAchievements;
    this.totalPoints = totalPoints;
    this.unlockedAchievements = unlockedAchievements;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAchievementsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAchievementsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_achievements, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAchievementsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.achievementProgressBar;
      ProgressBar achievementProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (achievementProgressBar == null) {
        break missingId;
      }

      id = R.id.achievementTabs;
      TabLayout achievementTabs = ViewBindings.findChildViewById(rootView, id);
      if (achievementTabs == null) {
        break missingId;
      }

      id = R.id.achievementsRecyclerView;
      RecyclerView achievementsRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (achievementsRecyclerView == null) {
        break missingId;
      }

      id = R.id.progressText;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.totalAchievements;
      TextView totalAchievements = ViewBindings.findChildViewById(rootView, id);
      if (totalAchievements == null) {
        break missingId;
      }

      id = R.id.totalPoints;
      TextView totalPoints = ViewBindings.findChildViewById(rootView, id);
      if (totalPoints == null) {
        break missingId;
      }

      id = R.id.unlockedAchievements;
      TextView unlockedAchievements = ViewBindings.findChildViewById(rootView, id);
      if (unlockedAchievements == null) {
        break missingId;
      }

      return new ActivityAchievementsBinding((CoordinatorLayout) rootView, achievementProgressBar,
          achievementTabs, achievementsRecyclerView, progressText, toolbar, totalAchievements,
          totalPoints, unlockedAchievements);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

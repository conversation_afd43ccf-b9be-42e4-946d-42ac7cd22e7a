package com.qurany2019.quranyapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * BroadcastReceiver لاستقبال أوامر الوسائط من الإشعارات
 */
public class MediaActionReceiver extends BroadcastReceiver {
    
    private static final String TAG = "MediaActionReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        try {
            Log.d(TAG, "تم استقبال broadcast");
            
            if (intent != null && "com.qurany2019.quranyapp.MEDIA_ACTION".equals(intent.getAction())) {
                String action = intent.getStringExtra("action");
                Log.d(TAG, "الأكشن المستقبل: " + action);
                
                if (action != null) {
                    // إرسال broadcast للتطبيق الرئيسي
                    Intent appIntent = new Intent("com.qurany2019.quranyapp.MEDIA_CONTROL");
                    appIntent.putExtra("media_action", action);
                    context.sendBroadcast(appIntent);
                    Log.d(TAG, "تم إرسال الأكشن للتطبيق الرئيسي: " + action);
                } else {
                    Log.e(TAG, "الأكشن فارغ!");
                }
            } else {
                Log.e(TAG, "Intent غير صحيح أو Action خطأ");
            }
        } catch (Exception e) {
            Log.e(TAG, "خطأ في معالجة الـ broadcast: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

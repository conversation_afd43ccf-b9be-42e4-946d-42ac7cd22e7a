<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/islamic_green_dark" />
            <corners android:radius="20dp" />
            <stroke android:width="1dp" android:color="@color/islamic_green" />
        </shape>
    </item>
    
    <!-- حالة التعطيل -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/islamic_gray" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/islamic_green" />
            <corners android:radius="20dp" />
            <stroke android:width="1dp" android:color="@color/islamic_green_dark" />
        </shape>
    </item>
    
</selector>

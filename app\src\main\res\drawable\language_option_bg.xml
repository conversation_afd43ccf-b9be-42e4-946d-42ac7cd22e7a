<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- حالة الضغط -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="#F0F8FF" />
            <stroke android:width="2dp" android:color="#4CAF50" />
        </shape>
    </item>
    
    <!-- الحالة العادية -->
    <item>
        <shape android:shape="rectangle">
            <corners android:radius="12dp" />
            <solid android:color="#FAFAFA" />
            <stroke android:width="1dp" android:color="#E0E0E0" />
        </shape>
    </item>
</selector>

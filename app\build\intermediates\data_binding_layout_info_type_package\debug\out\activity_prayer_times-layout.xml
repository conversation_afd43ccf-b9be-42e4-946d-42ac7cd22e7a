<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_prayer_times" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_prayer_times.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_prayer_times_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="552" endOffset="51"/></Target><Target id="@+id/app_bar" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="9" startOffset="4" endLine="22" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="21" endOffset="66"/></Target><Target id="@+id/tvCityName" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="71" endOffset="59"/></Target><Target id="@+id/tvCurrentDate" view="TextView"><Expressions/><location startLine="73" startOffset="20" endLine="81" endOffset="59"/></Target><Target id="@+id/tvCurrentTime" view="TextView"><Expressions/><location startLine="83" startOffset="20" endLine="91" endOffset="59"/></Target><Target id="@+id/tvNextPrayer" view="TextView"><Expressions/><location startLine="93" startOffset="20" endLine="101" endOffset="47"/></Target><Target id="@+id/cardFajr" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="118" startOffset="12" endLine="175" endOffset="47"/></Target><Target id="@+id/tvFajr" view="TextView"><Expressions/><location startLine="165" startOffset="20" endLine="172" endOffset="69"/></Target><Target id="@+id/cardSunrise" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="178" startOffset="12" endLine="235" endOffset="47"/></Target><Target id="@+id/tvSunrise" view="TextView"><Expressions/><location startLine="225" startOffset="20" endLine="232" endOffset="64"/></Target><Target id="@+id/cardDhuhr" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="238" startOffset="12" endLine="295" endOffset="47"/></Target><Target id="@+id/tvDhuhr" view="TextView"><Expressions/><location startLine="285" startOffset="20" endLine="292" endOffset="69"/></Target><Target id="@+id/cardAsr" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="298" startOffset="12" endLine="355" endOffset="47"/></Target><Target id="@+id/tvAsr" view="TextView"><Expressions/><location startLine="345" startOffset="20" endLine="352" endOffset="69"/></Target><Target id="@+id/cardMaghrib" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="358" startOffset="12" endLine="415" endOffset="47"/></Target><Target id="@+id/tvMaghrib" view="TextView"><Expressions/><location startLine="405" startOffset="20" endLine="412" endOffset="69"/></Target><Target id="@+id/cardIsha" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="418" startOffset="12" endLine="475" endOffset="47"/></Target><Target id="@+id/tvIsha" view="TextView"><Expressions/><location startLine="465" startOffset="20" endLine="472" endOffset="69"/></Target><Target id="@+id/switchNotifications" view="Switch"><Expressions/><location startLine="516" startOffset="24" endLine="520" endOffset="52"/></Target><Target id="@+id/tvNotificationInfo" view="TextView"><Expressions/><location startLine="525" startOffset="20" endLine="532" endOffset="56"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="544" startOffset="4" endLine="550" endOffset="57"/></Target></Targets></Layout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".MainActivity">

    <!-- الخلفية الرئيسية مع النمط الإسلامي -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/quran_main_background">

        <!-- شريط علوي مع أيقونة القائمة ومفتاح اللغة -->
        <LinearLayout
            android:id="@+id/topBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="16dp"
            android:layout_marginTop="24dp">

            <!-- أيقونة القائمة الجانبية -->
            <ImageView
                android:id="@+id/menuIcon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_menu"
                android:tint="@color/textPrimary"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                android:contentDescription="@string/menu_description"
                android:clickable="true"
                android:focusable="true" />

            <!-- مساحة فارغة لدفع مفتاح اللغة إلى اليمين -->
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- مفتاح تبديل اللغة -->
            <LinearLayout
                android:id="@+id/languageToggleContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/language_toggle_background"
                android:padding="8dp">

                <TextView
                    android:id="@+id/tvLanguageAr"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/ar"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:background="@drawable/language_button_selected"
                    android:padding="8dp"
                    android:layout_marginEnd="2dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center" />

                <TextView
                    android:id="@+id/tvLanguageEn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/en"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/textSecondary"
                    android:background="@drawable/language_button_unselected"
                    android:padding="8dp"
                    android:layout_marginStart="2dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

        <!-- العنوان الرئيسي "قرآني" -->
        <TextView
            android:id="@+id/mainTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/topBar"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="60dp"
            android:text="@string/main_title"
            android:textSize="64sp"
            android:textColor="@color/textPrimary"
            android:textStyle="bold"
            android:fontFamily="@font/noto"
            android:shadowColor="@color/shadowColor"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4" />

        <!-- الأزرار الرئيسية -->
        <LinearLayout
            android:id="@+id/buttonsContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/mainTitle"
            android:layout_marginTop="60dp"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="32dp"
            android:orientation="vertical"
            android:gravity="center">

            <!-- زر سور القرآن -->
            <LinearLayout
                android:id="@+id/btnQuranSuras"
                style="@style/QuranMainButtonContainer"
                android:layout_marginBottom="20dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_quran_read"
                    android:layout_marginEnd="16dp"
                    android:tint="@color/textPrimary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/btn_quran_suras"
                    android:textSize="20sp"
                    android:textColor="@color/textPrimary"
                    android:textStyle="bold"
                    android:fontFamily="@font/noto"
                    android:gravity="center" />

            </LinearLayout>

            <!-- زر الاستماع -->
            <LinearLayout
                android:id="@+id/btnQuranListen"
                style="@style/QuranMainButtonContainer"
                android:layout_marginBottom="20dp">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_quran_listen"
                    android:layout_marginEnd="16dp"
                    android:tint="@color/textPrimary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/btn_listen"
                    android:textSize="20sp"
                    android:textColor="@color/textPrimary"
                    android:textStyle="bold"
                    android:fontFamily="@font/noto"
                    android:gravity="center" />

            </LinearLayout>

            <!-- زر المزيد من التطبيقات -->
            <LinearLayout
                android:id="@+id/btnMoreApps"
                style="@style/QuranMainButtonContainer">

                <ImageView
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:src="@drawable/ic_menu"
                    android:layout_marginEnd="16dp"
                    android:tint="@color/textPrimary" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/btn_more_apps"
                    android:textSize="20sp"
                    android:textColor="@color/textPrimary"
                    android:textStyle="bold"
                    android:fontFamily="@font/noto"
                    android:gravity="center" />

            </LinearLayout>

        </LinearLayout>

        <!-- منطقة الإعلانات في الأسفل -->
        <LinearLayout
            android:id="@+id/adSection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="16dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical"
            android:gravity="center">

            <com.google.android.gms.ads.AdView
                android:id="@+id/adView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:adSize="BANNER"
                app:adUnitId="ca-app-pub-7841751633097845/8640371910" />

        </LinearLayout>

    </RelativeLayout>

    <!-- قائمة التنقل الجانبية -->
    <com.google.android.material.navigation.NavigationView
        android:id="@+id/nav_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start"
        android:fitsSystemWindows="false"
        android:background="@color/backgroundSecondary"
        app:headerLayout="@layout/nav_header_quran"
        app:menu="@menu/activity_quran_drawer"
        app:itemTextColor="@color/textPrimary"
        app:itemIconTint="@color/islamicGreenMedium" />

</androidx.drawerlayout.widget.DrawerLayout>

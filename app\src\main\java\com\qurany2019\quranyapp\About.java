package com.qurany2019.quranyapp;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import java.util.Locale;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;

public class About extends AppCompatActivity {

    private AdView mAdView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // إزالة fullscreen mode لإظهار شريط الأزرار بشكل طبيعي

        setContentView(R.layout.activity_about);

        // تهيئة التولبار
        androidx.appcompat.widget.Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("عن التطبيق");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // تهيئة إعلانات أد موب
        MobileAds.initialize(this, initializationStatus -> {});

        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        TextView appTitle = findViewById(R.id.app_title);
        TextView quranVerse = findViewById(R.id.quran_verse);
        TextView appVersionText = findViewById(R.id.app_version_text);

        // تحديد رقم الإصدار تلقائي
        setAppVersion(appVersionText);

        // إضافة حركة بسيطة للنصوص
        // addIconAnimation();

        animateTexts(appTitle, quranVerse);

        // إعداد أزرار سياسة الخصوصية والمزيد من التطبيقات
        setupPrivacyAndMoreAppsButtons();
    }

    // دالة لتحديد رقم الإصدار تلقائي
    private void setAppVersion(TextView appVersionText) {
        try {
            PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
            String versionName = packageInfo.versionName;
            appVersionText.setText(getString(R.string.version_prefix) + " " + versionName);
        } catch (PackageManager.NameNotFoundException e) {
            appVersionText.setText(getString(R.string.version_unknown));
        }
    }

    // إعداد أزرار سياسة الخصوصية والمزيد من التطبيقات
    private void setupPrivacyAndMoreAppsButtons() {
        try {
            // قسم سياسة الخصوصية
            CardView privacySection = findViewById(R.id.privacyPolicySection);
            if (privacySection != null) {
                privacySection.setOnClickListener(v -> {
                    try {
                        // فتح صفحة سياسة الخصوصية الفعلية
                        Intent privacyIntent = new Intent(this, PrivcyActivity.class);
                        startActivity(privacyIntent);
                        showToast("فتح سياسة الخصوصية...");
                    } catch (Exception e) {
                        showToast("لا يمكن فتح سياسة الخصوصية");
                    }
                });
            } else {
                showToast("لم يتم العثور على قسم سياسة الخصوصية");
            }

            // قسم المزيد من التطبيقات
            CardView moreAppsSection = findViewById(R.id.moreAppsSection);
            if (moreAppsSection != null) {
                moreAppsSection.setOnClickListener(v -> {
                    try {
                        // فتح صفحة المطور في Google Play
                        Intent storeIntent = new Intent(Intent.ACTION_VIEW);
                        storeIntent.setData(Uri.parse("https://play.google.com/store/apps/developer?id=AHED-OMN"));
                        startActivity(storeIntent);
                        showToast("فتح متجر التطبيقات...");
                    } catch (Exception e) {
                        showToast("لا يمكن فتح المتجر");
                    }
                });
            } else {
                showToast("لم يتم العثور على قسم المزيد من التطبيقات");
            }
        } catch (Exception e) {
            showToast("خطأ في إعداد الأزرار: " + e.getMessage());
        }
    }

    // دالة لعرض رسائل قصيرة
    private void showToast(String message) {
        try {
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    private void animateTexts(android.view.View appTitle, android.view.View quranVerse) {
        appTitle.setAlpha(0f);
        quranVerse.setAlpha(0f);

        appTitle.animate().alpha(1f).setDuration(1000).start();
        quranVerse.animate().alpha(1f).setDuration(1000).setStartDelay(500).start();
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}

package com.qurany2019.quranyapp;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.qurany2019.quranyapp.viewmodel.AzkarViewModel;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import java.util.ArrayList;
import java.util.List;

public class AzkarDetailActivity extends AppCompatActivity {
    private AdView mAdView;
    private RecyclerView azkarRecyclerView;
    private AzkarAdapter azkarAdapter;
    private String azkarType;
    private String azkarTitle;
    private AzkarViewModel viewModel;

    // النظام الشامل للتقدم والإشعارات - مبسط
    private SharedPreferences progressPrefs;
    private List<AzkarItem> currentAzkarList;
    private int completedCount = 0;
    private int totalAzkarCount = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_azkar_detail);

        // الحصول على البيانات من Intent
        azkarType = getIntent().getStringExtra("azkar_type");
        azkarTitle = getIntent().getStringExtra("azkar_title");

        // إعداد شريط الأدوات
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle(azkarTitle);

        // تهيئة ViewModel
        viewModel = new ViewModelProvider(this).get(AzkarViewModel.class);

        // تهيئة النظام الشامل
        initializeComprehensiveSystem();

        // إعداد RecyclerView
        setupRecyclerView();

        // إعداد الإعلانات
        setupAds();

        // تحميل الأذكار
        loadAzkar();
    }

    // تهيئة النظام الشامل للتقدم والإشعارات
    private void initializeComprehensiveSystem() {
        // إنشاء SharedPreferences للتقدم
        progressPrefs = getSharedPreferences("azkar_progress", Context.MODE_PRIVATE);

        // إعداد مستمع الأحداث للـ Adapter
        setupAdapterListener();
    }

    private void setupAdapterListener() {
        if (azkarAdapter != null) {
            azkarAdapter.setAzkarType(azkarType);
            azkarAdapter.setOnAzkarClickListener(new AzkarAdapter.OnAzkarClickListener() {
                @Override
                public void onAzkarCompleted(AzkarItem azkar) {
                    completedCount++;
                    checkSectionCompletion();
                }

                @Override
                public void onSectionCompleted(String sectionName) {
                    showSectionCompletedNotification(sectionName);
                    checkAllSectionsCompletion();
                }

                @Override
                public void onProgressChanged(int progress) {
                    updateProgressIndicators(progress);
                }
            });
        }
    }

    private void checkSectionCompletion() {
        if (currentAzkarList != null) {
            boolean sectionCompleted = true;
            for (AzkarItem azkar : currentAzkarList) {
                if (!azkar.isCompleted()) {
                    sectionCompleted = false;
                    break;
                }
            }
            if (sectionCompleted) {
                showSectionCompletedNotification(azkarTitle);
            }
        }
    }

    private void checkAllSectionsCompletion() {
        // فحص إذا كانت جميع الأقسام مكتملة
        if (completedCount >= totalAzkarCount) {
            showAllCompletedNotification();
        }
    }

    private void showSectionCompletedNotification(String sectionName) {
        String message = "مبارك! أكملت قسم " + sectionName + " بالكامل\nتقبل الله منك وبارك فيك";
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    private void showAllCompletedNotification() {
        String message = "تهانينا! أكملت جميع الأذكار\nجزاك الله خيراً وتقبل منك";
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    private void updateProgressIndicators(int progress) {
        // تحديث مؤشرات التقدم في الواجهة
        if (getSupportActionBar() != null) {
            String title = azkarTitle + " (" + progress + "%)";
            getSupportActionBar().setTitle(title);
        }
    }

    private void setupAds() {
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);
    }

    private void setupRecyclerView() {
        azkarRecyclerView = findViewById(R.id.azkarRecyclerView);
        azkarRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        azkarAdapter = new AzkarAdapter(this, new ArrayList<>());
        azkarRecyclerView.setAdapter(azkarAdapter);
    }

    private void loadAzkar() {
        List<AzkarItem> azkarList = new ArrayList<>();

        switch (azkarType) {
            case "morning":
                azkarList = getMorningAzkar();
                break;
            case "evening":
                azkarList = getEveningAzkar();
                break;
            case "sleep":
                azkarList = getSleepAzkar();
                break;
            case "wake":
                azkarList = getWakeAzkar();
                break;
            case "prayer":
                azkarList = getPrayerAzkar();
                break;
            default:
                azkarList = getMorningAzkar();
                break;
        }

        currentAzkarList = azkarList;
        totalAzkarCount = azkarList.size();

        azkarAdapter.updateAzkar(azkarList);
        setupAdapterListener();
    }

    private List<AzkarItem> getMorningAzkar() {
        List<AzkarItem> azkar = new ArrayList<>();
        
        azkar.add(new AzkarItem("أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ", 
            "اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ وَلَا يَئُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ", 
            1));
            
        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ", 
            "قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُنْ لَهُ كُفُوًا أَحَدٌ", 
            3));
            
        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ", 
            "قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ * مِنْ شَرِّ مَا خَلَقَ * وَمِنْ شَرِّ غَاسِقٍ إِذَا وَقَبَ * وَمِنْ شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ * وَمِنْ شَرِّ حَاسِدٍ إِذَا حَسَدَ", 
            3));
            
        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ", 
            "قُلْ أَعُوذُ بِرَبِّ النَّاسِ * مَلِكِ النَّاسِ * إِلَهِ النَّاسِ * مِنْ شَرِّ الْوَسْوَاسِ الْخَنَّاسِ * الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ * مِنَ الْجِنَّةِ وَالنَّاسِ", 
            3));
            
        azkar.add(new AzkarItem("", 
            "أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ، رَبِّ أَسْأَلُكَ خَيْرَ مَا فِي هَذَا الْيَوْمِ وَخَيْرَ مَا بَعْدَهُ، وَأَعُوذُ بِكَ مِنْ شَرِّ مَا فِي هَذَا الْيَوْمِ وَشَرِّ مَا بَعْدَهُ، رَبِّ أَعُوذُ بِكَ مِنَ الْكَسَلِ وَسُوءِ الْكِبَرِ، رَبِّ أَعُوذُ بِكَ مِنْ عَذَابٍ فِي النَّارِ وَعَذَابٍ فِي الْقَبْرِ", 
            1));
            
        azkar.add(new AzkarItem("", 
            "اللَّهُمَّ بِكَ أَصْبَحْنَا، وَبِكَ أَمْسَيْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ", 
            1));
            
        azkar.add(new AzkarItem("", 
            "اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ، أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ، أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ، وَأَبُوءُ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ", 
            1));
            
        azkar.add(new AzkarItem("", 
            "رَضِيتُ بِاللَّهِ رَبًّا، وَبِالْإِسْلَامِ دِينًا، وَبِمُحَمَّدٍ صَلَّى اللَّهُ عَلَيْهِ وَسَلَّمَ رَسُولًا", 
            3));
            
        azkar.add(new AzkarItem("", 
            "اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ، اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي دِينِي وَدُنْيَايَ وَأَهْلِي وَمَالِي، اللَّهُمَّ اسْتُرْ عَوْرَاتِي وَآمِنْ رَوْعَاتِي، اللَّهُمَّ احْفَظْنِي مِنْ بَيْنِ يَدَيَّ، وَمِنْ خَلْفِي، وَعَنْ يَمِينِي، وَعَنْ شِمَالِي، وَمِنْ فَوْقِي، وَأَعُوذُ بِعَظَمَتِكَ أَنْ أُغْتَالَ مِنْ تَحْتِي", 
            1));
            
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ عَالِمَ الْغَيْبِ وَالشَّهَادَةِ فَاطِرَ السَّمَاوَاتِ وَالْأَرْضِ، رَبَّ كُلِّ شَيْءٍ وَمَلِيكَهُ، أَشْهَدُ أَنْ لَا إِلَهَ إِلَّا أَنْتَ، أَعُوذُ بِكَ مِنْ شَرِّ نَفْسِي، وَمِنْ شَرِّ الشَّيْطَانِ وَشِرْكِهِ، وَأَنْ أَقْتَرِفَ عَلَى نَفْسِي سُوءًا أَوْ أَجُرَّهُ إِلَى مُسْلِمٍ",
            1));

        // أذكار إضافية مهمة
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ مَا أَصْبَحَ بِي مِنْ نِعْمَةٍ أَوْ بِأَحَدٍ مِنْ خَلْقِكَ، فَمِنْكَ وَحْدَكَ لَا شَرِيكَ لَكَ، فَلَكَ الْحَمْدُ وَلَكَ الشُّكْرُ",
            1));

        azkar.add(new AzkarItem("",
            "حَسْبِيَ اللَّهُ لَا إِلَهَ إِلَّا هُوَ عَلَيْهِ تَوَكَّلْتُ وَهُوَ رَبُّ الْعَرْشِ الْعَظِيمِ",
            7));

        azkar.add(new AzkarItem("",
            "بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ",
            3));

        azkar.add(new AzkarItem("",
            "اللَّهُمَّ عَافِنِي فِي بَدَنِي، اللَّهُمَّ عَافِنِي فِي سَمْعِي، اللَّهُمَّ عَافِنِي فِي بَصَرِي، لَا إِلَهَ إِلَّا أَنْتَ",
            3));

        azkar.add(new AzkarItem("",
            "اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْكُفْرِ، وَالْفَقْرِ، وَأَعُوذُ بِكَ مِنْ عَذَابِ الْقَبْرِ، لَا إِلَهَ إِلَّا أَنْتَ",
            3));

        azkar.add(new AzkarItem("",
            "يَا حَيُّ يَا قَيُّومُ بِرَحْمَتِكَ أَسْتَغِيثُ أَصْلِحْ لِي شَأْنِي كُلَّهُ وَلَا تَكِلْنِي إِلَى نَفْسِي طَرْفَةَ عَيْنٍ",
            3));

        azkar.add(new AzkarItem("",
            "أَصْبَحْنَا عَلَى فِطْرَةِ الْإِسْلَامِ، وَعَلَى كَلِمَةِ الْإِخْلَاصِ، وَعَلَى دِينِ نَبِيِّنَا مُحَمَّدٍ صَلَّى اللَّهُ عَلَيْهِ وَسَلَّمَ، وَعَلَى مِلَّةِ أَبِينَا إِبْرَاهِيمَ حَنِيفًا مُسْلِمًا وَمَا كَانَ مِنَ الْمُشْرِكِينَ",
            1));

        azkar.add(new AzkarItem("",
            "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ عَدَدَ خَلْقِهِ، وَرِضَا نَفْسِهِ، وَزِنَةَ عَرْشِهِ، وَمِدَادَ كَلِمَاتِهِ",
            3));

        azkar.add(new AzkarItem("",
            "اللَّهُمَّ صَلِّ وَسَلِّمْ وَبَارِكْ عَلَى نَبِيِّنَا مُحَمَّدٍ",
            10));

        azkar.add(new AzkarItem("",
            "لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ",
            100));

        azkar.add(new AzkarItem("",
            "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ",
            100));

        azkar.add(new AzkarItem("",
            "أَسْتَغْفِرُ اللَّهَ وَأَتُوبُ إِلَيْهِ",
            100));

        return azkar;
    }

    @Override
    public boolean onSupportNavigateUp() {
        getOnBackPressedDispatcher().onBackPressed();
        return true;
    }

    private List<AzkarItem> getEveningAzkar() {
        List<AzkarItem> azkar = new ArrayList<>();

        azkar.add(new AzkarItem("أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ",
            "اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ وَلَا يَئُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ",
            1));

        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ",
            "قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُنْ لَهُ كُفُوًا أَحَدٌ",
            3));

        azkar.add(new AzkarItem("",
            "أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ، رَبِّ أَسْأَلُكَ خَيْرَ مَا فِي هَذِهِ اللَّيْلَةِ وَخَيْرَ مَا بَعْدَهَا، وَأَعُوذُ بِكَ مِنْ شَرِّ مَا فِي هَذِهِ اللَّيْلَةِ وَشَرِّ مَا بَعْدَهَا، رَبِّ أَعُوذُ بِكَ مِنَ الْكَسَلِ وَسُوءِ الْكِبَرِ، رَبِّ أَعُوذُ بِكَ مِنْ عَذَابٍ فِي النَّارِ وَعَذَابٍ فِي الْقَبْرِ",
            1));

        azkar.add(new AzkarItem("",
            "اللَّهُمَّ بِكَ أَمْسَيْنَا، وَبِكَ أَصْبَحْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ الْمَصِيرُ",
            1));

        // سيد الاستغفار
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ، أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ، أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ وَأَبُوءُ بِذَنْبِي فَاغْفِرْ لِي فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ",
            1));

        // الرضا بالله رباً
        azkar.add(new AzkarItem("",
            "رَضِيتُ بِاللَّهِ رَبًّا وَبِالْإِسْلَامِ دِينًا وَبِمُحَمَّدٍ صلى الله عليه وسلم نَبِيًّا",
            3));

        // دعاء العفو والعافية
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ، اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي دِينِي وَدُنْيَايَ وَأَهْلِي وَمَالِي، اللَّهُمَّ اسْتُرْ عَوْرَاتِي وَآمِنْ رَوْعَاتِي، اللَّهُمَّ احْفَظْنِي مِنْ بَيْنِ يَدَيَّ، وَمِنْ خَلْفِي، وَعَنْ يَمِينِي، وَعَنْ شِمَالِي، وَمِنْ فَوْقِي، وَأَعُوذُ بِعَظَمَتِكَ أَنْ أُغْتَالَ مِنْ تَحْتِي",
            1));

        // حسبي الله
        azkar.add(new AzkarItem("",
            "حَسْبِيَ اللَّهُ لَا إِلَهَ إِلَّا هُوَ عَلَيْهِ تَوَكَّلْتُ وَهُوَ رَبُّ الْعَرْشِ الْعَظِيمِ",
            7));

        // بسم الله الذي لا يضر
        azkar.add(new AzkarItem("",
            "بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ",
            3));

        // الصلاة على النبي
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ صَلِّ وَسَلِّمْ وَبَارِكْ عَلَى نَبِيِّنَا مُحَمَّدٍ",
            10));

        // التهليل
        azkar.add(new AzkarItem("",
            "لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ",
            100));

        // التسبيح
        azkar.add(new AzkarItem("",
            "سُبْحَانَ اللَّهِ وَبِحَمْدِهِ",
            100));

        // الاستغفار
        azkar.add(new AzkarItem("",
            "أَسْتَغْفِرُ اللَّهَ وَأَتُوبُ إِلَيْهِ",
            100));

        return azkar;
    }

    private List<AzkarItem> getSleepAzkar() {
        List<AzkarItem> azkar = new ArrayList<>();

        // دعاء النوم الأساسي
        azkar.add(new AzkarItem("",
            "بِاسْمِكَ رَبِّي وَضَعْتُ جَنْبِي، وَبِكَ أَرْفَعُهُ، فَإِنْ أَمْسَكْتَ نَفْسِي فَارْحَمْهَا، وَإِنْ أَرْسَلْتَهَا فَاحْفَظْهَا بِمَا تَحْفَظُ بِهِ عِبَادَكَ الصَّالِحِينَ",
            1));

        // دعاء الموت والحياة
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ إِنَّكَ خَلَقْتَ نَفْسِي وَأَنْتَ تَوَفَّاهَا، لَكَ مَمَاتُهَا وَمَحْيَاهَا، إِنْ أَحْيَيْتَهَا فَاحْفَظْهَا، وَإِنْ أَمَتَّهَا فَاغْفِرْ لَهَا، اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَافِيَةَ",
            1));

        // آية الكرسي
        azkar.add(new AzkarItem("أَعُوذُ بِاللَّهِ مِنَ الشَّيْطَانِ الرَّجِيمِ",
            "اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِمَا شَاءَ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ وَلَا يَئُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ",
            1));

        // المعوذات
        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ",
            "قُلْ هُوَ اللَّهُ أَحَدٌ * اللَّهُ الصَّمَدُ * لَمْ يَلِدْ وَلَمْ يُولَدْ * وَلَمْ يَكُنْ لَهُ كُفُوًا أَحَدٌ",
            3));

        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ",
            "قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ * مِنْ شَرِّ مَا خَلَقَ * وَمِنْ شَرِّ غَاسِقٍ إِذَا وَقَبَ * وَمِنْ شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ * وَمِنْ شَرِّ حَاسِدٍ إِذَا حَسَدَ",
            3));

        azkar.add(new AzkarItem("بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ",
            "قُلْ أَعُوذُ بِرَبِّ النَّاسِ * مَلِكِ النَّاسِ * إِلَهِ النَّاسِ * مِنْ شَرِّ الْوَسْوَاسِ الْخَنَّاسِ * الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ * مِنَ الْجِنَّةِ وَالنَّاسِ",
            3));

        // التسبيح قبل النوم
        azkar.add(new AzkarItem("",
            "سُبْحَانَ اللَّهِ",
            33));

        azkar.add(new AzkarItem("",
            "الْحَمْدُ لِلَّهِ",
            33));

        azkar.add(new AzkarItem("",
            "اللَّهُ أَكْبَرُ",
            34));

        return azkar;
    }

    private List<AzkarItem> getWakeAzkar() {
        List<AzkarItem> azkar = new ArrayList<>();

        // دعاء الاستيقاظ الأساسي
        azkar.add(new AzkarItem("",
            "الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ",
            1));

        // دعاء العافية
        azkar.add(new AzkarItem("",
            "الْحَمْدُ لِلَّهِ الَّذِي عَافَانِي فِي جَسَدِي، وَرَدَّ عَلَيَّ رُوحِي، وَأَذِنَ لِي بِذِكْرِهِ",
            1));

        // لا إله إلا الله
        azkar.add(new AzkarItem("",
            "لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ، الْحَمْدُ لِلَّهِ، وَسُبْحَانَ اللَّهِ، وَلَا إِلَهَ إِلَّا اللَّهُ، وَاللَّهُ أَكْبَرُ، وَلَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ",
            1));

        // دعاء الاستيقاظ من النوم
        azkar.add(new AzkarItem("",
            "اللَّهُمَّ إِنِّي أَسْأَلُكَ مِنْ فَضْلِكَ وَرَحْمَتِكَ، فَإِنَّهُ لَا يَمْلِكُهَا إِلَّا أَنْتَ",
            1));

        return azkar;
    }

    private List<AzkarItem> getPrayerAzkar() {
        List<AzkarItem> azkar = new ArrayList<>();

        azkar.add(new AzkarItem("",
            "أَسْتَغْفِرُ اللَّهَ",
            3));

        azkar.add(new AzkarItem("",
            "اللَّهُمَّ أَنْتَ السَّلَامُ وَمِنْكَ السَّلَامُ، تَبَارَكْتَ يَا ذَا الْجَلَالِ وَالْإِكْرَامِ",
            1));

        azkar.add(new AzkarItem("",
            "لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ، اللَّهُمَّ لَا مَانِعَ لِمَا أَعْطَيْتَ، وَلَا مُعْطِيَ لِمَا مَنَعْتَ، وَلَا يَنْفَعُ ذَا الْجَدِّ مِنْكَ الْجَدُّ",
            1));

        azkar.add(new AzkarItem("",
            "سُبْحَانَ اللَّهِ",
            33));

        azkar.add(new AzkarItem("",
            "الْحَمْدُ لِلَّهِ",
            33));

        azkar.add(new AzkarItem("",
            "اللَّهُ أَكْبَرُ",
            34));

        return azkar;
    }
}

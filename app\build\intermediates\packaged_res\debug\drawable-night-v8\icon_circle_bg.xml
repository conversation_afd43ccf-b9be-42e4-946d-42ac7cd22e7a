<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- حالة الضغط في الوضع الليلي -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <gradient
                android:startColor="#FFEB3B"
                android:endColor="#FFA000"
                android:angle="135"
                android:type="linear" />
            <stroke
                android:width="2dp"
                android:color="#FF6F00" />
        </shape>
    </item>

    <!-- الحالة العادية في الوضع الليلي -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:startColor="#FFD700"
                android:endColor="#FF9800"
                android:angle="135"
                android:type="linear" />
            <stroke
                android:width="1dp"
                android:color="#FFCC02" />
        </shape>
    </item>

</selector>

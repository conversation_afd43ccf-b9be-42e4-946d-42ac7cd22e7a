package com.qurany2019.quranyapp;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import com.airbnb.lottie.LottieAnimationView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.MobileAds;
import android.media.MediaPlayer;
import android.util.Log;
import android.media.AudioManager;
import android.os.Bundle;
import android.os.Handler;
import androidx.appcompat.app.AppCompatActivity;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Toast;
import android.widget.ImageView;
import android.view.View;



import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Random;
/**
 * Created by java_dude on 06/06/18.
 */
public class managerdb extends AppCompatActivity implements MediaPlayer.OnCompletionListener, SeekBar.OnSeekBarChangeListener {
    private AdView mAdView;
    private ImageButton btnPlay;
    private ImageButton btnForward;
    private ImageButton btnBackward;
    private ImageButton btnNext;
    private ImageButton btnPrevious;


    private ImageButton btnRepeat;
    private ImageButton btnShuffle;
    private SeekBar songProgressBar;
    private TextView songTitleLabel;
    private TextView songCurrentDurationLabel;
    private TextView songTotalDurationLabel;
    private LinearLayout layoutads;
    // Media Player
    private static MediaPlayer mp ;
    // Handler to update UI timer, progress bar etc,.
    private Handler mHandler = new Handler();
    ;
    private SongsManager songManager;
    private Utilities utils;
    private int seekForwardTime = 5000; // 5000 milliseconds
    private int seekBackwardTime = 5000; // 5000 milliseconds
    private int currentSongIndex = 0;
    private boolean isShuffle = false;
    private boolean isRepeat = false;
    String RecitesName="";
    String RecitesAYA="";
    private ArrayList<HashMap<String, String>> songsList = new ArrayList<HashMap<String, String>>();

    // متغيرات الانيميشن
    private ImageView quranIcon;
    private ImageView outerGlow, middleGlow, innerGlow, decorativeStars;


    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_managerdb);
        // إزالة fullscreen mode لحل مشكلة قطع الصفحة
        // سنترك الصفحة تعمل بالوضع العادي مع شريط الحالة والتنقل

        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);
        Bundle b=getIntent().getExtras();
        RecitesName=b.getString("RecitesName");
        RecitesAYA=b.getString("RecitesAYA");

        // فحص إذا كان هناك ملف محلي
        String localFilePath = b.getString("LocalFilePath");
        boolean isLocalFile = b.getBoolean("IsLocalFile", false);



        // All player buttons
        btnPlay = (ImageButton) findViewById( R.id.btnPlay);
        btnForward = (ImageButton) findViewById( R.id.btnForward);
        btnBackward = (ImageButton) findViewById( R.id.btnBackward);
        btnNext = (ImageButton) findViewById( R.id.btnNext);
        btnPrevious = (ImageButton) findViewById( R.id.btnPrevious);
        layoutads=(LinearLayout)findViewById(R.id.layoutads);
        btnRepeat = (ImageButton) findViewById( R.id.btnRepeat);
        btnShuffle = (ImageButton) findViewById( R.id.btnShuffle);
        songProgressBar = (SeekBar) findViewById( R.id.songProgressBar);
        songTitleLabel = (TextView) findViewById( R.id.songTitle);
        songCurrentDurationLabel = (TextView) findViewById( R.id.songCurrentDurationLabel);
        songTotalDurationLabel = (TextView) findViewById( R.id.songTotalDurationLabel);

        // Mediaplayer
        mp = new MediaPlayer();
        // تعيين MediaPlayer في MediaPlayerManager
        MediaPlayerManager.setMediaPlayer(mp);
        // تعيين static references للتحكم المباشر
        currentInstance = this;
        staticMp = mp;
        songManager = new SongsManager();
        utils = new Utilities();

        // Listeners
        songProgressBar.setOnSeekBarChangeListener(this); // Important
        mp.setOnCompletionListener(this); // Important

        // Getting all songs list with context support for local files
        android.util.Log.d("MANAGER_DB", getString(R.string.loading_surahs_message));
        songsList = songManager.getPlayListWithContext(this, RecitesName);
        mAdView = (AdView) findViewById(R.id.adView);
        // إعداد الانيميشن
        setupAnimations();

        // معالجة أكشن إشعارات الوسائط
        handleMediaAction();

        // معالجة أوامر الإشعار البسيط
        handleSimpleMediaIntent(getIntent());

        // تسجيل BroadcastReceiver لأوامر الوسائط
        setupMediaBroadcastReceiver();
        // تسجيل BroadcastReceiver البسيط
        setupSimpleMediaReceiver();



        // By default play first song
        currentSongIndex=Integer.parseInt(  RecitesAYA);//-1 ;

        // فحص إذا كان هناك ملف محلي للتشغيل
        if (isLocalFile && localFilePath != null && !localFilePath.isEmpty()) {
            playLocalFile(localFilePath);
        } else {
            playSong(currentSongIndex);
        }

        /**
         * Play button click event
         * plays a song and changes button to pause image
         * pauses a song and changes button to play image
         * */
        btnPlay.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // check for already playing
                if(isMediaPlayerPlaying()){
                    if(mp!=null){
                        mp.pause();
                        // Changing button image to play button
                        btnPlay.setImageResource( R.drawable.ic_play_arrow_white);
                        // إيقاف انيميشن التشغيل
                        stopPlayingAnimations();
                        // إيقاف إشعار الوسائط
                        stopMediaNotification();
                    }
                }else{
                    // Resume song - لا نستدعي playSong هنا
                    if(mp!=null){
                        mp.start();
                        // Changing button image to pause button
                        btnPlay.setImageResource( R.drawable.ic_pause_white);
                        // تشغيل انيميشن التشغيل
                        startPlayingAnimations();
                        // بدء إشعار الوسائط البسيط الجديد
                        startSimpleMediaNotification();

                    }
                }

            }
        });

        /**
         * Forward button click event
         * Forwards song specified seconds
         * */
        btnForward.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // get current song position
                int currentPosition = mp.getCurrentPosition();
                // check if seekForward time is lesser than song duration
                if(currentPosition + seekForwardTime <= mp.getDuration()){
                    // forward song
                    mp.seekTo(currentPosition + seekForwardTime);
                }else{
                    // forward to end position
                    mp.seekTo(mp.getDuration());
                }
            }
        });

        /**
         * Backward button click event
         * Backward song to specified seconds
         * */
        btnBackward.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // get current song position
                int currentPosition = mp.getCurrentPosition();
                // check if seekBackward time is greater than 0 sec
                if(currentPosition - seekBackwardTime >= 0){
                    // forward song
                    mp.seekTo(currentPosition - seekBackwardTime);
                }else{
                    // backward to starting position
                    mp.seekTo(0);
                }

            }
        });

        /**
         * Next button click event
         * Plays next song by taking currentSongIndex + 1
         * */
        btnNext.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // check if next song is there or not
                if (currentSongIndex < (songsList.size() - 1)) {
                    playSong(currentSongIndex + 1);
                    currentSongIndex = currentSongIndex + 1;
                } else {
                    // إذا كانت آخر سورة، ارجع للأولى
                    playSong(0);
                    currentSongIndex = 0;
                }
            }
        });

        /**
         * Back button click event
         * Plays previous song by currentSongIndex - 1
         * */
        btnPrevious.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                if(currentSongIndex > 0){
                    playSong(currentSongIndex - 1);
                    currentSongIndex = currentSongIndex - 1;
                }else{
                    // play last song
                    playSong(songsList.size() - 1);
                    currentSongIndex = songsList.size() - 1;
                }

            }
        });

        /**
         * Button Click event for Repeat button
         * Enables repeat flag to true
         * */
        btnRepeat.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                // تأثير بصري عند الضغط
                btnRepeat.animate()
                    .scaleX(0.9f)
                    .scaleY(0.9f)
                    .setDuration(100)
                    .withEndAction(() -> {
                        btnRepeat.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(100)
                            .start();
                    })
                    .start();

                if(isRepeat){
                    isRepeat = false;
                    // عرض رسالة جميلة مع أيقونة
                    Toast.makeText(getApplicationContext(), "❌ " + getString(R.string.repeat_off), Toast.LENGTH_SHORT).show();
                    btnRepeat.setImageResource(R.drawable.btn_repeat);
                    btnRepeat.setAlpha(0.7f);
                }else{
                    // make repeat to true
                    isRepeat = true;
                    // عرض رسالة جميلة مع أيقونة
                    Toast.makeText(getApplicationContext(), "🔁 " + getString(R.string.repeat_on), Toast.LENGTH_SHORT).show();
                    // make shuffle to false
                    isShuffle = false;
                    btnRepeat.setImageResource(R.drawable.btn_repeat_focused);
                    btnRepeat.setAlpha(1.0f);
                    btnShuffle.setImageResource(R.drawable.btn_shuffle);
                    btnShuffle.setAlpha(0.7f);
                }
            }
        });

        /**
         * Button Click event for Shuffle button
         * Enables shuffle flag to true
         * */
        btnShuffle.setOnClickListener(new View.OnClickListener() {

            @Override
            public void onClick(View arg0) {
                if(isShuffle){
                    isShuffle = false;
                    Toast.makeText(getApplicationContext(), getString(R.string.shuffle_off), Toast.LENGTH_SHORT).show();
                    btnShuffle.setImageResource( R.drawable.btn_shuffle);
                }else{
                    // make repeat to true
                    isShuffle= true;
                    Toast.makeText(getApplicationContext(), getString(R.string.shuffle_on), Toast.LENGTH_SHORT).show();
                    // make shuffle to false
                    isRepeat = false;
                    btnShuffle.setImageResource( R.drawable.btn_shuffle_focused);
                    btnRepeat.setImageResource( R.drawable.btn_repeat);
                }
            }
        });

        /**
         * Button Click event for Play list click event
         * Launches list activity which displays list of songs
         * */


    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_managerdb, menu);


        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();

        if (id == R.id.gbackmenu) {
          this.finish();
            }



        return super.onOptionsItemSelected(item);
    }


    // @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK )
        {
            if(isMediaPlayerPlaying())
                if(mp!=null)
                    mp.pause();

           this.finish();
        }

        return super.onKeyDown(keyCode, event);
    }
    /**
     * Receiving song index from playlist view
     * and play the song
     * */
    @Override
    protected void onActivityResult(int requestCode,
                                    int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(resultCode == 100){
            currentSongIndex = data.getExtras().getInt("songIndex");
            // play selected song
            playSong(currentSongIndex);
        }

    }

    /**
     * Function to play a song
     * @param songIndex - index of song
     * */
public void playSong(int songIndex) {
    try {
        android.util.Log.d("PLAY_SONG", "🎯 بدء تشغيل السورة رقم: " + songIndex);
        android.util.Log.d("PLAY_SONG", "📊 حجم القائمة: " + songsList.size());

        // التحقق من صحة الفهرس
        if (songIndex < 0 || songIndex >= songsList.size()) {
            android.util.Log.e("PLAY_SONG_ERROR", "❌ فهرس خاطئ: " + songIndex + " (الحجم: " + songsList.size() + ")");
            android.widget.Toast.makeText(this, "❌ خطأ في رقم السورة", android.widget.Toast.LENGTH_SHORT).show();
            return;
        }

        String path = songsList.get(songIndex).get("songPath");
        android.util.Log.d("PLAY_SONG", "الرابط الصوتي: " + path);

        // 🔧 تطبيق نظام Cache الذكي للملفات المحلية
        if (!path.startsWith("http://") && !path.startsWith("https://")) {
            android.util.Log.d("PLAY_SONG", "📁 ملف محلي - فحص ذكي");

            // استخراج رقم السورة من المسار
            String surahCode = extractSurahCodeFromPath(path);
            if (surahCode != null) {
                String smartPath = findLocalFileSmartly(RecitesName, surahCode);
                if (smartPath != null) {
                    path = smartPath;
                    android.util.Log.d("PLAY_SONG", "✅ تم العثور على الملف بالبحث الذكي: " + path);
                } else {
                    android.util.Log.d("PLAY_SONG", "❌ لم يتم العثور على الملف محلياً - سيتم التشغيل من الإنترنت");
                }
            }
        }

        // 🔧 تطبيق نظام Cache الذكي للملفات المحلية
        if (!path.startsWith("http://") && !path.startsWith("https://")) {
            android.util.Log.d("PLAY_SONG", "📁 ملف محلي - فحص ذكي");

            // استخراج رقم السورة من المسار
            String surahCode = extractSurahCodeFromPath(path);
            if (surahCode != null) {
                String smartPath = findLocalFileSmartly(RecitesName, surahCode);
                if (smartPath != null) {
                    path = smartPath;
                    android.util.Log.d("PLAY_SONG", "✅ تم العثور على الملف بالبحث الذكي: " + path);
                } else {
                    android.util.Log.d("PLAY_SONG", "❌ لم يتم العثور على الملف محلياً - سيتم التشغيل من الإنترنت");
                }
            }
        }

        // فحص نوع الرابط
        if (path.startsWith("http://") || path.startsWith("https://")) {
            android.util.Log.d("PLAY_SONG", "🌐 رابط إنترنت - يحتاج اتصال");
        } else {
            android.util.Log.d("PLAY_SONG", "📁 ملف محلي");
            // فحص وجود الملف
            java.io.File file = new java.io.File(path);
            android.util.Log.d("PLAY_SONG", "   ✅ موجود: " + file.exists());
            if (file.exists()) {
                android.util.Log.d("PLAY_SONG", "   📏 الحجم: " + file.length() + " بايت");
            }
        }

        mp.reset();
        android.util.Log.d("PLAY_SONG", "تم إعادة تعيين MediaPlayer");

        mp.setAudioStreamType(android.media.AudioManager.STREAM_MUSIC);
        android.util.Log.d("PLAY_SONG", "تم تعيين نوع الصوت");

        mp.setDataSource(path);
        android.util.Log.d("PLAY_SONG", "تم تعيين مصدر البيانات");
        mp.setOnPreparedListener(mediaPlayer -> {
    mediaPlayer.start();
    btnPlay.setImageResource(R.drawable.img_btn_pause); // ← تغيير شكل الزر
    // تشغيل انيميشن التشغيل
    startPlayingAnimations();
    // بدء إشعار الوسائط البسيط الجديد
    startSimpleMediaNotification();

});
        final String finalPath = path; // متغير final للاستخدام في lambda
        mp.setOnErrorListener((mediaPlayer, what, extra) -> {
            android.util.Log.e("MEDIA_ERROR", "خطأ في MediaPlayer - what: " + what + ", extra: " + extra);
            android.util.Log.e("MEDIA_ERROR", "المسار: " + finalPath);

            runOnUiThread(() -> {
                android.widget.Toast.makeText(this, "❌ خطأ في تشغيل الملف الصوتي", android.widget.Toast.LENGTH_LONG).show();
                btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                stopPlayingAnimations();
            });
            return true;
        });

        mp.prepareAsync();

        songTitleLabel.setText(songsList.get(songIndex).get("songTitle"));
        songProgressBar.setProgress(0);
        songProgressBar.setMax(100);
        updateProgressBar();
        btnPlay.setImageResource(R.drawable.ic_pause_white);
        currentSongIndex = songIndex;
    } catch (Exception e) {
        android.util.Log.e("PLAY_SONG_ERROR", "خطأ في playSong: " + e.getMessage());
        android.widget.Toast.makeText(this, "❌ خطأ في تشغيل السورة: " + e.getMessage(), android.widget.Toast.LENGTH_LONG).show();
        e.printStackTrace();
    }
}

// دالة تشغيل الملف المحلي
public void playLocalFile(String localPath) {
    try {
        QuranLogger.info(QuranLogger.STORAGE, "🎯 بدء تشغيل ملف محلي: " + localPath);

        // فحص تفصيلي للملف
        QuranLogger.logFileStatus(localPath);

        // التأكد من وجود الملف
        java.io.File file = new java.io.File(localPath);
        if (!file.exists()) {
            QuranLogger.error(QuranLogger.FILE_SYSTEM, "الملف غير موجود: " + localPath);
            android.widget.Toast.makeText(this, "❌ الملف غير موجود", android.widget.Toast.LENGTH_SHORT).show();
            return;
        }

        QuranLogger.info(QuranLogger.FILE_SYSTEM, "✅ الملف موجود - الحجم: " + file.length() + " بايت");

        mp.reset();
        mp.setAudioStreamType(android.media.AudioManager.STREAM_MUSIC);
        mp.setDataSource(localPath);
        mp.setOnPreparedListener(mediaPlayer -> {
            mediaPlayer.start();
            btnPlay.setImageResource(R.drawable.ic_pause_white);
            // تشغيل انيميشن التشغيل
            startPlayingAnimations();
            // بدء إشعار الوسائط البسيط
            startSimpleMediaNotification();

            android.widget.Toast.makeText(managerdb.this, "🎵 تشغيل من التخزين المحلي", android.widget.Toast.LENGTH_SHORT).show();
        });
        mp.prepareAsync();

        // عرض اسم السورة الصحيح
        String surahName = getSurahName(Integer.parseInt(RecitesAYA));
        if (surahName != null && !surahName.isEmpty()) {
            songTitleLabel.setText(surahName);
        } else {
            // في حالة عدم وجود اسم السورة، استخدم الطريقة القديمة
            songTitleLabel.setText(RecitesAYA + " - " + RecitesName);
        }
        songProgressBar.setProgress(0);
        songProgressBar.setMax(100);
        updateProgressBar();

    } catch (Exception e) {
        android.widget.Toast.makeText(this, "❌ خطأ في تشغيل الملف المحلي: " + e.getMessage(), android.widget.Toast.LENGTH_LONG).show();
        e.printStackTrace();
    }
}

// دالة للحصول على اسم السورة من الفهرس
private String getSurahName(int index) {
    try {
        if (songsList != null && index >= 0 && index < songsList.size()) {
            return songsList.get(index).get("songTitle");
        }
    } catch (Exception e) {
        e.printStackTrace();
    }
    return null;
}

    /**
     * Update timer on seekbar
     * */
    public void updateProgressBar() {
        mHandler.postDelayed(mUpdateTimeTask, 100);
    }

    /**
     * Background Runnable thread
     * */

private Runnable mUpdateTimeTask = new Runnable() {
    public void run() {
        try {
            long totalDuration = 0;
            long currentDuration = 0;

            if (mp != null && isMediaPlayerPlaying()) {
                totalDuration = mp.getDuration();
                currentDuration = mp.getCurrentPosition();

                songTotalDurationLabel.setText("" + utils.milliSecondsToTimer(totalDuration));
                songCurrentDurationLabel.setText("" + utils.milliSecondsToTimer(currentDuration));
            }

            int progress = (int)(utils.getProgressPercentage(currentDuration, totalDuration));
            songProgressBar.setProgress(progress);

            if(currentDuration >= (totalDuration / 8)){
                // من الممكن يكون لديك كود هنا (لم يتم نسخه في النسخة السابقة)
            }

            mHandler.postDelayed(this, 100);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
};


    /**
     *
     * */
    @Override
    public void onProgressChanged(SeekBar seekBar, int progress, boolean fromTouch) {

    }

    /**
     * When user starts moving the progress handler
     * */
    @Override
    public void onStartTrackingTouch(SeekBar seekBar) {
        // remove message Handler from updating progress bar
        mHandler.removeCallbacks(mUpdateTimeTask);
    }

    /**
     * When user stops moving the progress hanlder
     * */
    @Override
    public void onStopTrackingTouch(SeekBar seekBar) {
        mHandler.removeCallbacks(mUpdateTimeTask);
        if (mp != null && isMediaPlayerPlaying()) {
            int totalDuration = mp.getDuration();
        int currentPosition = utils.progressToTimer(seekBar.getProgress(), totalDuration);

        // forward or backward to certain seconds
                    mp.seekTo(currentPosition);
        }

        // update timer progress again
        updateProgressBar();
    }

    /**
     * On Song Playing completed
     * if repeat is ON play same song again
     * if shuffle is ON play random song
     * */
    @Override
    public void onCompletion(MediaPlayer arg0) {

        // check for repeat is ON or OFF
        if(isRepeat){
            // repeat is on play same song again
            playSong(currentSongIndex);
        } else if(isShuffle){
            // shuffle is on - play a random song
            Random rand = new Random();
            currentSongIndex = rand.nextInt((songsList.size() - 1) - 0 + 1) + 0;
            playSong(currentSongIndex);
        } else{
            // no repeat or shuffle ON - القرآن انتهى
            btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
            // إيقاف تحديث شريط التقدم
            mHandler.removeCallbacks(mUpdateTimeTask);
            // إيقاف انيميشن التشغيل
            stopPlayingAnimations();
            // الاحتفاظ بشريط التقدم في النهاية (100%)
            songProgressBar.setProgress(100);
        }
    }

    /**
     * إعداد الانيميشن والمؤثرات المتحركة
     */
    private void setupAnimations() {
        try {
            // ربط العناصر
            quranIcon = findViewById(R.id.quranIcon);
            outerGlow = findViewById(R.id.outerGlow);
            middleGlow = findViewById(R.id.middleGlow);
            innerGlow = findViewById(R.id.innerGlow);
            decorativeStars = findViewById(R.id.decorativeStars);

            // إعداد انيميشن ظهور تدريجي للصورة
            if (quranIcon != null) {
                quranIcon.setAlpha(0f);
                quranIcon.animate()
                    .alpha(1f)
                    .setDuration(1000)
                    .start();
            }

            // تفعيل تأثيرات الإضاءة المستمرة
            startGlowEffects();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * تفعيل تأثيرات الإضاءة المستمرة
     */
    private void startGlowEffects() {
        try {
            // تأثير الإضاءة الخارجية
            if (outerGlow != null) {
                outerGlow.animate()
                    .alpha(0.3f)
                    .setDuration(2000)
                    .withEndAction(() -> {
                        if (outerGlow != null) {
                            outerGlow.animate()
                                .alpha(0.8f)
                                .setDuration(2000)
                                .withEndAction(this::startGlowEffects)
                                .start();
                        }
                    })
                    .start();
            }

            // تأثير الإضاءة المتوسطة
            if (middleGlow != null) {
                middleGlow.animate()
                    .alpha(0.4f)
                    .setDuration(1500)
                    .withEndAction(() -> {
                        if (middleGlow != null) {
                            middleGlow.animate()
                                .alpha(0.9f)
                                .setDuration(1500)
                                .start();
                        }
                    })
                    .start();
            }

            // تأثير الإضاءة الداخلية
            if (innerGlow != null) {
                innerGlow.animate()
                    .alpha(0.5f)
                    .setDuration(1000)
                    .withEndAction(() -> {
                        if (innerGlow != null) {
                            innerGlow.animate()
                                .alpha(1.0f)
                                .setDuration(1000)
                                .start();
                        }
                    })
                    .start();
            }

            // تأثير النجوم الزخرفية
            if (decorativeStars != null) {
                decorativeStars.animate()
                    .rotationBy(360f)
                    .setDuration(20000)
                    .withEndAction(() -> {
                        if (decorativeStars != null) {
                            startGlowEffects(); // إعادة تشغيل الدوران
                        }
                    })
                    .start();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * تطبيق انيميشن التشغيل
     */
    private void startPlayingAnimations() {
        try {
            if (quranIcon != null) {
                // انيميشن دوران مستمر للصورة
                quranIcon.animate()
                    .rotationBy(360f)
                    .setDuration(8000)
                    .withEndAction(() -> {
                        try {
                            if (mp != null && isMediaPlayerPlaying()) {
                                startPlayingAnimations(); // تكرار الدوران
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    })
                    .start();
            }

            // تسريع تأثيرات الإضاءة أثناء التشغيل
            if (outerGlow != null) {
                outerGlow.animate()
                    .alpha(1.0f)
                    .setDuration(500)
                    .start();
            }

            if (middleGlow != null) {
                middleGlow.animate()
                    .alpha(1.0f)
                    .setDuration(500)
                    .start();
            }

            if (innerGlow != null) {
                innerGlow.animate()
                    .alpha(1.0f)
                    .setDuration(500)
                    .start();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * فحص آمن لحالة MediaPlayer
     */
    private boolean isMediaPlayerPlaying() {
        try {
            return mp != null && mp.isPlaying();
        } catch (IllegalStateException e) {
            // MediaPlayer في حالة غير صالحة
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * إيقاف انيميشن التشغيل
     */
    private void stopPlayingAnimations() {
        try {
            if (quranIcon != null) {
                // إيقاف الدوران وانيميشن نبضة
                quranIcon.animate()
                    .scaleX(1.1f)
                    .scaleY(1.1f)
                    .setDuration(200)
                    .withEndAction(() -> {
                        quranIcon.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(200)
                            .start();
                    })
                    .start();
            }

            // إرجاع تأثيرات الإضاءة للحالة العادية
            if (outerGlow != null) {
                outerGlow.animate()
                    .alpha(0.6f)
                    .setDuration(500)
                    .start();
            }

            if (middleGlow != null) {
                middleGlow.animate()
                    .alpha(0.7f)
                    .setDuration(500)
                    .start();
            }

            if (innerGlow != null) {
                innerGlow.animate()
                    .alpha(0.8f)
                    .setDuration(500)
                    .start();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * الحصول على اسم القارئ
     */
    private String getReciterName() {
        try {
            if (RecitesName != null && !RecitesName.isEmpty()) {
                // تحويل اسم القارئ من الإنجليزية إلى العربية
                LnaguageClass lc = new LnaguageClass();
                ArrayList<AuthorClass> recitersList = lc.AutherList();

                for (AuthorClass reciter : recitersList) {
                    if (reciter.ServerName.equals(RecitesName)) {
                        return reciter.RealName;
                    }
                }
                return RecitesName;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "قارئ مجهول";
    }

    /**
     * بدء إشعار الوسائط
     */
    private void startMediaNotification() {
        try {
            String surahName = getSurahName(currentSongIndex);
            String reciterName = getReciterName();

            MediaNotificationService.updateInfo(surahName, reciterName, true);

            Intent serviceIntent = new Intent(this, MediaNotificationService.class);
            serviceIntent.setAction("SHOW_NOTIFICATION");
            startService(serviceIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * إيقاف إشعار الوسائط
     */
    private void stopMediaNotification() {
        try {
            Intent serviceIntent = new Intent(this, MediaNotificationService.class);
            serviceIntent.setAction("STOP_NOTIFICATION");
            startService(serviceIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private android.content.BroadcastReceiver mediaReceiver;
    private android.content.BroadcastReceiver simpleMediaReceiver;

    // Static references للتحكم المباشر
    private static managerdb currentInstance;
    private static MediaPlayer staticMp;

    /**
     * Static methods للتحكم المباشر في MediaPlayer
     */
    public static void staticPlayPause() {
        try {
            android.util.Log.d("STATIC_CONTROL", "🎯 staticPlayPause() تم استدعاؤه!");

            if (staticMp != null && currentInstance != null) {
                if (staticMp.isPlaying()) {
                    android.util.Log.d("STATIC_CONTROL", "⏸️ إيقاف التشغيل");
                    staticMp.pause();
                    currentInstance.btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                    currentInstance.stopPlayingAnimations();
                } else {
                    android.util.Log.d("STATIC_CONTROL", "▶️ بدء التشغيل");
                    staticMp.start();
                    currentInstance.btnPlay.setImageResource(R.drawable.ic_pause_white);
                    currentInstance.startPlayingAnimations();
                }
                // تحديث الإشعار
                SimpleMediaNotification.updateInfo(
                    currentInstance.getSurahName(currentInstance.currentSongIndex),
                    currentInstance.getReciterName(),
                    staticMp.isPlaying()
                );
            } else {
                android.util.Log.e("STATIC_CONTROL", "❌ staticMp أو currentInstance is null!");
            }
        } catch (Exception e) {
            android.util.Log.e("STATIC_CONTROL", "❌ خطأ في staticPlayPause: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * تسجيل BroadcastReceiver لأوامر الوسائط
     */
    private void setupMediaBroadcastReceiver() {
        try {
            android.util.Log.d("MEDIA_RECEIVER", "تسجيل BroadcastReceiver");

            mediaReceiver = new android.content.BroadcastReceiver() {
                @Override
                public void onReceive(android.content.Context context, android.content.Intent intent) {
                    android.util.Log.d("MEDIA_RECEIVER", "تم استقبال broadcast من MediaActionReceiver");
                    String action = intent.getStringExtra("media_action");
                    android.util.Log.d("MEDIA_RECEIVER", "الأكشن المستقبل: " + action);
                    if (action != null) {
                        handleDirectMediaAction(action);
                    } else {
                        android.util.Log.e("MEDIA_RECEIVER", "الأكشن فارغ!");
                    }
                }
            };

            android.content.IntentFilter filter = new android.content.IntentFilter("com.qurany2019.quranyapp.MEDIA_CONTROL");
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(mediaReceiver, filter, android.content.Context.RECEIVER_NOT_EXPORTED);
            } else {
                registerReceiver(mediaReceiver, filter);
            }
            android.util.Log.d("MEDIA_RECEIVER", "تم تسجيل BroadcastReceiver بنجاح");
        } catch (Exception e) {
            android.util.Log.e("MEDIA_RECEIVER", "خطأ في تسجيل BroadcastReceiver: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * معالجة أوامر الوسائط مباشرة
     */
    private void handleDirectMediaAction(String action) {
        try {
            android.util.Log.d("MEDIA_ACTION", "تم استقبال أكشن: " + action);

            if ("com.qurany2019.quranyapp.PLAY".equals(action)) {
                android.util.Log.d("MEDIA_ACTION", "تشغيل من الإشعار");
                if (mp != null && !isMediaPlayerPlaying()) {
                    mp.start();
                    btnPlay.setImageResource(R.drawable.ic_pause_white);
                    startPlayingAnimations();
                    // تحديث حالة الإشعار بعد التشغيل الفعلي
                    MediaNotificationService.updateInfo(getSurahName(currentSongIndex), getReciterName(), true);
                    // إرسال أمر لتحديث الإشعار
                    updateMediaNotification();
                }
            } else if ("com.qurany2019.quranyapp.PAUSE".equals(action)) {
                android.util.Log.d("MEDIA_ACTION", "إيقاف من الإشعار");
                if (mp != null && isMediaPlayerPlaying()) {
                    mp.pause();
                    btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                    stopPlayingAnimations();
                    MediaNotificationService.updateInfo(getSurahName(currentSongIndex), getReciterName(), false);
                    // إرسال أمر لتحديث الإشعار
                    updateMediaNotification();
                }
            } else if ("com.qurany2019.quranyapp.NEXT".equals(action)) {
                android.util.Log.d("MEDIA_ACTION", "التالي من الإشعار");
                if (currentSongIndex < (songsList.size() - 1)) {
                    playSong(currentSongIndex + 1);
                    currentSongIndex = currentSongIndex + 1;
                } else {
                    // إذا كانت آخر سورة، ارجع للأولى
                    playSong(0);
                    currentSongIndex = 0;
                }
            } else if ("com.qurany2019.quranyapp.PREVIOUS".equals(action)) {
                android.util.Log.d("MEDIA_ACTION", "السابق من الإشعار");
                if (currentSongIndex > 0) {
                    playSong(currentSongIndex - 1);
                    currentSongIndex = currentSongIndex - 1;
                } else {
                    // إذا كانت أول سورة، اذهب للأخيرة
                    playSong(songsList.size() - 1);
                    currentSongIndex = songsList.size() - 1;
                }
            } else if ("com.qurany2019.quranyapp.STOP".equals(action)) {
                android.util.Log.d("MEDIA_ACTION", "إيقاف كامل من الإشعار");
                if (mp != null) {
                    mp.stop();
                    btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                    stopPlayingAnimations();
                    stopMediaNotification();
                }
            }
        } catch (Exception e) {
            android.util.Log.e("MEDIA_ACTION", "خطأ في معالجة الأكشن: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * معالجة أوامر الإشعار البسيط - محدثة لاستخدام MediaPlayerManager
     */
    private void handleSimpleMediaIntent(Intent intent) {
        if (intent != null && "MEDIA_BUTTON".equals(intent.getAction())) {
            String action = intent.getStringExtra("button_action");
            Log.d("SIMPLE_MEDIA", "تم استقبال أمر: " + action);

            if ("PLAY_PAUSE".equals(action)) {
                if (MediaPlayerManager.isPlaying()) {
                    Log.d("SIMPLE_MEDIA", "إيقاف التشغيل");
                    MediaPlayerManager.pause();
                    btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                    stopPlayingAnimations();
                    SimpleMediaNotification.updateInfo(getSurahName(currentSongIndex), getReciterName(), false);
                } else {
                    Log.d("SIMPLE_MEDIA", "بدء التشغيل");
                    MediaPlayerManager.play();
                    btnPlay.setImageResource(R.drawable.ic_pause_white);
                    startPlayingAnimations();
                    SimpleMediaNotification.updateInfo(getSurahName(currentSongIndex), getReciterName(), true);
                }
            } else if ("NEXT".equals(action)) {
                Log.d("SIMPLE_MEDIA", "التالي");
                if (currentSongIndex < (songsList.size() - 1)) {
                    playSong(currentSongIndex + 1);
                    currentSongIndex = currentSongIndex + 1;
                } else {
                    playSong(0);
                    currentSongIndex = 0;
                }
            } else if ("PREVIOUS".equals(action)) {
                Log.d("SIMPLE_MEDIA", "السابق");
                if (currentSongIndex > 0) {
                    playSong(currentSongIndex - 1);
                    currentSongIndex = currentSongIndex - 1;
                } else {
                    playSong(songsList.size() - 1);
                    currentSongIndex = songsList.size() - 1;
                }
            }
        }
    }

    /**
     * بدء إشعار الوسائط البسيط
     */
    private void startSimpleMediaNotification() {
        try {
            String surahName = getSurahName(currentSongIndex);
            String reciterName = getReciterName();

            SimpleMediaNotification.updateInfo(surahName, reciterName, true);

            Intent serviceIntent = new Intent(this, SimpleMediaNotification.class);
            serviceIntent.setAction("SHOW");
            startService(serviceIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * تحديث إشعار الوسائط
     */
    private void updateMediaNotification() {
        try {
            Intent serviceIntent = new Intent(this, MediaNotificationService.class);
            serviceIntent.setAction("SHOW_NOTIFICATION");
            startService(serviceIntent);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * تسجيل BroadcastReceiver البسيط
     */
    private void setupSimpleMediaReceiver() {
        try {
            android.util.Log.d("SIMPLE_RECEIVER", "تسجيل BroadcastReceiver البسيط");

            simpleMediaReceiver = new android.content.BroadcastReceiver() {
                @Override
                public void onReceive(android.content.Context context, android.content.Intent intent) {
                    android.util.Log.d("SIMPLE_RECEIVER", "🎯 تم استقبال broadcast!");
                    String action = intent.getStringExtra("action");
                    android.util.Log.d("SIMPLE_RECEIVER", "الأكشن: " + action);

                    if ("PLAY_PAUSE".equals(action)) {
                        android.util.Log.d("SIMPLE_RECEIVER", "🔥 معالجة PLAY_PAUSE");

                        if (MediaPlayerManager.isPlaying()) {
                            android.util.Log.d("SIMPLE_RECEIVER", "⏸️ إيقاف التشغيل");
                            MediaPlayerManager.pause();
                            btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                            stopPlayingAnimations();
                        } else {
                            android.util.Log.d("SIMPLE_RECEIVER", "▶️ بدء التشغيل");
                            MediaPlayerManager.play();
                            btnPlay.setImageResource(R.drawable.ic_pause_white);
                            startPlayingAnimations();
                        }

                        // تحديث الإشعار
                        SimpleMediaNotification.updateInfo(getSurahName(currentSongIndex), getReciterName(), MediaPlayerManager.isPlaying());
                    }
                }
            };

            android.content.IntentFilter filter = new android.content.IntentFilter("SIMPLE_MEDIA_CONTROL");
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                registerReceiver(simpleMediaReceiver, filter, android.content.Context.RECEIVER_NOT_EXPORTED);
            } else {
                registerReceiver(simpleMediaReceiver, filter);
            }
            android.util.Log.d("SIMPLE_RECEIVER", "✅ تم تسجيل BroadcastReceiver البسيط بنجاح");
        } catch (Exception e) {
            android.util.Log.e("SIMPLE_RECEIVER", "❌ خطأ في تسجيل BroadcastReceiver البسيط: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * معالجة أكشن إشعارات الوسائط من Intent مباشر
     */
    private void handleMediaAction() {
        try {
            Intent intent = getIntent();
            if (intent != null && "MEDIA_ACTION".equals(intent.getAction())) {
                String action = intent.getStringExtra("action");
                android.util.Log.d("MEDIA_DIRECT", "تم استقبال أكشن مباشر: " + action);

                if ("ACTION_PLAY".equals(action)) {
                    android.util.Log.d("MEDIA_DIRECT", "تشغيل مباشر من الإشعار");
                    if (mp != null && !isMediaPlayerPlaying()) {
                        mp.start();
                        btnPlay.setImageResource(R.drawable.ic_pause_white);
                        startPlayingAnimations();
                        MediaNotificationService.updateInfo(getSurahName(currentSongIndex), getReciterName(), true);
                    }
                } else if ("ACTION_PAUSE".equals(action)) {
                    android.util.Log.d("MEDIA_DIRECT", "إيقاف مباشر من الإشعار");
                    if (mp != null && isMediaPlayerPlaying()) {
                        mp.pause();
                        btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                        stopPlayingAnimations();
                        MediaNotificationService.updateInfo(getSurahName(currentSongIndex), getReciterName(), false);
                    }
                } else if ("ACTION_NEXT".equals(action)) {
                    android.util.Log.d("MEDIA_DIRECT", "التالي مباشر من الإشعار");
                    if (currentSongIndex < (songsList.size() - 1)) {
                        playSong(currentSongIndex + 1);
                        currentSongIndex = currentSongIndex + 1;
                    } else {
                        // إذا كانت آخر سورة، ارجع للأولى
                        playSong(0);
                        currentSongIndex = 0;
                    }
                } else if ("ACTION_PREVIOUS".equals(action)) {
                    android.util.Log.d("MEDIA_DIRECT", "السابق مباشر من الإشعار");
                    if (currentSongIndex > 0) {
                        playSong(currentSongIndex - 1);
                        currentSongIndex = currentSongIndex - 1;
                    } else {
                        // إذا كانت أول سورة، اذهب للأخيرة
                        playSong(songsList.size() - 1);
                        currentSongIndex = songsList.size() - 1;
                    }
                } else if ("ACTION_STOP".equals(action)) {
                    android.util.Log.d("MEDIA_DIRECT", "إيقاف كامل مباشر من الإشعار");
                    if (mp != null) {
                        mp.stop();
                        btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                        stopPlayingAnimations();
                        stopMediaNotification();
                    }
                }
            }
        } catch (Exception e) {
            android.util.Log.e("MEDIA_DIRECT", "خطأ في معالجة الأكشن المباشر: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onDestroy(){
        try {
            // إلغاء تسجيل BroadcastReceiver
            if (mediaReceiver != null) {
                unregisterReceiver(mediaReceiver);
                android.util.Log.d("MEDIA_RECEIVER", "تم إلغاء تسجيل BroadcastReceiver");
            }

            if (mp != null) {
                if (isMediaPlayerPlaying()) {
                    mp.stop();
                }
                mp.release();
                mp = null;
            }
        } catch (Exception e) {
            android.util.Log.e("MEDIA_RECEIVER", "خطأ في onDestroy: " + e.getMessage());
            e.printStackTrace();
        }
        super.onDestroy();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);

        android.util.Log.d("MANAGER_DB", "🎯 onNewIntent تم استدعاؤه!");
        if (intent != null) {
            android.util.Log.d("MANAGER_DB", "Action: " + intent.getAction());
            android.util.Log.d("MANAGER_DB", "Extras: " + intent.getExtras());

            // طباعة جميع الـ extras للتشخيص
            if (intent.getExtras() != null) {
                for (String key : intent.getExtras().keySet()) {
                    android.util.Log.d("MANAGER_DB", "Extra Key: " + key + " = " + intent.getExtras().get(key));
                }
            }

            // معالجة أوامر الإشعار البسيط
            if ("SIMPLE_MEDIA_ACTION".equals(intent.getAction())) {
                String action = intent.getStringExtra("media_action");
                android.util.Log.d("MANAGER_DB", "🔥 معالجة أمر إشعار: " + action);

                if ("PLAY_PAUSE".equals(action)) {
                    if (mp != null) {
                        if (isMediaPlayerPlaying()) {
                            android.util.Log.d("MANAGER_DB", "⏸️ إيقاف التشغيل");
                            mp.pause();
                            btnPlay.setImageResource(R.drawable.ic_play_arrow_white);
                            stopPlayingAnimations();
                        } else {
                            android.util.Log.d("MANAGER_DB", "▶️ بدء التشغيل");
                            mp.start();
                            btnPlay.setImageResource(R.drawable.ic_pause_white);
                            startPlayingAnimations();
                        }
                        // تحديث الإشعار
                        SimpleMediaNotification.updateInfo(getSurahName(currentSongIndex), getReciterName(), isMediaPlayerPlaying());
                    }
                }
            }
        } else {
            android.util.Log.e("MANAGER_DB", "❌ Intent is null!");
        }

        handleSimpleMediaIntent(intent);
    }

    // =============== نظام Cache الذكي للملفات المحلية ===============

    // Cache للملفات المحلية (نفس النظام من AyaList)
    private static java.util.Map<String, java.util.Set<String>> localFilesCache = new java.util.HashMap<>();
    private static long lastCacheUpdate = 0;
    private static final long CACHE_VALIDITY = 30000; // 30 ثانية

    /**
     * استخراج رقم السورة من المسار
     */
    private String extractSurahCodeFromPath(String path) {
        try {
            if (path.contains("/")) {
                String fileName = path.substring(path.lastIndexOf("/") + 1);
                if (fileName.contains(".")) {
                    fileName = fileName.substring(0, fileName.lastIndexOf("."));
                }
                // البحث عن رقم في بداية اسم الملف
                if (fileName.matches("^\\d{3}.*")) {
                    return fileName.substring(0, 3);
                }
            }
        } catch (Exception e) {
            android.util.Log.e("MANAGER_EXTRACT", "خطأ في استخراج رقم السورة: " + e.getMessage());
        }
        return null;
    }

    /**
     * البحث الذكي عن الملف المحلي
     */
    private String findLocalFileSmartly(String reciterName, String surahCode) {
        try {
            java.io.File musicDir = android.os.Environment.getExternalStoragePublicDirectory(android.os.Environment.DIRECTORY_MUSIC);
            java.io.File quranAppDir = new java.io.File(musicDir, "قرآني");
            java.io.File audioDir = new java.io.File(quranAppDir, reciterName);

            // تحديث cache الملفات
            updateLocalFilesCache(audioDir, reciterName);

            // البحث عن اسم الملف الفعلي
            String actualFileName = findActualFileName(reciterName, surahCode);
            if (actualFileName != null) {
                return new java.io.File(audioDir, actualFileName + ".mp3").getAbsolutePath();
            }

        } catch (Exception e) {
            android.util.Log.e("MANAGER_SMART_SEARCH", getString(R.string.smart_search_error) + ": " + e.getMessage());
        }
        return null;
    }

    /**
     * العثور على اسم الملف الفعلي من cache
     */
    private String findActualFileName(String reciterName, String surahCode) {
        try {
            java.util.Set<String> files = localFilesCache.get(reciterName);
            if (files != null) {
                android.util.Log.d("MANAGER_CACHE", "🔍 البحث عن اسم الملف الفعلي للسورة: " + surahCode);

                // البحث المباشر بالرقم
                for (String fileName : files) {
                    if (fileName.startsWith(surahCode)) {
                        android.util.Log.d("MANAGER_CACHE", "✅ تم العثور على الملف بالرقم: " + fileName);
                        return fileName;
                    }
                }

                // البحث باسم السورة
                String surahName = getSurahNameByCode(surahCode);
                if (surahName != null) {
                    android.util.Log.d("MANAGER_CACHE", "🔍 البحث باسم السورة: " + surahName);
                    for (String fileName : files) {
                        if (fileName.contains(surahName)) {
                            android.util.Log.d("MANAGER_CACHE", "✅ تم العثور على الملف باسم السورة: " + fileName);
                            return fileName;
                        }
                    }
                }

                android.util.Log.d("MANAGER_CACHE", "❌ لم يتم العثور على الملف");
            }
        } catch (Exception e) {
            android.util.Log.e("MANAGER_CACHE", "خطأ في البحث عن اسم الملف: " + e.getMessage());
        }
        return null;
    }

    /**
     * تحديث cache الملفات المحلية
     */
    private void updateLocalFilesCache(java.io.File audioDir, String reciterName) {
        try {
            long currentTime = System.currentTimeMillis();

            if (currentTime - lastCacheUpdate > CACHE_VALIDITY || !localFilesCache.containsKey(reciterName)) {
                java.util.Set<String> files = new java.util.HashSet<>();

                if (audioDir.exists()) {
                    String[] fileList = audioDir.list();
                    if (fileList != null) {
                        android.util.Log.d("MANAGER_CACHE", "📁 فحص مجلد: " + audioDir.getAbsolutePath());
                        for (String fileName : fileList) {
                            android.util.Log.d("MANAGER_CACHE", "📄 ملف موجود: " + fileName);
                            if (fileName.endsWith(".mp3")) {
                                String baseName = fileName.substring(0, fileName.lastIndexOf(".mp3"));
                                files.add(baseName);
                                android.util.Log.d("MANAGER_CACHE", "✅ تم إضافة للـ cache: " + baseName);
                            }
                        }
                    }
                }

                localFilesCache.put(reciterName, files);
                lastCacheUpdate = currentTime;
                android.util.Log.d("MANAGER_CACHE", "تم تحديث cache للقارئ: " + reciterName + " - عدد الملفات: " + files.size());
            }
        } catch (Exception e) {
            android.util.Log.e("MANAGER_CACHE", "خطأ في تحديث cache: " + e.getMessage());
        }
    }

    /**
     * فحص وجود الملف في cache
     */
    private boolean isFileInCache(String reciterName, String surahCode) {
        try {
            java.util.Set<String> files = localFilesCache.get(reciterName);
            if (files != null) {
                android.util.Log.d("MANAGER_CACHE", "🔍 البحث عن: " + surahCode + " في cache");
                android.util.Log.d("MANAGER_CACHE", "📋 الملفات المتاحة: " + files.toString());

                // البحث المباشر بالرقم
                boolean found = files.contains(surahCode);
                if (found) {
                    android.util.Log.d("MANAGER_CACHE", "✅ تم العثور على الملف بالرقم");
                    return true;
                }

                // البحث باسم السورة
                String surahName = getSurahNameByCode(surahCode);
                if (surahName != null) {
                    android.util.Log.d("MANAGER_CACHE", "🔍 البحث باسم السورة: " + surahName);
                    for (String fileName : files) {
                        if (fileName.contains(surahName)) {
                            android.util.Log.d("MANAGER_CACHE", "✅ تم العثور على الملف باسم السورة: " + fileName);
                            return true;
                        }
                    }
                }

                android.util.Log.d("MANAGER_CACHE", "❌ لم يتم العثور على الملف");
                return false;
            }
        } catch (Exception e) {
            android.util.Log.e("MANAGER_CACHE", "خطأ في فحص cache: " + e.getMessage());
        }
        return false;
    }

    /**
     * الحصول على اسم السورة من الرقم
     */
    private String getSurahNameByCode(String surahCode) {
        try {
            int code = Integer.parseInt(surahCode);
            String[] surahNames = {
                "الفاتحة", "البقرة", "ال عمران", "النساء", "المائدة", "الانعام", "الأعراف", "الأنفال", "التوبة", "يونس",
                "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
                "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
                "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
                "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
                "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
                "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
                "نوح", "الجن", "المزمل", "المدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
                "التكوير", "الانفطار", "المطففين", "الانشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
                "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
                "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
                "المسد", "الاخلاص", "الفلق", "الناس"
            };

            if (code >= 1 && code <= surahNames.length) {
                return surahNames[code - 1].trim();
            }
        } catch (Exception e) {
            android.util.Log.e("MANAGER_SURAH_NAME", getString(R.string.surah_number_conversion_error) + ": " + e.getMessage());
        }
        return null;
    }
}
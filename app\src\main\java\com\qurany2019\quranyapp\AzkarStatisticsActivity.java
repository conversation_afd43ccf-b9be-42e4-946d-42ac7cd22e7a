package com.qurany2019.quranyapp;

import android.graphics.Color;
import android.os.Bundle;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.ViewModelProvider;

import com.github.mikephil.charting.charts.BarChart;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.BarData;
import com.github.mikephil.charting.data.BarDataSet;
import com.github.mikephil.charting.data.BarEntry;
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter;
import com.github.mikephil.charting.utils.ColorTemplate;

import com.qurany2019.quranyapp.database.entities.AzkarStatisticsEntity;
import com.qurany2019.quranyapp.viewmodel.AzkarViewModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

public class AzkarStatisticsActivity extends BaseActivity {
    
    private AzkarViewModel viewModel;
    
    // UI Components
    private TextView totalAzkarCount;
    private TextView completedAzkarCount;
    private TextView completionPercentage;
    private TextView totalTasbihCount;
    private TextView sessionDuration;
    private TextView streakDays;
    private ProgressBar overallProgressBar;
    private BarChart weeklyChart;
    
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_azkar_statistics);
        
        setupToolbar();
        initializeViews();
        setupViewModel();
        setupChart();
        observeData();
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
        
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }

    private void initializeViews() {
        totalAzkarCount = findViewById(R.id.totalAzkarCount);
        completedAzkarCount = findViewById(R.id.completedAzkarCount);
        completionPercentage = findViewById(R.id.completionPercentage);
        totalTasbihCount = findViewById(R.id.totalTasbihCount);
        sessionDuration = findViewById(R.id.sessionDuration);
        streakDays = findViewById(R.id.streakDays);
        overallProgressBar = findViewById(R.id.overallProgressBar);
        weeklyChart = findViewById(R.id.weeklyChart);
    }

    private void setupViewModel() {
        viewModel = new ViewModelProvider(this).get(AzkarViewModel.class);
    }

    private void setupChart() {
        // إعداد الرسم البياني
        weeklyChart.getDescription().setEnabled(false);
        weeklyChart.setDrawGridBackground(false);
        weeklyChart.setDrawBarShadow(false);
        weeklyChart.setHighlightFullBarEnabled(false);
        weeklyChart.setPinchZoom(false);
        weeklyChart.setDrawValueAboveBar(true);
        
        // إعداد المحور السيني
        XAxis xAxis = weeklyChart.getXAxis();
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setDrawGridLines(false);
        xAxis.setGranularity(1f);
        xAxis.setLabelCount(7);
        
        // إعداد المحور الصادي الأيسر
        YAxis leftAxis = weeklyChart.getAxisLeft();
        leftAxis.setLabelCount(8, false);
        leftAxis.setPosition(YAxis.YAxisLabelPosition.OUTSIDE_CHART);
        leftAxis.setSpaceTop(15f);
        leftAxis.setAxisMinimum(0f);
        
        // إخفاء المحور الصادي الأيمن
        weeklyChart.getAxisRight().setEnabled(false);
        
        // إعداد الألوان
        weeklyChart.setBackgroundColor(Color.WHITE);
        weeklyChart.animateY(1000);
    }

    private void observeData() {
        // قراءة البيانات الحقيقية من SharedPreferences بدلاً من قاعدة البيانات
        loadRealStatisticsFromPrefs();

        // مراقبة إحصائيات اليوم (احتياطي)
        viewModel.getTodayStatistics().observe(this, statisticsList -> {
            if (statisticsList != null && !statisticsList.isEmpty()) {
                updateTodayStatistics(statisticsList.get(0)); // الإحصائيات العامة
            } else {
                // إذا لم توجد بيانات في قاعدة البيانات، استخدم SharedPreferences
                loadRealStatisticsFromPrefs();
            }
        });

        // مراقبة التقدم اليومي
        viewModel.getTodayProgress().observe(this, progressList -> {
            if (progressList != null) {
                updateProgressStatistics(progressList);
            }
        });

        // تحديث الرسم البياني الأسبوعي
        updateWeeklyChart();
    }

    private void loadRealStatisticsFromPrefs() {
        try {
            // قراءة البيانات الحقيقية من SharedPreferences
            android.content.SharedPreferences prefs = getSharedPreferences("azkar_progress", MODE_PRIVATE);

            // قراءة البيانات المحفوظة
            int totalCompleted = prefs.getInt("total_azkar_completed", 0);
            int totalTasbih = prefs.getInt("total_tasbih_count", 0);
            int streakDaysValue = prefs.getInt("streak_days", 0);
            long totalSessionTime = prefs.getLong("total_session_time", 0);

            // حساب أذكار اليوم
            String today = dateFormat.format(new java.util.Date());
            int todayAzkar = prefs.getInt("daily_azkar_" + today, 0);

            // حساب النسبة المئوية (من 20 ذكر يومياً)
            int dailyTarget = 20;
            float percentage = dailyTarget > 0 ? ((float) todayAzkar / dailyTarget) * 100 : 0;
            if (percentage > 100) percentage = 100;

            // عرض البيانات الحقيقية
            totalAzkarCount.setText(String.valueOf(totalCompleted));
            completedAzkarCount.setText(String.valueOf(todayAzkar));
            completionPercentage.setText(String.format(Locale.getDefault(), "%.0f%%", percentage));
            totalTasbihCount.setText(String.valueOf(totalTasbih));
            sessionDuration.setText(formatDuration(totalSessionTime));
            streakDays.setText(String.format(Locale.getDefault(), "%d %s", streakDaysValue, getString(R.string.days_text)));

            // تحديث شريط التقدم
            overallProgressBar.setMax(100);
            overallProgressBar.setProgress((int) percentage);

        } catch (Exception e) {
            // في حالة الخطأ، عرض قيم افتراضية
            totalAzkarCount.setText("0");
            completedAzkarCount.setText("0");
            completionPercentage.setText("0%");
            totalTasbihCount.setText("0");
            sessionDuration.setText("0 " + getString(R.string.minutes_text));
            streakDays.setText("0 " + getString(R.string.days_text));
            overallProgressBar.setProgress(0);
        }
    }

    private void updateTodayStatistics(AzkarStatisticsEntity stats) {
        totalAzkarCount.setText(String.valueOf(stats.getTotalAzkarCount()));
        completedAzkarCount.setText(String.valueOf(stats.getCompletedAzkarCount()));
        
        float percentage = stats.getCompletionPercentage();
        completionPercentage.setText(String.format(Locale.getDefault(), "%.0f%%", percentage));
        
        totalTasbihCount.setText(String.valueOf(stats.getTotalTasbihCount()));
        sessionDuration.setText(formatDuration(stats.getSessionDuration()));
        streakDays.setText(String.format(Locale.getDefault(), "%d %s", stats.getStreakDays(), getString(R.string.days_text)));
        
        // تحديث شريط التقدم
        overallProgressBar.setMax(100);
        overallProgressBar.setProgress((int) percentage);
    }

    private void updateProgressStatistics(List progressList) {
        // حساب الإحصائيات من قائمة التقدم
        int total = progressList.size();
        int completed = 0;
        int totalTasbih = 0;
        
        for (Object progress : progressList) {
            // سيتم تحديث هذا عند ربطه بـ AzkarProgressEntity
            // completed += progress.isCompleted() ? 1 : 0;
            // totalTasbih += progress.getCurrentCount();
        }
        
        float percentage = total > 0 ? ((float) completed / total) * 100 : 0;
        
        totalAzkarCount.setText(String.valueOf(total));
        completedAzkarCount.setText(String.valueOf(completed));
        completionPercentage.setText(String.format(Locale.getDefault(), "%.0f%%", percentage));
        totalTasbihCount.setText(String.valueOf(totalTasbih));
        
        overallProgressBar.setMax(100);
        overallProgressBar.setProgress((int) percentage);
    }

    private void updateWeeklyChart() {
        List<BarEntry> entries = new ArrayList<>();
        List<String> labels = new ArrayList<>();
        
        Calendar calendar = Calendar.getInstance();
        
        // إنشاء بيانات الأسبوع الماضي
        for (int i = 6; i >= 0; i--) {
            calendar.add(Calendar.DAY_OF_YEAR, -i);
            
            // إضافة تسمية اليوم
            String dayName = getDayName(calendar.get(Calendar.DAY_OF_WEEK));
            labels.add(dayName);
            
            // قراءة البيانات الحقيقية من SharedPreferences
            float completionValue = getRealDayCompletion(calendar);
            entries.add(new BarEntry(6 - i, completionValue));
            
            calendar = Calendar.getInstance(); // إعادة تعيين التقويم
        }
        
        BarDataSet dataSet = new BarDataSet(entries, getString(R.string.daily_completion_rate));
        dataSet.setColors(ColorTemplate.MATERIAL_COLORS);
        dataSet.setValueTextColor(Color.BLACK);
        dataSet.setValueTextSize(12f);
        
        BarData barData = new BarData(dataSet);
        barData.setBarWidth(0.9f);
        
        weeklyChart.setData(barData);
        weeklyChart.getXAxis().setValueFormatter(new IndexAxisValueFormatter(labels));
        weeklyChart.invalidate(); // تحديث الرسم البياني
    }

    private float getRealDayCompletion(Calendar calendar) {
        try {
            // قراءة البيانات الحقيقية من SharedPreferences
            android.content.SharedPreferences prefs = getSharedPreferences("azkar_progress", 0);

            // تنسيق التاريخ
            String dateKey = dateFormat.format(calendar.getTime());

            // قراءة عدد الأذكار لهذا اليوم
            int dailyAzkar = prefs.getInt("daily_azkar_" + dateKey, 0);

            // حساب النسبة المئوية (من 20 ذكر يومياً)
            int dailyTarget = 20;
            float percentage = dailyTarget > 0 ? ((float) dailyAzkar / dailyTarget) * 100 : 0;
            if (percentage > 100) percentage = 100;

            return percentage;
        } catch (Exception e) {
            return 0; // في حالة الخطأ
        }
    }

    private String getDayName(int dayOfWeek) {
        switch (dayOfWeek) {
            case Calendar.SUNDAY: return getString(R.string.sunday);
            case Calendar.MONDAY: return getString(R.string.monday);
            case Calendar.TUESDAY: return getString(R.string.tuesday);
            case Calendar.WEDNESDAY: return getString(R.string.wednesday);
            case Calendar.THURSDAY: return getString(R.string.thursday);
            case Calendar.FRIDAY: return getString(R.string.friday);
            case Calendar.SATURDAY: return getString(R.string.saturday);
            default: return "";
        }
    }

    private String formatDuration(long durationMillis) {
        long seconds = durationMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format(Locale.getDefault(), "%d:%02d:%02d", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format(Locale.getDefault(), "%d:%02d", minutes, seconds % 60);
        } else {
            return String.format(Locale.getDefault(), "%d %s", seconds, getString(R.string.seconds_text));
        }
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}

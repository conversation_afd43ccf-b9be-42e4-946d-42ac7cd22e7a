// Generated by view binder compiler. Do not edit!
package com.qurany2019.quranyapp.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.gms.ads.AdView;
import com.qurany2019.quranyapp.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAyaListBinding implements ViewBinding {
  @NonNull
  private final RelativeLayout rootView;

  @NonNull
  public final LinearLayout LayoutLoading;

  @NonNull
  public final LinearLayout adContainer;

  @NonNull
  public final AdView adView;

  @NonNull
  public final TextView downloadingText;

  @NonNull
  public final RelativeLayout headerLayout;

  @NonNull
  public final ListView listView;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView reciterNameText;

  @NonNull
  public final TextView surahCountText;

  private ActivityAyaListBinding(@NonNull RelativeLayout rootView,
      @NonNull LinearLayout LayoutLoading, @NonNull LinearLayout adContainer,
      @NonNull AdView adView, @NonNull TextView downloadingText,
      @NonNull RelativeLayout headerLayout, @NonNull ListView listView,
      @NonNull ProgressBar progressBar, @NonNull TextView reciterNameText,
      @NonNull TextView surahCountText) {
    this.rootView = rootView;
    this.LayoutLoading = LayoutLoading;
    this.adContainer = adContainer;
    this.adView = adView;
    this.downloadingText = downloadingText;
    this.headerLayout = headerLayout;
    this.listView = listView;
    this.progressBar = progressBar;
    this.reciterNameText = reciterNameText;
    this.surahCountText = surahCountText;
  }

  @Override
  @NonNull
  public RelativeLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAyaListBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAyaListBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_aya_list, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAyaListBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.LayoutLoading;
      LinearLayout LayoutLoading = ViewBindings.findChildViewById(rootView, id);
      if (LayoutLoading == null) {
        break missingId;
      }

      id = R.id.adContainer;
      LinearLayout adContainer = ViewBindings.findChildViewById(rootView, id);
      if (adContainer == null) {
        break missingId;
      }

      id = R.id.adView;
      AdView adView = ViewBindings.findChildViewById(rootView, id);
      if (adView == null) {
        break missingId;
      }

      id = R.id.downloadingText;
      TextView downloadingText = ViewBindings.findChildViewById(rootView, id);
      if (downloadingText == null) {
        break missingId;
      }

      id = R.id.headerLayout;
      RelativeLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.listView;
      ListView listView = ViewBindings.findChildViewById(rootView, id);
      if (listView == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.reciterNameText;
      TextView reciterNameText = ViewBindings.findChildViewById(rootView, id);
      if (reciterNameText == null) {
        break missingId;
      }

      id = R.id.surahCountText;
      TextView surahCountText = ViewBindings.findChildViewById(rootView, id);
      if (surahCountText == null) {
        break missingId;
      }

      return new ActivityAyaListBinding((RelativeLayout) rootView, LayoutLoading, adContainer,
          adView, downloadingText, headerLayout, listView, progressBar, reciterNameText,
          surahCountText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}

package com.qurany2019.quranyapp;

import android.Manifest;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.util.Log;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.cardview.widget.CardView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.MobileAds;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class PrayerTimesActivity extends BaseActivity {

    private static final String TAG = "PrayerTimesActivity";
    private AdView mAdView;
    private TextView tvCurrentDate, tvCurrentTime, tvNextPrayer, tvCityName;
    private TextView tvFajr, tvSunrise, tvDhuhr, tvAsr, tvMaghrib, tvIsha;
    private CardView cardFajr, cardSunrise, cardDhuhr, cardAsr, cardMaghrib, cardIsha;
    private android.widget.Switch switchNotifications;
    private static final int LOCATION_PERMISSION_REQUEST = 1002;

    // Timer لتحديث الصلاة القادمة والوقت الحالي
    private android.os.Handler updateHandler;
    private Runnable updateRunnable;

    // متغيرات أوقات الصلاة
    private double latitude = 24.7136; // الرياض افتراضياً
    private double longitude = 46.6753;
    private String currentCity = "الرياض";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // تطبيق اللغة المحفوظة
        applyLanguageSettings();

        setContentView(R.layout.activity_prayer_times);

        // إعداد التولبار
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(getString(R.string.prayer_times_title));
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        // تهيئة الإعلانات
        MobileAds.initialize(this);
        mAdView = findViewById(R.id.adView);
        AdRequest adRequest = new AdRequest.Builder().build();
        mAdView.loadAd(adRequest);

        initViews();
        loadSavedPrayerTimesOrRequestLocation();
        updateCurrentTime();

        // بدء تحديث الصلاة القادمة والوقت الحالي
        startUpdateTimer();
    }

    private void initViews() {
        tvCurrentDate = findViewById(R.id.tvCurrentDate);
        tvCurrentTime = findViewById(R.id.tvCurrentTime);
        tvNextPrayer = findViewById(R.id.tvNextPrayer);
        tvCityName = findViewById(R.id.tvCityName);

        tvFajr = findViewById(R.id.tvFajr);
        tvSunrise = findViewById(R.id.tvSunrise);
        tvDhuhr = findViewById(R.id.tvDhuhr);
        tvAsr = findViewById(R.id.tvAsr);
        tvMaghrib = findViewById(R.id.tvMaghrib);
        tvIsha = findViewById(R.id.tvIsha);

        cardFajr = findViewById(R.id.cardFajr);
        cardSunrise = findViewById(R.id.cardSunrise);
        cardDhuhr = findViewById(R.id.cardDhuhr);
        cardAsr = findViewById(R.id.cardAsr);
        cardMaghrib = findViewById(R.id.cardMaghrib);
        cardIsha = findViewById(R.id.cardIsha);

        // إعداد Switch الإشعارات
        switchNotifications = findViewById(R.id.switchNotifications);
        setupNotificationSwitch();
    }

    /**
     * إعداد Switch الإشعارات
     */
    private void setupNotificationSwitch() {
        try {
            // قراءة حالة الإشعارات المحفوظة
            android.content.SharedPreferences prefs = getSharedPreferences("prayer_settings", android.content.Context.MODE_PRIVATE);
            boolean notificationsEnabled = prefs.getBoolean("notifications_enabled", true);
            switchNotifications.setChecked(notificationsEnabled);

            // إعداد مستمع التغيير
            switchNotifications.setOnCheckedChangeListener((buttonView, isChecked) -> {
                // حفظ الحالة الجديدة
                prefs.edit().putBoolean("notifications_enabled", isChecked).apply();

                // تفعيل/إلغاء الإشعارات
                PrayerNotificationService notificationService = new PrayerNotificationService(this);
                notificationService.setNotificationsEnabled(isChecked);

                // عرض رسالة للمستخدم
                String message = isChecked ?
                    "🔔 تم تفعيل إشعارات أوقات الصلاة" :
                    "🔕 تم إلغاء إشعارات أوقات الصلاة";
                showToast(message);

                // إذا تم التفعيل وتوجد أوقات صلاة محفوظة، أعد جدولة الإشعارات
                if (isChecked) {
                    rescheduleNotificationsIfNeeded();
                }
            });

        } catch (Exception e) {
            android.util.Log.e(TAG, "Error setting up notification switch", e);
        }
    }

    /**
     * إعادة جدولة الإشعارات إذا لزم الأمر
     */
    private void rescheduleNotificationsIfNeeded() {
        try {
            android.content.SharedPreferences prefs = getSharedPreferences("prayer_times", android.content.Context.MODE_PRIVATE);

            String fajr = prefs.getString("fajr", "");
            String cityName = prefs.getString("city_name", currentCity);

            if (!fajr.isEmpty()) {
                String[] prayerTimes = {
                    prefs.getString("fajr", ""),
                    prefs.getString("sunrise", ""),
                    prefs.getString("dhuhr", ""),
                    prefs.getString("asr", ""),
                    prefs.getString("maghrib", ""),
                    prefs.getString("isha", "")
                };

                PrayerNotificationService notificationService = new PrayerNotificationService(this);
                notificationService.schedulePrayerNotifications(prayerTimes, cityName);

                android.util.Log.d(TAG, "Notifications rescheduled for " + cityName);
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error rescheduling notifications", e);
        }
    }

    /**
     * تحميل أوقات الصلاة المحفوظة أو طلب الموقع إذا لم تكن محفوظة
     */
    private void loadSavedPrayerTimesOrRequestLocation() {
        try {
            // التحقق من وجود أوقات صلاة محفوظة
            android.content.SharedPreferences prefs = getSharedPreferences("prayer_times", MODE_PRIVATE);

            String fajr = prefs.getString("fajr", "");
            String dhuhr = prefs.getString("dhuhr", "");
            String cityName = prefs.getString("city_name", currentCity);

            // إذا كانت هناك أوقات محفوظة، استخدمها
            if (!fajr.isEmpty() && !dhuhr.isEmpty()) {
                // عرض الأوقات المحفوظة
                tvFajr.setText(fajr);
                tvSunrise.setText(prefs.getString("sunrise", ""));
                tvDhuhr.setText(dhuhr);
                tvAsr.setText(prefs.getString("asr", ""));
                tvMaghrib.setText(prefs.getString("maghrib", ""));
                tvIsha.setText(prefs.getString("isha", ""));

                // عرض اسم المدينة المحفوظ
                tvCityName.setText(cityName);

                // تحديث الصلاة القادمة
                updateNextPrayerFromSaved();

            } else {
                // إذا لم تكن هناك أوقات محفوظة، تحقق من أذونات الموقع
                if (hasLocationPermission()) {
                    // إذا كانت الأذونات ممنوحة، احسب أوقات الصلاة
                    showToast("جاري تحديث أوقات الصلاة...");
                    getCurrentLocation();
                } else {
                    // إذا لم تكن الأذونات ممنوحة، اطلبها مباشرة
                    requestLocationPermissions();
                }
            }

        } catch (Exception e) {
            // في حالة حدوث خطأ، عرض أوقات افتراضية
            calculateDefaultPrayerTimes();
        }
    }

    /**
     * حساب أوقات الصلاة الافتراضية (الرياض)
     */
    private void calculateDefaultPrayerTimes() {
        try {
            // أوقات افتراضية للرياض
            tvFajr.setText("05:30");
            tvSunrise.setText("06:45");
            tvDhuhr.setText("12:15");
            tvAsr.setText("15:30");
            tvMaghrib.setText("18:00");
            tvIsha.setText("19:30");

            // تحديث اسم المدينة
            currentCity = "الرياض";
            tvCityName.setText(currentCity);

            // تحديث الصلاة القادمة
            updateNextPrayerFromSaved();

        } catch (Exception e) {
            // في حالة الخطأ، عرض رسالة
            showToast("خطأ في عرض أوقات الصلاة");
        }
    }

    /**
     * تحديث الصلاة القادمة من الأوقات المحفوظة (محسن)
     */
    private void updateNextPrayerFromSaved() {
        try {
            android.content.SharedPreferences prefs = getSharedPreferences("prayer_times", MODE_PRIVATE);

            String[] prayers = {
                getString(R.string.fajr),
                getString(R.string.sunrise),
                getString(R.string.dhuhr),
                getString(R.string.asr),
                getString(R.string.maghrib),
                getString(R.string.isha)
            };
            String[] times = {
                prefs.getString("fajr", ""),
                prefs.getString("sunrise", ""),
                prefs.getString("dhuhr", ""),
                prefs.getString("asr", ""),
                prefs.getString("maghrib", ""),
                prefs.getString("isha", "")
            };

            Calendar now = Calendar.getInstance();
            int currentHour = now.get(Calendar.HOUR_OF_DAY);
            int currentMinute = now.get(Calendar.MINUTE);
            int currentTimeInMinutes = currentHour * 60 + currentMinute;

            String nextPrayer = getString(R.string.fajr);
            String nextTime = convertTimeFormat(times[0]) + " " + getString(R.string.tomorrow_indicator);

            for (int i = 0; i < times.length; i++) {
                if (!times[i].isEmpty()) {
                    try {
                        // تحويل الوقت إلى دقائق مع دعم تنسيق 12 و 24 ساعة
                        int prayerTimeInMinutes = convertTimeToMinutes(times[i]);

                        if (currentTimeInMinutes < prayerTimeInMinutes) {
                            nextPrayer = prayers[i];
                            nextTime = convertTimeFormat(times[i]);
                            break;
                        }
                    } catch (Exception e) {
                        // تجاهل الأخطاء في تحليل الوقت
                        continue;
                    }
                }
            }

            tvNextPrayer.setText(getString(R.string.next_prayer_label) + " " + nextPrayer + " - " + nextTime);

        } catch (Exception e) {
            tvNextPrayer.setText(getString(R.string.determining_next_prayer));
        }
    }

    /**
     * تحويل الوقت إلى دقائق مع دعم تنسيق 12 و 24 ساعة
     */
    private int convertTimeToMinutes(String timeStr) {
        try {
            // إزالة المسافات الزائدة
            timeStr = timeStr.trim();

            // تحديد إذا كان الوقت صباحي أم مسائي
            boolean isPM = timeStr.contains("PM") || timeStr.contains("م");
            boolean isAM = timeStr.contains("AM") || timeStr.contains("ص");

            // إزالة AM/PM والمسافات
            String cleanTime = timeStr.replaceAll("[AMPMصم\\s]", "").trim();

            // تقسيم الوقت إلى ساعات ودقائق
            String[] parts = cleanTime.split(":");
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid time format");
            }

            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);

            // تحويل إلى تنسيق 24 ساعة
            if (isPM && hours != 12) {
                hours += 12;
            } else if (isAM && hours == 12) {
                hours = 0;
            }

            return hours * 60 + minutes;

        } catch (Exception e) {
            // في حالة فشل التحليل، إرجاع قيمة كبيرة لتجنب المقارنة
            return Integer.MAX_VALUE;
        }
    }

    /**
     * بدء تحديث الصلاة القادمة والوقت الحالي كل دقيقة
     */
    private void startUpdateTimer() {
        updateHandler = new android.os.Handler();
        updateRunnable = new Runnable() {
            @Override
            public void run() {
                // تحديث الوقت الحالي
                updateCurrentTime();

                // تحديث الصلاة القادمة من الأوقات المحفوظة
                updateNextPrayerFromSaved();

                // جدولة التحديث التالي بعد دقيقة واحدة
                updateHandler.postDelayed(this, 60000); // 60 ثانية
            }
        };

        // بدء التحديث
        updateHandler.post(updateRunnable);
    }

    /**
     * إيقاف تحديث الصلاة القادمة
     */
    private void stopUpdateTimer() {
        if (updateHandler != null && updateRunnable != null) {
            updateHandler.removeCallbacks(updateRunnable);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        stopUpdateTimer();
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopUpdateTimer();
    }

    @Override
    protected void onResume() {
        super.onResume();
        startUpdateTimer();
    }

    /**
     * التحقق من أذونات الموقع
     */
    private boolean hasLocationPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                == PackageManager.PERMISSION_GRANTED ||
               ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION)
                == PackageManager.PERMISSION_GRANTED;
    }

    private void requestLocationPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {

            // عرض رسالة توضيحية قبل طلب الإذن
            showLocationPermissionDialog();

        } else {
            getCurrentLocation();
        }
    }

    /**
     * عرض رسالة توضيحية لطلب إذن الموقع
     */
    private void showLocationPermissionDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("إذن الوصول للموقع")
               .setMessage("يحتاج التطبيق للوصول إلى موقعك لحساب أوقات الصلاة الدقيقة حسب منطقتك الجغرافية.\n\n" +
                          "سيتم استخدام الموقع أيضاً لإرسال إشعارات أوقات الصلاة في الأوقات المناسبة حتى عند السفر.")
               .setPositiveButton("السماح", (dialog, which) -> {
                   ActivityCompat.requestPermissions(this,
                           new String[]{Manifest.permission.ACCESS_FINE_LOCATION,
                                   Manifest.permission.ACCESS_COARSE_LOCATION},
                           LOCATION_PERMISSION_REQUEST);
               })
               .setNegativeButton("رفض", (dialog, which) -> {
                   showToast("سيتم استخدام موقع افتراضي (الرياض) لحساب أوقات الصلاة");
                   calculateDefaultPrayerTimes();
               })
               .setCancelable(false)
               .show();
    }

    private void getCurrentLocation() {
        try {
            LocationManager locationManager = (LocationManager) getSystemService(Context.LOCATION_SERVICE);
            if (locationManager != null &&
                ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {

                Location location = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
                if (location != null) {
                    latitude = location.getLatitude();
                    longitude = location.getLongitude();

                    // تحديد اسم المدينة من الإحداثيات
                    getCityNameFromCoordinates(latitude, longitude);

                } else {
                    // طلب تحديث الموقع
                    locationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER,
                            1000, 1, new LocationListener() {
                        @Override
                        public void onLocationChanged(Location location) {
                            latitude = location.getLatitude();
                            longitude = location.getLongitude();

                            // تحديد اسم المدينة من الإحداثيات
                            getCityNameFromCoordinates(latitude, longitude);

                            locationManager.removeUpdates(this);
                        }

                        @Override
                        public void onStatusChanged(String provider, int status, Bundle extras) {}

                        @Override
                        public void onProviderEnabled(String provider) {}

                        @Override
                        public void onProviderDisabled(String provider) {}
                    });
                }
            }
        } catch (Exception e) {
            // استخدام الموقع الافتراضي
            calculatePrayerTimes();
        }
    }

    /**
     * تحديد اسم المدينة من الإحداثيات باستخدام Geocoding
     */
    private void getCityNameFromCoordinates(double lat, double lng) {
        try {
            android.location.Geocoder geocoder = new android.location.Geocoder(this, Locale.getDefault());
            java.util.List<android.location.Address> addresses = geocoder.getFromLocation(lat, lng, 1);

            if (addresses != null && !addresses.isEmpty()) {
                android.location.Address address = addresses.get(0);

                // محاولة الحصول على اسم المدينة بطرق مختلفة
                String cityName = address.getLocality(); // المدينة
                if (cityName == null || cityName.isEmpty()) {
                    cityName = address.getSubAdminArea(); // المنطقة الفرعية
                }
                if (cityName == null || cityName.isEmpty()) {
                    cityName = address.getAdminArea(); // المنطقة الإدارية
                }
                if (cityName == null || cityName.isEmpty()) {
                    cityName = address.getCountryName(); // البلد
                }

                if (cityName != null && !cityName.isEmpty()) {
                    currentCity = cityName;
                    showToast(getString(R.string.location_determined_message) + ": " + translateCityName(currentCity));
                } else {
                    currentCity = getString(R.string.current_location_fallback);
                    showToast(getString(R.string.location_determined_message));
                }
            } else {
                currentCity = getString(R.string.current_location_fallback);
                showToast(getString(R.string.location_determined_message));
            }

        } catch (Exception e) {
            // في حالة فشل Geocoding، استخدم اسم عام
            currentCity = getString(R.string.current_location_fallback);
            showToast(getString(R.string.location_determined_message));
        }

        // بعد تحديد اسم المدينة، احسب أوقات الصلاة
        calculatePrayerTimes();
    }

    private void calculatePrayerTimes() {
        // عرض رسالة تحميل
        String loadingText = getString(R.string.loading_prayer_times);
        tvFajr.setText(loadingText);
        tvSunrise.setText(loadingText);
        tvDhuhr.setText(loadingText);
        tvAsr.setText(loadingText);
        tvMaghrib.setText(loadingText);
        tvIsha.setText(loadingText);
        tvNextPrayer.setText(getString(R.string.determining_next_prayer));

        // استخدام Google Prayer Times API الحقيقي
        GooglePrayerTimesAPI googleAPI = new GooglePrayerTimesAPI(this);
        googleAPI.getPrayerTimes(latitude, longitude, currentCity, new GooglePrayerTimesAPI.PrayerTimesCallback() {
            @Override
            public void onSuccess(GooglePrayerTimesAPI.PrayerTimesData data) {
                runOnUiThread(() -> {
                    updatePrayerTimesUI(data);
                });
            }

            @Override
            public void onError(String error) {
                runOnUiThread(() -> {
                    // في حالة فشل API، استخدم الحساب المحلي كبديل
                    calculateLocalPrayerTimes();
                    showToast("تم استخدام حساب محلي لأوقات الصلاة: " + error);
                });
            }
        });
    }

    private void updatePrayerTimesUI(GooglePrayerTimesAPI.PrayerTimesData data) {
        tvFajr.setText(convertTimeFormat(data.fajr));
        tvSunrise.setText(convertTimeFormat(data.sunrise));
        tvDhuhr.setText(convertTimeFormat(data.dhuhr));
        tvAsr.setText(convertTimeFormat(data.asr));
        tvMaghrib.setText(convertTimeFormat(data.maghrib));
        tvIsha.setText(convertTimeFormat(data.isha));
        tvNextPrayer.setText(data.nextPrayer);

        // عرض اسم المدينة مع الترجمة
        tvCityName.setText(translateCityName(data.cityName));

        // عرض الوقت الحالي والتاريخ الهجري إذا كان متاحاً
        if (data.currentTime != null) {
            tvCurrentTime.setText(convertTimeFormat(data.currentTime));
        }

        if (data.hijriDate != null && !data.hijriDate.isEmpty()) {
            tvCurrentDate.setText(getString(R.string.hijri_date_label) + " " + data.hijriDate);
        }

        // حفظ أوقات الصلاة وجدولة الإشعارات
        savePrayerTimesAndScheduleNotifications(data);
    }

    /**
     * حفظ أوقات الصلاة وجدولة الإشعارات
     */
    private void savePrayerTimesAndScheduleNotifications(GooglePrayerTimesAPI.PrayerTimesData data) {
        try {
            // حفظ أوقات الصلاة في SharedPreferences
            android.content.SharedPreferences prefs = getSharedPreferences("prayer_times", MODE_PRIVATE);
            android.content.SharedPreferences.Editor editor = prefs.edit();

            editor.putString("fajr", data.fajr);
            editor.putString("sunrise", data.sunrise);
            editor.putString("dhuhr", data.dhuhr);
            editor.putString("asr", data.asr);
            editor.putString("maghrib", data.maghrib);
            editor.putString("isha", data.isha);
            editor.putString("city_name", data.cityName);
            editor.apply();

            // جدولة إشعارات أوقات الصلاة
            PrayerNotificationService notificationService = new PrayerNotificationService(this);

            String[] prayerTimes = {data.fajr, data.sunrise, data.dhuhr, data.asr, data.maghrib, data.isha};
            notificationService.schedulePrayerNotifications(prayerTimes, data.cityName);

            // إرسال إشعار اختبار في المرة الأولى
            android.content.SharedPreferences settingsPrefs = getSharedPreferences("prayer_settings", MODE_PRIVATE);
            boolean firstTime = settingsPrefs.getBoolean("first_time_notifications", true);

            if (firstTime) {
                notificationService.sendTestNotification();
                settingsPrefs.edit().putBoolean("first_time_notifications", false).apply();
            }

        } catch (Exception e) {
            android.util.Log.e("PrayerTimesActivity", "Error saving prayer times or scheduling notifications", e);
        }
    }

    private void showToast(String message) {
        // استخدام Toast بسيط
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_LONG).show();
    }

    /**
     * تحويل تنسيق الوقت حسب اللغة المختارة
     */
    private String convertTimeFormat(String time) {
        try {
            SharedPreferences prefs = getSharedPreferences("language_prefs", Context.MODE_PRIVATE);
            boolean isArabic = prefs.getBoolean("is_arabic", true);

            if (isArabic) {
                // تحويل AM/PM الإنجليزية إلى العربية
                return time.replace("AM", "ص").replace("PM", "م");
            } else {
                // تحويل العربية إلى الإنجليزية إذا لزم الأمر
                return time.replace("ص", "AM").replace("م", "PM");
            }
        } catch (Exception e) {
            return time;
        }
    }

    /**
     * ترجمة اسم المدينة حسب اللغة المختارة
     */
    private String translateCityName(String cityName) {
        try {
            SharedPreferences prefs = getSharedPreferences("language_prefs", Context.MODE_PRIVATE);
            boolean isArabic = prefs.getBoolean("is_arabic", true);

            if (cityName == null || cityName.isEmpty()) {
                return getString(R.string.unknown_city);
            }

            // قاموس ترجمة المدن الشائعة
            if (isArabic) {
                // إذا كانت اللغة عربية، تحويل الأسماء الإنجليزية إلى عربية
                switch (cityName.toLowerCase()) {
                    case "riyadh": return "الرياض";
                    case "mecca": case "makkah": return "مكة المكرمة";
                    case "medina": case "madinah": return "المدينة المنورة";
                    case "jeddah": return "جدة";
                    case "dammam": return "الدمام";
                    case "khobar": return "الخبر";
                    case "taif": return "الطائف";
                    case "abha": return "أبها";
                    case "tabuk": return "تبوك";
                    case "hail": return "حائل";
                    case "buraidah": return "بريدة";
                    case "khamis mushait": return "خميس مشيط";
                    case "hofuf": return "الهفوف";
                    case "mubarraz": return "المبرز";
                    case "najran": return "نجران";
                    case "yanbu": return "ينبع";
                    case "jubail": return "الجبيل";
                    case "dhahran": return "الظهران";
                    case "qatif": return "القطيف";
                    case "arar": return "عرعر";
                    case "sakaka": return "سكاكا";
                    case "jazan": return "جازان";
                    case "bisha": return "بيشة";
                    case "al-bahah": return "الباحة";
                    case "your current location": return "موقعك الحالي";
                    case "unknown city": return "مدينة غير معروفة";
                    default: return cityName; // إرجاع الاسم كما هو إذا لم يوجد ترجمة
                }
            } else {
                // إذا كانت اللغة إنجليزية، تحويل الأسماء العربية إلى إنجليزية
                switch (cityName) {
                    case "الرياض": return "Riyadh";
                    case "مكة المكرمة": case "مكة": return "Mecca";
                    case "المدينة المنورة": case "المدينة": return "Medina";
                    case "جدة": return "Jeddah";
                    case "الدمام": return "Dammam";
                    case "الخبر": return "Khobar";
                    case "الطائف": return "Taif";
                    case "أبها": return "Abha";
                    case "تبوك": return "Tabuk";
                    case "حائل": return "Hail";
                    case "بريدة": return "Buraidah";
                    case "خميس مشيط": return "Khamis Mushait";
                    case "الهفوف": return "Hofuf";
                    case "المبرز": return "Mubarraz";
                    case "نجران": return "Najran";
                    case "ينبع": return "Yanbu";
                    case "الجبيل": return "Jubail";
                    case "الظهران": return "Dhahran";
                    case "القطيف": return "Qatif";
                    case "عرعر": return "Arar";
                    case "سكاكا": return "Sakaka";
                    case "جازان": return "Jazan";
                    case "بيشة": return "Bisha";
                    case "الباحة": return "Al-Bahah";
                    case "موقعك الحالي": return "Your Current Location";
                    case "مدينة غير معروفة": return "Unknown City";
                    default: return cityName; // إرجاع الاسم كما هو إذا لم يوجد ترجمة
                }
            }
        } catch (Exception e) {
            return cityName;
        }
    }

    private void calculateLocalPrayerTimes() {
        Calendar calendar = Calendar.getInstance();
        int dayOfYear = calendar.get(Calendar.DAY_OF_YEAR);

        // حساب أوقات الصلاة باستخدام خوارزمية محسنة كبديل
        PrayerTimes prayerTimes = new PrayerTimes(latitude, longitude, dayOfYear);

        // تطبيق الترجمة على الأوقات
        tvFajr.setText(convertTimeFormat(prayerTimes.getFajr()));
        tvSunrise.setText(convertTimeFormat(prayerTimes.getSunrise()));
        tvDhuhr.setText(convertTimeFormat(prayerTimes.getDhuhr()));
        tvAsr.setText(convertTimeFormat(prayerTimes.getAsr()));
        tvMaghrib.setText(convertTimeFormat(prayerTimes.getMaghrib()));
        tvIsha.setText(convertTimeFormat(prayerTimes.getIsha()));

        updateNextPrayer(prayerTimes);
    }

    private void updateCurrentTime() {
        // تحديث التاريخ والوقت الحالي
        Calendar calendar = Calendar.getInstance();

        // تحديد اللغة المختارة
        SharedPreferences prefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = prefs.getBoolean("is_arabic", true);

        Locale currentLocale = isArabic ? new Locale("ar") : Locale.ENGLISH;

        SimpleDateFormat dateFormat = new SimpleDateFormat("EEEE، dd MMMM yyyy", currentLocale);
        SimpleDateFormat timeFormat = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

        String currentDate = dateFormat.format(calendar.getTime());
        String currentTime = timeFormat.format(calendar.getTime());

        // تحويل AM/PM حسب اللغة
        if (isArabic) {
            currentTime = currentTime.replace("AM", "ص").replace("PM", "م");
        }

        tvCurrentDate.setText(currentDate);
        tvCurrentTime.setText(currentTime);

        // تحديث رسالة "جاري تحديد الصلاة القادمة"
        tvNextPrayer.setText(getString(R.string.determining_next_prayer));
    }

    private void updateNextPrayer(PrayerTimes prayerTimes) {
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);
        int currentMinute = now.get(Calendar.MINUTE);
        int currentTimeInMinutes = currentHour * 60 + currentMinute;

        String[] prayers = {
            getString(R.string.fajr),
            getString(R.string.sunrise),
            getString(R.string.dhuhr),
            getString(R.string.asr),
            getString(R.string.maghrib),
            getString(R.string.isha)
        };
        String[] times = {
            convertTimeFormat(prayerTimes.getFajr()),
            convertTimeFormat(prayerTimes.getSunrise()),
            convertTimeFormat(prayerTimes.getDhuhr()),
            convertTimeFormat(prayerTimes.getAsr()),
            convertTimeFormat(prayerTimes.getMaghrib()),
            convertTimeFormat(prayerTimes.getIsha())
        };

        // تحديد اللغة المختارة
        SharedPreferences prefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = prefs.getBoolean("is_arabic", true);

        String nextPrayer = getString(R.string.fajr);
        String nextTime = times[0] + " " + getString(R.string.tomorrow_indicator);

        for (int i = 0; i < times.length; i++) {
            try {
                // إزالة AM/PM والمسافات لتحليل الوقت (دعم العربية والإنجليزية)
                String cleanTime = times[i].replaceAll("[صمAMPM\\s]", "").trim();
                String[] timeParts = cleanTime.split(":");

                if (timeParts.length >= 2) {
                    int hours = Integer.parseInt(timeParts[0]);
                    int minutes = Integer.parseInt(timeParts[1]);

                    // تحويل إلى تنسيق 24 ساعة (دعم العربية والإنجليزية)
                    if ((times[i].contains("م") || times[i].contains("PM")) && hours != 12) {
                        hours += 12;
                    } else if ((times[i].contains("ص") || times[i].contains("AM")) && hours == 12) {
                        hours = 0;
                    }

                    int prayerTimeInMinutes = hours * 60 + minutes;

                    if (currentTimeInMinutes < prayerTimeInMinutes) {
                        nextPrayer = prayers[i];
                        nextTime = times[i];
                        break;
                    }
                }
            } catch (Exception e) {
                // تجاهل الأخطاء والمتابعة للوقت التالي
                continue;
            }
        }

        tvNextPrayer.setText(getString(R.string.next_prayer_label) + " " + nextPrayer + " - " + nextTime);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == LOCATION_PERMISSION_REQUEST) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showToast("تم منح إذن الموقع - جاري تحديث أوقات الصلاة...");
                getCurrentLocation();

                // طلب إذن الموقع في الخلفية لإشعارات أوقات الصلاة
                requestBackgroundLocationPermissionIfNeeded();
            } else {
                // استخدام الموقع الافتراضي
                calculatePrayerTimes();
                showToast("سيتم استخدام موقع افتراضي (الرياض) لحساب أوقات الصلاة");
            }
        } else if (requestCode == LOCATION_PERMISSION_REQUEST + 1) { // Background location permission
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                showToast("تم منح إذن الموقع في الخلفية - سيتم تحديث إشعارات أوقات الصلاة تلقائياً عند السفر");
            } else {
                showToast("إذن الموقع في الخلفية مطلوب لتحديث إشعارات أوقات الصلاة تلقائياً عند السفر");
            }
        }
    }

    /**
     * طلب إذن الموقع في الخلفية إذا لم يكن موجوداً (لإشعارات أوقات الصلاة)
     */
    private void requestBackgroundLocationPermissionIfNeeded() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            if (!hasBackgroundLocationPermission()) {
                showBackgroundLocationPermissionDialog();
            }
        }
    }

    /**
     * فحص إذن الموقع في الخلفية
     */
    private boolean hasBackgroundLocationPermission() {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_BACKGROUND_LOCATION)
                   == PackageManager.PERMISSION_GRANTED;
        }
        return true; // الإصدارات الأقدم لا تحتاج إذن منفصل للخلفية
    }

    /**
     * عرض رسالة توضيحية لطلب إذن الموقع في الخلفية
     */
    private void showBackgroundLocationPermissionDialog() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(this);
        builder.setTitle("إذن الموقع في الخلفية")
               .setMessage("لضمان دقة إشعارات أوقات الصلاة عند السفر أو تغيير الموقع، يحتاج التطبيق للوصول إلى موقعك حتى عندما يكون مغلقاً.\n\n" +
                          "هذا يضمن تحديث أوقات الصلاة تلقائياً حسب موقعك الجديد.")
               .setPositiveButton("السماح", (dialog, which) -> {
                   ActivityCompat.requestPermissions(this,
                           new String[]{Manifest.permission.ACCESS_BACKGROUND_LOCATION},
                           LOCATION_PERMISSION_REQUEST + 1);
               })
               .setNegativeButton("لاحقاً", (dialog, which) -> {
                   showToast("يمكنك تفعيل هذا الإذن لاحقاً من إعدادات التطبيق");
               })
               .setCancelable(true)
               .show();
    }

    private void applyLanguageSettings() {
        SharedPreferences prefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
        boolean isArabic = prefs.getBoolean("is_arabic", true);
        setLocale(isArabic ? "ar" : "en");
    }

    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }

    // فئة مساعدة لحساب أوقات الصلاة
    private static class PrayerTimes {
        private String fajr, sunrise, dhuhr, asr, maghrib, isha;

        public PrayerTimes(double latitude, double longitude, int dayOfYear) {
            calculateTimes(latitude, longitude, dayOfYear);
        }

        private void calculateTimes(double latitude, double longitude, int dayOfYear) {
            // حساب مبسط لأوقات الصلاة
            // في التطبيق الحقيقي يجب استخدام مكتبة متخصصة

            // حساب زاوية الشمس
            double declination = 23.45 * Math.sin(Math.toRadians(360 * (284 + dayOfYear) / 365.0));
            double latRad = Math.toRadians(latitude);
            double declRad = Math.toRadians(declination);

            // حساب وقت الشروق والغروب
            double hourAngle = Math.toDegrees(Math.acos(-Math.tan(latRad) * Math.tan(declRad)));

            double sunriseTime = 12 - hourAngle / 15.0;
            double sunsetTime = 12 + hourAngle / 15.0;

            // حساب أوقات الصلاة
            fajr = formatTime(sunriseTime - 1.5);
            sunrise = formatTime(sunriseTime);
            dhuhr = formatTime(12.0 + longitude / 15.0);
            asr = formatTime(dhuhr.equals("12:00") ? 15.5 : parseTime(dhuhr) + 3.5);
            maghrib = formatTime(sunsetTime);
            isha = formatTime(sunsetTime + 1.5);
        }

        private String formatTime(double time) {
            int hours = (int) time;
            int minutes = (int) ((time - hours) * 60);

            // تحويل إلى تنسيق 12 ساعة مع AM/PM
            try {
                String timeStr = String.format(Locale.ENGLISH, "%02d:%02d", hours, minutes);
                SimpleDateFormat input = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
                SimpleDateFormat output = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

                String formattedTime = output.format(input.parse(timeStr));
                // سيتم تطبيق الترجمة في الكود المستدعي
                return formattedTime;
            } catch (Exception e) {
                return String.format(Locale.ENGLISH, "%02d:%02d", hours, minutes);
            }
        }

        private double parseTime(String timeStr) {
            try {
                // إزالة AM/PM والمسافات
                String cleanTime = timeStr.replaceAll("[صم\\s]", "").trim();
                String[] parts = cleanTime.split(":");

                if (parts.length >= 2) {
                    int hours = Integer.parseInt(parts[0]);
                    int minutes = Integer.parseInt(parts[1]);

                    // تحويل إلى تنسيق 24 ساعة إذا كان يحتوي على PM/م
                    if (timeStr.contains("م") && hours != 12) {
                        hours += 12;
                    } else if (timeStr.contains("ص") && hours == 12) {
                        hours = 0;
                    }

                    return hours + minutes / 60.0;
                }
                return 12.0; // قيمة افتراضية
            } catch (Exception e) {
                return 12.0; // قيمة افتراضية في حالة الخطأ
            }
        }

        public String getFajr() { return fajr; }
        public String getSunrise() { return sunrise; }
        public String getDhuhr() { return dhuhr; }
        public String getAsr() { return asr; }
        public String getMaghrib() { return maghrib; }
        public String getIsha() { return isha; }
    }
}

package com.qurany2019.quranyapp;

import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.material.appbar.CollapsingToolbarLayout;

import java.util.ArrayList;

public class ModernRecitesNameActivity extends AppCompatActivity implements ModernRecitersAdapter.OnReciterClickListener {
    
    // المكونات الجديدة
    private RecyclerView recitersRecyclerView;
    private ModernRecitersAdapter modernAdapter;
    private EditText searchEditText;
    private TextView recitersCountText;
    private CollapsingToolbarLayout collapsingToolbar;
    private Toolbar toolbar;
    private ImageView quranIcon;
    private AdView adView;
    
    // البيانات
    private ArrayList<AuthorClass> recitersList;
    private ArrayList<AuthorClass> originalRecitersList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_recites_name);

        initViews();
        setupToolbar();
        setupRecyclerView();
        setupSearch();
        loadRecitersData();
        setupAds();
        animateInitialElements();
    }

    private void initViews() {
        // Toolbar & AppBar
        toolbar = findViewById(R.id.toolbar);
        collapsingToolbar = findViewById(R.id.collapsingToolbar);
        recitersCountText = findViewById(R.id.recitersCountText);
        quranIcon = findViewById(R.id.quranIcon);

        // RecyclerView & Search
        recitersRecyclerView = findViewById(R.id.recitersRecyclerView);
        searchEditText = findViewById(R.id.searchEditText);

        // Ads
        adView = findViewById(R.id.adView);
    }

    private void setupToolbar() {
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }

        // النقر على زر الرجوع
        toolbar.setNavigationOnClickListener(v -> {
            animateBackButton();
            onBackPressed();
        });
    }

    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        recitersRecyclerView.setLayoutManager(layoutManager);

        // إضافة انيميشن للعناصر
        recitersRecyclerView.setItemAnimator(new androidx.recyclerview.widget.DefaultItemAnimator());
    }

    private void setupSearch() {
        searchEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (modernAdapter != null) {
                    modernAdapter.filter(s.toString());
                }
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }

    private void loadRecitersData() {
        // تحميل قائمة القراء
        LnaguageClass lc = new LnaguageClass();
        recitersList = lc.AutherList();
        originalRecitersList = new ArrayList<>(recitersList);

        // تحديث عدد القراء
        recitersCountText.setText(recitersList.size() + " " + getString(R.string.quran_reader_title));

        // إعداد الـ Adapter
        modernAdapter = new ModernRecitersAdapter(this, recitersList);
        modernAdapter.setOnReciterClickListener(this);
        recitersRecyclerView.setAdapter(modernAdapter);

        // انيميشن ظهور القائمة
        animateRecyclerView();
    }

    private void setupAds() {
        try {
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);
        } catch (Exception e) {
            // تجاهل أخطاء الإعلانات
        }
    }

    // تنفيذ واجهة ModernRecitersAdapter.OnReciterClickListener
    @Override
    public void onReciterClick(AuthorClass reciter, int position) {
        // الانتقال إلى صفحة السور
        navigateToSurahList(reciter.ServerName);
    }

    @Override
    public void onPlayClick(AuthorClass reciter, int position) {
        // نفس الوظيفة - الانتقال إلى صفحة السور
        navigateToSurahList(reciter.ServerName);
    }

    private void navigateToSurahList(String reciterName) {
        try {
            // التحقق من وجود ModernAyaListActivity أولاً
            Intent intent = new Intent(this, ModernAyaListActivity.class);
            intent.putExtra("RecitesName", reciterName);
            startActivity(intent);
        } catch (Exception e) {
            // في حالة عدم وجود ModernAyaListActivity، استخدم AyaList العادي
            try {
                Intent intent = new Intent(this, AyaList.class);
                intent.putExtra("RecitesName", reciterName);
                startActivity(intent);
            } catch (Exception ex) {
                // في حالة فشل كلاهما
                ex.printStackTrace();
            }
        }
    }

    // انيميشن العناصر الأولية
    private void animateInitialElements() {
        // انيميشن أيقونة القرآن
        ObjectAnimator iconRotation = ObjectAnimator.ofFloat(quranIcon, "rotation", 0f, 360f);
        iconRotation.setDuration(1000);
        iconRotation.setInterpolator(new AccelerateDecelerateInterpolator());
        iconRotation.start();

        // انيميشن ظهور عدد القراء
        ObjectAnimator countFade = ObjectAnimator.ofFloat(recitersCountText, "alpha", 0f, 1f);
        countFade.setDuration(800);
        countFade.setStartDelay(300);
        countFade.start();
    }

    // انيميشن ظهور RecyclerView
    private void animateRecyclerView() {
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(recitersRecyclerView, "alpha", 0f, 1f);
        ObjectAnimator slideUp = ObjectAnimator.ofFloat(recitersRecyclerView, "translationY", 100f, 0f);

        fadeIn.setDuration(500);
        slideUp.setDuration(500);

        fadeIn.setInterpolator(new AccelerateDecelerateInterpolator());
        slideUp.setInterpolator(new AccelerateDecelerateInterpolator());

        fadeIn.start();
        slideUp.start();
    }

    // انيميشن زر الرجوع
    private void animateBackButton() {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(toolbar, "scaleX", 1f, 0.95f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(toolbar, "scaleY", 1f, 0.95f, 1f);

        scaleX.setDuration(150);
        scaleY.setDuration(150);

        scaleX.start();
        scaleY.start();
    }

    @Override
    public void onBackPressed() {
        // إضافة انيميشن عند الرجوع
        animateExit();
        super.onBackPressed();
    }

    private void animateExit() {
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(recitersRecyclerView, "alpha", 1f, 0f);
        fadeOut.setDuration(300);
        fadeOut.start();
    }

    @Override
    protected void onDestroy() {
        if (adView != null) {
            adView.destroy();
        }
        super.onDestroy();
    }
}

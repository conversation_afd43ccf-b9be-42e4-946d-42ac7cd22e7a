package com.qurany2019.quranyapp;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.location.Location;
import android.location.LocationManager;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.annotation.NonNull;

import com.qurany2019.quranyapp.utils.PermissionHelper;
import com.qurany2019.quranyapp.utils.AzkarDataInitializer;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class MainActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // تطبيق إعدادات اللغة والوضع الليلي المحفوظة
        applyLanguageSettings();
        applyDarkModeSettings();

        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main_basic);

        Log.d("MainActivity", "onCreate called");

        // إعداد شريط التنقل السفلي
        setupBottomNavigation();

        // إعداد البطاقات الرئيسية
        setupMainCards();

        // إعداد زر الوضع الليلي
        setupNightModeToggle();

        // تحديث أوقات الصلاة والوقت الحالي
        loadPrayerTimes();
        updateCurrentTime();

        // تحديث آخر سورة تم قراءتها
        updateLastReadSurah();

        // لا نطلب الأذونات هنا - سيتم طلبها في صفحة مواقيت الصلاة عند الحاجة

        // تهيئة البيانات الافتراضية
        AzkarDataInitializer.initializeDefaultData(this);
    }

    // تطبيق إعدادات اللغة المحفوظة
    private void applyLanguageSettings() {
        try {
            SharedPreferences langPrefs = getSharedPreferences("language_prefs", MODE_PRIVATE);
            boolean isArabic = langPrefs.getBoolean("is_arabic", true);
            setLocale(isArabic ? "ar" : "en");
        } catch (Exception e) {
            Log.e("MainActivity", "Error applying language settings: " + e.getMessage());
        }
    }

    private void setLocale(String languageCode) {
        Locale locale = new Locale(languageCode);
        Locale.setDefault(locale);
        Configuration config = new Configuration();
        config.locale = locale;
        getResources().updateConfiguration(config, getResources().getDisplayMetrics());
    }

    // تطبيق إعدادات الوضع الليلي المحفوظة
    private void applyDarkModeSettings() {
        try {
            SharedPreferences prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
            boolean isDarkMode = prefs.getBoolean("dark_mode", false);

            if (isDarkMode) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error applying dark mode: " + e.getMessage());
        }
    }

    private void setupBottomNavigation() {
        // إعداد أيقونة الرئيسية
        LinearLayout homeNavItem = findViewById(R.id.homeNavItem);
        if (homeNavItem != null) {
            homeNavItem.setOnClickListener(v -> {
                // البقاء في الصفحة الرئيسية
                Toast.makeText(this, "أنت في الصفحة الرئيسية", Toast.LENGTH_SHORT).show();
            });
        }

        // إعداد أيقونة القرآن
        LinearLayout quranNavItem = findViewById(R.id.quranNavItem);
        if (quranNavItem != null) {
            quranNavItem.setOnClickListener(v -> {
                try {
                    Intent intent = new Intent(this, quranActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }

        // إعداد أيقونة الاستماع
        LinearLayout listenNavItem = findViewById(R.id.listenNavItem);
        if (listenNavItem != null) {
            listenNavItem.setOnClickListener(v -> {
                try {
                    // استخدام النشاط الجديد للاختبار
                    Intent intent = new Intent(this, TestRecitersActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // في حالة فشل النشاط الجديد، استخدم القديم
                    try {
                        Intent fallbackIntent = new Intent(this, RecitesName.class);
                        startActivity(fallbackIntent);
                    } catch (Exception ex) {
                        // تجاهل الخطأ
                    }
                }
            });
        }

        // إعداد أيقونة الأذكار
        LinearLayout azkarNavItem = findViewById(R.id.azkarNavItem);
        if (azkarNavItem != null) {
            azkarNavItem.setOnClickListener(v -> {
                try {
                    Intent intent = new Intent(this, AzkarActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }

        // إعداد أيقونة المزيد
        LinearLayout moreNavItem = findViewById(R.id.moreNavItem);
        if (moreNavItem != null) {
            moreNavItem.setOnClickListener(v -> {
                Log.d("MainActivity", "More button clicked!");
                try {
                    Intent intent = new Intent(this, MoreActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    Log.e("MainActivity", "Error starting MoreActivity: " + e.getMessage());
                }
            });
        } else {
            Log.e("MainActivity", "moreNavItem not found!");
        }
    }

    private void setupMainCards() {
        // إعداد بطاقة القرآن الكريم
        RelativeLayout quranSection = findViewById(R.id.quranSection);
        if (quranSection != null) {
            quranSection.setOnClickListener(v -> {
                animateCardClick(v);
                try {
                    Intent intent = new Intent(this, quranActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }

        // إعداد بطاقة الاستماع
        RelativeLayout recitersSection = findViewById(R.id.recitersSection);
        if (recitersSection != null) {
            recitersSection.setOnClickListener(v -> {
                animateCardClick(v);
                try {
                    Intent intent = new Intent(this, RecitesName.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }

        // إعداد بطاقة الأذكار
        LinearLayout azkarSection = findViewById(R.id.azkarSection);
        if (azkarSection != null) {
            azkarSection.setOnClickListener(v -> {
                animateCardClick(v);
                try {
                    Intent intent = new Intent(this, AzkarActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }



        // إعداد بطاقة أوقات الصلاة
        LinearLayout prayerTimesSection = findViewById(R.id.prayerTimesSection);
        if (prayerTimesSection != null) {
            prayerTimesSection.setOnClickListener(v -> {
                animateCardClick(v);
                try {
                    Intent intent = new Intent(this, PrayerTimesActivity.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }

        // إعداد بطاقة السبحة
        LinearLayout tasbihSection = findViewById(R.id.tasbihSection);
        if (tasbihSection != null) {
            tasbihSection.setOnClickListener(v -> {
                animateCardClick(v);
                try {
                    Intent intent = new Intent(this, Tazker.class);
                    startActivity(intent);
                } catch (Exception e) {
                    // تجاهل الخطأ
                }
            });
        }
    }

    // دالة تأثير الحركة عند النقر على البطاقات
    private void animateCardClick(View view) {
        // تأثير التصغير والتكبير مع تغيير الشفافية
        view.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .alpha(0.8f)
                .setDuration(100)
                .withEndAction(() -> {
                    view.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .alpha(1.0f)
                            .setDuration(100)
                            .start();
                })
                .start();
    }

    // إعداد زر الوضع الليلي
    private void setupNightModeToggle() {
        LinearLayout nightModeToggle = findViewById(R.id.nightModeToggle);
        ImageView nightModeIcon = findViewById(R.id.nightModeIcon);
        TextView nightModeText = findViewById(R.id.nightModeText);

        if (nightModeToggle != null) {
            // تحديث الواجهة حسب الوضع الحالي
            updateNightModeUI(nightModeIcon, nightModeText);

            nightModeToggle.setOnClickListener(v -> {
                animateCardClick(v);
                toggleNightMode(nightModeIcon, nightModeText);
            });
        }
    }

    // دالة تبديل الوضع الليلي
    private void toggleNightMode(ImageView icon, TextView text) {
        SharedPreferences prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        boolean currentMode = prefs.getBoolean("dark_mode", false);
        boolean newMode = !currentMode;

        // حفظ الوضع الجديد
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("dark_mode", newMode);
        editor.apply();

        // تطبيق الوضع الجديد فوراً
        if (newMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        // رسالة تأكيد مترجمة
        String message = newMode ? getString(R.string.night_mode_enabled) : getString(R.string.night_mode_disabled);
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

        // إعادة إنشاء النشاط لتطبيق الثيم الجديد
        recreate();
    }

    // دالة تحديث واجهة زر الوضع الليلي المدمجة مع الترجمة
    private void updateNightModeUI(ImageView icon, TextView text) {
        SharedPreferences prefs = getSharedPreferences("app_settings", MODE_PRIVATE);
        boolean isDarkMode = prefs.getBoolean("dark_mode", false);

        if (isDarkMode) {
            icon.setImageResource(R.drawable.ic_sun);
            text.setText(getString(R.string.night_mode_light));
            // تأثير حركي بسيط للأيقونة
            icon.animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(200)
                .withEndAction(() -> icon.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .start())
                .start();
        } else {
            icon.setImageResource(R.drawable.ic_moon);
            text.setText(getString(R.string.night_mode_dark));
            // تأثير حركي بسيط للأيقونة
            icon.animate()
                .scaleX(1.2f)
                .scaleY(1.2f)
                .setDuration(200)
                .withEndAction(() -> icon.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(200)
                    .start())
                .start();
        }
    }

    // تحديث أوقات الصلاة من البيانات المحفوظة (متزامن مع صفحة مواقيت الصلاة)
    private void loadPrayerTimes() {
        try {
            // قراءة أوقات الصلاة المحفوظة من صفحة مواقيت الصلاة
            SharedPreferences prefs = getSharedPreferences("prayer_times", MODE_PRIVATE);

            String fajr = prefs.getString("fajr", "");
            String dhuhr = prefs.getString("dhuhr", "");
            String asr = prefs.getString("asr", "");
            String maghrib = prefs.getString("maghrib", "");
            String isha = prefs.getString("isha", "");

            // إذا كانت هناك أوقات محفوظة، استخدمها مباشرة
            if (!fajr.isEmpty() && !dhuhr.isEmpty()) {
                updatePrayerTimeViews(fajr, dhuhr, asr, maghrib, isha);
            } else {
                // إذا لم تكن هناك أوقات محفوظة، عرض رسالة للمستخدم لتحديث الأوقات
                updatePrayerTimeViews("--:--", "--:--", "--:--", "--:--", "--:--");

                // يمكن إضافة Toast لتوجيه المستخدم
                // Toast.makeText(this, "يرجى تحديث أوقات الصلاة من صفحة مواقيت الصلاة", Toast.LENGTH_LONG).show();
            }

        } catch (Exception e) {
            Log.e("MainActivity", "Error loading prayer times: " + e.getMessage());
            // في حالة الخطأ، عرض أوقات فارغة
            updatePrayerTimeViews("--:--", "--:--", "--:--", "--:--", "--:--");
        }
    }

    // تحديث عرض أوقات الصلاة في الواجهة (نسخة من MainActivitySimple)
    private void updatePrayerTimeViews(String fajr, String dhuhr, String asr, String maghrib, String isha) {
        try {
            // تحديث أوقات الصلاة باستخدام المعرفات المباشرة
            TextView fajrTime = findViewById(R.id.fajrTime);
            TextView dhuhrTime = findViewById(R.id.dhuhrTime);
            TextView asrTime = findViewById(R.id.asrTime);
            TextView maghribTime = findViewById(R.id.maghribTime);
            TextView ishaTime = findViewById(R.id.ishaTime);

            // تحويل الأوقات إلى تنسيق 12 ساعة مع تحويل الأرقام
            if (fajrTime != null) fajrTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(fajr)));
            if (dhuhrTime != null) dhuhrTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(dhuhr)));
            if (asrTime != null) asrTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(asr)));
            if (maghribTime != null) maghribTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(maghrib)));
            if (ishaTime != null) ishaTime.setText(convertArabicToEnglishNumbers(convertTo12HourFormat(isha)));
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    // دالة لتحويل الوقت إلى تنسيق 12 ساعة (نسخة من MainActivitySimple)
    private String convertTo12HourFormat(String time) {
        if (time == null || time.isEmpty()) return time;

        try {
            // تطبيق الترجمة حسب اللغة المختارة
            android.content.SharedPreferences langPrefs = getSharedPreferences("language_prefs", android.content.Context.MODE_PRIVATE);
            boolean isArabic = langPrefs.getBoolean("is_arabic", true);

            // إذا كان الوقت يحتوي على AM/PM بالفعل، قم بالترجمة فقط
            if (time.contains("AM") || time.contains("PM")) {
                if (isArabic) {
                    return time.replace("AM", "ص").replace("PM", "م");
                } else {
                    return time;
                }
            }

            // إذا كان الوقت يحتوي على ص/م، تحويل للإنجليزية إذا لزم الأمر
            if (time.contains("ص") || time.contains("م")) {
                if (isArabic) {
                    return time; // الوقت بالعربية بالفعل
                } else {
                    return time.replace("ص", "AM").replace("م", "PM");
                }
            }

            // إذا كان الوقت بتنسيق 24 ساعة، قم بالتحويل
            String cleanTime = time.split(" ")[0];
            SimpleDateFormat input = new SimpleDateFormat("HH:mm", Locale.ENGLISH);
            SimpleDateFormat output = new SimpleDateFormat("h:mm a", Locale.ENGLISH);

            String formattedTime = output.format(input.parse(cleanTime));

            if (isArabic) {
                return formattedTime.replace("AM", "ص").replace("PM", "م");
            } else {
                return formattedTime;
            }
        } catch (Exception e) {
            // إذا فشل التحويل، إرجاع الوقت كما هو
            return time;
        }
    }

    // دالة لتحويل الأرقام العربية إلى إنجليزية (نسخة من MainActivitySimple)
    private String convertArabicToEnglishNumbers(String text) {
        if (text == null) return "";

        // تحويل الأرقام العربية-الهندية (٠١٢٣٤٥٦٧٨٩)
        String result = text.replace("٠", "0")
                           .replace("١", "1")
                           .replace("٢", "2")
                           .replace("٣", "3")
                           .replace("٤", "4")
                           .replace("٥", "5")
                           .replace("٦", "6")
                           .replace("٧", "7")
                           .replace("٨", "8")
                           .replace("٩", "9");

        // تحويل الأرقام الفارسية/الأردية (۰۱۲۳۴۵۶۷۸۹) إذا وجدت
        result = result.replace("۰", "0")
                      .replace("۱", "1")
                      .replace("۲", "2")
                      .replace("۳", "3")
                      .replace("۴", "4")
                      .replace("۵", "5")
                      .replace("۶", "6")
                      .replace("۷", "7")
                      .replace("۸", "8")
                      .replace("۹", "9");

        return result;
    }

    // تحديث الوقت الحالي (نسخة من MainActivitySimple)
    private void updateCurrentTime() {
        try {
            TextView currentTimeView = findViewById(R.id.currentTime);
            if (currentTimeView != null) {
                Calendar calendar = Calendar.getInstance();
                // استخدام تنسيق 12 ساعة مع AM/PM
                SimpleDateFormat timeFormat = new SimpleDateFormat("h:mm a", Locale.ENGLISH);
                String currentTime = timeFormat.format(calendar.getTime());

                // تطبيق الترجمة حسب اللغة المختارة
                android.content.SharedPreferences langPrefs = getSharedPreferences("language_prefs", android.content.Context.MODE_PRIVATE);
                boolean isArabic = langPrefs.getBoolean("is_arabic", true);

                if (isArabic) {
                    currentTime = currentTime.replace("AM", "ص").replace("PM", "م");
                }

                currentTimeView.setText(currentTime);
            }
        } catch (Exception e) {
            // تجاهل الأخطاء
        }
    }

    // طلب الأذونات المطلوبة
    private void requestRequiredPermissions() {
        // طلب إذن الإشعارات أولاً
        if (!PermissionHelper.hasNotificationPermission(this)) {
            PermissionHelper.requestNotificationPermission(this);
        } else {
            // إذا كان إذن الإشعارات موجود، اطلب إذن الموقع
            requestLocationPermissionIfNeeded();
        }
    }

    // طلب إذن الموقع إذا لم يكن موجوداً
    private void requestLocationPermissionIfNeeded() {
        if (!PermissionHelper.hasLocationPermission(this)) {
            PermissionHelper.requestLocationPermission(this);
        }
    }

    // طلب إذن الموقع في الخلفية إذا لم يكن موجوداً (لإشعارات أوقات الصلاة)
    private void requestBackgroundLocationPermissionIfNeeded() {
        if (!PermissionHelper.hasBackgroundLocationPermission(this)) {
            PermissionHelper.requestBackgroundLocationPermission(this);
        }
    }

    // معالجة نتائج طلب الأذونات
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        switch (requestCode) {
            case PermissionHelper.NOTIFICATION_PERMISSION_REQUEST_CODE:
                if (PermissionHelper.isPermissionGranted(grantResults)) {
                    Toast.makeText(this, "تم منح إذن الإشعارات", Toast.LENGTH_SHORT).show();
                    // بعد منح إذن الإشعارات، اطلب إذن الموقع
                    requestLocationPermissionIfNeeded();
                } else {
                    Toast.makeText(this, "إذن الإشعارات مطلوب لتلقي التذكيرات", Toast.LENGTH_LONG).show();
                    // حتى لو لم يتم منح إذن الإشعارات، اطلب إذن الموقع
                    requestLocationPermissionIfNeeded();
                }
                break;

            case PermissionHelper.LOCATION_PERMISSION_REQUEST_CODE:
                if (PermissionHelper.isPermissionGranted(grantResults)) {
                    // تحديث أوقات الصلاة بناءً على الموقع الجديد
                    loadPrayerTimes();
                    // طلب إذن الموقع في الخلفية لإشعارات أوقات الصلاة
                    requestBackgroundLocationPermissionIfNeeded();
                } else {
                    // عرض أوقات افتراضية
                    loadPrayerTimes();
                }
                break;

            case PermissionHelper.LOCATION_PERMISSION_REQUEST_CODE + 1: // Background location permission
                if (PermissionHelper.isPermissionGranted(grantResults)) {
                    Toast.makeText(this, "تم منح إذن الموقع في الخلفية - سيتم تحديث إشعارات أوقات الصلاة تلقائياً", Toast.LENGTH_LONG).show();
                } else {
                    Toast.makeText(this, "إذن الموقع في الخلفية مطلوب لتحديث إشعارات أوقات الصلاة تلقائياً عند السفر", Toast.LENGTH_LONG).show();
                }
                break;
        }
    }



    @Override
    protected void onResume() {
        super.onResume();
        // تحديث أوقات الصلاة والوقت الحالي عند العودة للصفحة
        loadPrayerTimes();
        updateCurrentTime();

        // تحديث آخر سورة تم قراءتها
        updateLastReadSurah();
    }

    // تحديث آخر سورة تم قراءتها
    private void updateLastReadSurah() {
        try {
            TextView lastReadSurahText = findViewById(R.id.lastReadSurah);
            if (lastReadSurahText != null) {
                // قراءة آخر سورة من SharedPreferences
                SharedPreferences prefs = getSharedPreferences("MyPrefsFile", MODE_PRIVATE);
                int lastSurahIndex = prefs.getInt("curSura", 0); // الافتراضي الفاتحة (0)

                // أسماء السور
                String[] surahNames = {
                    "الفاتحة", "البقرة", "آل عمران", "النساء", "المائدة", "الأنعام", "الأعراف", "الأنفال", "التوبة", "يونس",
                    "هود", "يوسف", "الرعد", "إبراهيم", "الحجر", "النحل", "الإسراء", "الكهف", "مريم", "طه",
                    "الأنبياء", "الحج", "المؤمنون", "النور", "الفرقان", "الشعراء", "النمل", "القصص", "العنكبوت", "الروم",
                    "لقمان", "السجدة", "الأحزاب", "سبأ", "فاطر", "يس", "الصافات", "ص", "الزمر", "غافر",
                    "فصلت", "الشورى", "الزخرف", "الدخان", "الجاثية", "الأحقاف", "محمد", "الفتح", "الحجرات", "ق",
                    "الذاريات", "الطور", "النجم", "القمر", "الرحمن", "الواقعة", "الحديد", "المجادلة", "الحشر", "الممتحنة",
                    "الصف", "الجمعة", "المنافقون", "التغابن", "الطلاق", "التحريم", "الملك", "القلم", "الحاقة", "المعارج",
                    "نوح", "الجن", "المزمل", "المدثر", "القيامة", "الإنسان", "المرسلات", "النبأ", "النازعات", "عبس",
                    "التكوير", "الانفطار", "المطففين", "الانشقاق", "البروج", "الطارق", "الأعلى", "الغاشية", "الفجر", "البلد",
                    "الشمس", "الليل", "الضحى", "الشرح", "التين", "العلق", "القدر", "البينة", "الزلزلة", "العاديات",
                    "القارعة", "التكاثر", "العصر", "الهمزة", "الفيل", "قريش", "الماعون", "الكوثر", "الكافرون", "النصر",
                    "المسد", "الإخلاص", "الفلق", "الناس"
                };

                // التأكد من أن الفهرس صحيح
                if (lastSurahIndex >= 0 && lastSurahIndex < surahNames.length) {
                    lastReadSurahText.setText("سورة " + surahNames[lastSurahIndex]);
                } else {
                    lastReadSurahText.setText("سورة الفاتحة"); // افتراضي
                }
            }
        } catch (Exception e) {
            // في حالة الخطأ، عرض الفاتحة كافتراضي
            TextView lastReadSurahText = findViewById(R.id.lastReadSurah);
            if (lastReadSurahText != null) {
                lastReadSurahText.setText("سورة الفاتحة");
            }
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- الخلفية الخارجية الكبيرة -->
    <item>
        <shape android:shape="oval">
            <size android:width="320dp" android:height="320dp" />
            <gradient
                android:type="radial"
                android:gradientRadius="160dp"
                android:startColor="#1A237E"
                android:centerColor="#283593"
                android:endColor="#3F51B5" />
        </shape>
    </item>
    
    <!-- الدائرة الثانية -->
    <item android:top="20dp" android:left="20dp" android:right="20dp" android:bottom="20dp">
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="140dp"
                android:startColor="#3949AB"
                android:centerColor="#5C6BC0"
                android:endColor="#7986CB" />
            <stroke android:width="2dp" android:color="#FFD700" />
        </shape>
    </item>
    
    <!-- الدائرة الثالثة -->
    <item android:top="40dp" android:left="40dp" android:right="40dp" android:bottom="40dp">
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="120dp"
                android:startColor="#9FA8DA"
                android:centerColor="#C5CAE9"
                android:endColor="#E8EAF6" />
            <stroke android:width="1dp" android:color="#FFD700" />
        </shape>
    </item>
    
    <!-- الدائرة الداخلية -->
    <item android:top="60dp" android:left="60dp" android:right="60dp" android:bottom="60dp">
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="100dp"
                android:startColor="#FFFFFF"
                android:centerColor="#F5F5F5"
                android:endColor="#E0E0E0" />
            <stroke android:width="3dp" android:color="#FFD700" />
        </shape>
    </item>
    
</layer-list>

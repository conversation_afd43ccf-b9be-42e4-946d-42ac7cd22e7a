05-30 22:39:05.962   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:06.016   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:06.918   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x1660efa sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:07.137   723   842 I ImeTracker: com.qurany2019.quranyapp:68e498df: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:07.193 27769 27769 I ImeTracker: com.qurany2019.quranyapp:68e498df: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:07.331  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:07.369   723  1257 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:07.949   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:07.995   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:09.043   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x5903b03 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:09.144   723   842 I ImeTracker: com.qurany2019.quranyapp:e30d6b72: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:09.150  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:09.153   723  1813 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:09.158 27769 27769 I ImeTracker: com.qurany2019.quranyapp:e30d6b72: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:09.951   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:09.993   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:10.413   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xc4376dc sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:10.504   723   842 I ImeTracker: com.qurany2019.quranyapp:9e560f0e: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:10.516 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9e560f0e: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:10.530  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:10.536   723   866 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:11.256 27769 27783 I y2019.quranyapp: Background concurrent mark compact GC freed 6663KB AllocSpace bytes, 36(1628KB) LOS objects, 49% free, 8314KB/16MB, paused 1.097ms,6.158ms total 67.596ms
05-30 22:39:12.004   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x37b9b21 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:12.038   723   842 I ImeTracker: com.qurany2019.quranyapp:ad894cfd: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:12.039 27769 27769 I ImeTracker: com.qurany2019.quranyapp:ad894cfd: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:12.047  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:12.051   723  1862 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:14.404   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.451   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.453   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.465   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:14.964   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:15.010   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:15.356   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd77fad0 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:15.502   723   842 I ImeTracker: com.qurany2019.quranyapp:aa1fbd8b: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:15.507  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:15.509   723   948 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:15.510 27769 27769 I ImeTracker: com.qurany2019.quranyapp:aa1fbd8b: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:16.906   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xad696fa sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:16.939   723   842 I ImeTracker: com.qurany2019.quranyapp:b0834f63: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:16.946  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:16.948   723  1693 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:16.974 27769 27769 I ImeTracker: com.qurany2019.quranyapp:b0834f63: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:17.946   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:17.987   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:18.449   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xc08e898 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:18.535   723   842 I ImeTracker: com.qurany2019.quranyapp:c6cab6ed: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:18.556  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:18.562   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:18.609 27769 27769 I ImeTracker: com.qurany2019.quranyapp:c6cab6ed: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:20.038   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd428cf6 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:20.113   723   842 I ImeTracker: com.qurany2019.quranyapp:cc04d17: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:20.118 27769 27769 I ImeTracker: com.qurany2019.quranyapp:cc04d17: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:20.122  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:20.124   723   948 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:20.945   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:20.983   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:21.470   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x7cc6e82 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:21.520   723   842 I ImeTracker: com.qurany2019.quranyapp:188cb7c3: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:21.532  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:21.533   723  1095 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:21.547 27769 27769 I ImeTracker: com.qurany2019.quranyapp:188cb7c3: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:22.931 27769 27783 I y2019.quranyapp: Background concurrent mark compact GC freed 5961KB AllocSpace bytes, 44(2096KB) LOS objects, 49% free, 8614KB/16MB, paused 9.536ms,3.098ms total 83.711ms
05-30 22:39:23.034   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xda5c297 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:23.169   723   842 I ImeTracker: com.qurany2019.quranyapp:9789688a: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:23.186  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:23.188   723  1693 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:23.208 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9789688a: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:23.948   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:23.985   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:24.461   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x2c21477 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:24.618   723   842 I ImeTracker: com.qurany2019.quranyapp:1b63baee: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:24.627  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:24.632   723  1832 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:24.641 27769 27769 I ImeTracker: com.qurany2019.quranyapp:1b63baee: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:26.123   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xa0b6d99 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:26.286   723   842 I ImeTracker: com.qurany2019.quranyapp:9aa3bbbf: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:26.294  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:26.294 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9aa3bbbf: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:26.297   723  1907 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:26.753   723   749 W ProcessStats: Tracking association SourceState{9180c9c com.qurany2019.quranyapp/10220 Top #19714} whose proc state 1 is better than process ProcessState{5d0d6cb com.google.android.gms/10147 pkg=com.google.android.gms} proc state 2 (435 skipped)
05-30 22:39:26.952   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:27.016   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:27.573   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x726e655 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:27.649   723   842 I ImeTracker: com.qurany2019.quranyapp:9cbab044: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:27.657  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:27.659   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:27.719 27769 27769 I ImeTracker: com.qurany2019.quranyapp:9cbab044: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:28.955   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:29.247   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xb6d4a00 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:29.341   723   842 I ImeTracker: com.qurany2019.quranyapp:8d912d6a: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:29.347  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:29.348   723  1812 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:29.374 27769 27769 I ImeTracker: com.qurany2019.quranyapp:8d912d6a: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:29.956   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:29.994   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:30.293   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xab4a50c sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:30.358   723   842 I ImeTracker: com.qurany2019.quranyapp:1f330a2c: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:30.364  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:30.366   723  1908 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:30.388 27769 27769 I ImeTracker: com.qurany2019.quranyapp:1f330a2c: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:32.077   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd36e200 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:32.108   723   842 I ImeTracker: com.qurany2019.quranyapp:1b890a50: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:32.112  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:32.116   723  1913 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:32.126 27769 27769 I ImeTracker: com.qurany2019.quranyapp:1b890a50: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:32.961   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:33.003   723   943 E ClipboardService: Denying clipboard access to com.qurany2019.quranyapp, application is not in focus nor is it a system service for user 0
05-30 22:39:33.382   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xdd62c60 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:33.515   723   842 I ImeTracker: com.qurany2019.quranyapp:7bfc95a4: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:33.522  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:33.529   723  1239 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:33.538 27769 27769 I ImeTracker: com.qurany2019.quranyapp:7bfc95a4: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:35.023   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0x2d12d57 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:35.099   723   842 I ImeTracker: com.qurany2019.quranyapp:43193036: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:35.113  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:35.116   723  1862 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:35.183 27769 27769 I ImeTracker: com.qurany2019.quranyapp:43193036: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:36.469   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd5148e5 sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:36.712   723   842 I ImeTracker: com.qurany2019.quranyapp:4cc79df7: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:36.717  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:36.719   723  1095 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:36.730 27769 27769 I ImeTracker: com.qurany2019.quranyapp:4cc79df7: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN
05-30 22:39:51.162   723   746 V WindowManager:         {WCT{RemoteToken{bc9a617 Task{999c5b0 #394 type=standard A=10220:com.qurany2019.quranyapp}}} m=TO_FRONT f=NONE p=WCT{RemoteToken{a3c4d1d DefaultTaskDisplayArea@233973881}} leash=Surface(name=Task=394)/@0xd4319bc sb=Rect(0, 0 - 1080, 2400) eb=Rect(0, 0 - 1080, 2400) epz=Point(1080, 2400) d=0 taskParent=-1},
05-30 22:39:51.314   723   842 I ImeTracker: com.qurany2019.quranyapp:da7ee548: onRequestHide at ORIGIN_SERVER reason HIDE_SAME_WINDOW_FOCUSED_WITHOUT_EDITOR fromUser false
05-30 22:39:51.317  1413  1413 I GoogleInputMethodService: GoogleInputMethodService.onStartInput():1293 onStartInput(EditorInfo{EditorInfo{packageName=com.qurany2019.quranyapp, inputType=0, inputTypeString=NULL, enableLearning=false, autoCorrection=false, autoComplete=false, imeOptions=0, privateImeOptions=null, actionName=UNSPECIFIED, actionLabel=null, initialSelStart=-1, initialSelEnd=-1, initialCapsMode=0, label=null, fieldId=0, fieldName=null, extras=null, hintText=null, hintLocales=[]}}, false)
05-30 22:39:51.318   723  1833 W PackageConfigPersister: App-specific configuration not found for packageName: com.qurany2019.quranyapp and userId: 0
05-30 22:39:51.326 27769 27769 I ImeTracker: com.qurany2019.quranyapp:da7ee548: onCancelled at PHASE_CLIENT_ALREADY_HIDDEN

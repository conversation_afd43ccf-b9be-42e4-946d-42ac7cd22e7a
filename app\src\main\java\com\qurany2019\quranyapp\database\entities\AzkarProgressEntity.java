package com.qurany2019.quranyapp.database.entities;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ColumnInfo;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.Ignore;

@Entity(
    tableName = "azkar_progress_table",
    foreignKeys = @ForeignKey(
        entity = AzkarEntity.class,
        parentColumns = "id",
        childColumns = "azkar_id",
        onDelete = ForeignKey.CASCADE
    ),
    indices = {@Index("azkar_id"), @Index("date")}
)
public class AzkarProgressEntity {
    @PrimaryKey(autoGenerate = true)
    private int id;

    @ColumnInfo(name = "azkar_id")
    private int azkarId;

    @ColumnInfo(name = "current_count")
    private int currentCount;

    @ColumnInfo(name = "target_count")
    private int targetCount;

    @ColumnInfo(name = "is_completed")
    private boolean isCompleted;

    @ColumnInfo(name = "date")
    private String date; // Format: yyyy-MM-dd

    @ColumnInfo(name = "start_time")
    private long startTime;

    @ColumnInfo(name = "completion_time")
    private long completionTime;

    @ColumnInfo(name = "session_duration")
    private long sessionDuration; // in milliseconds

    @ColumnInfo(name = "created_at")
    private long createdAt;

    @ColumnInfo(name = "updated_at")
    private long updatedAt;

    // Constructors
    public AzkarProgressEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    @Ignore
    public AzkarProgressEntity(int azkarId, int targetCount, String date) {
        this.azkarId = azkarId;
        this.currentCount = 0;
        this.targetCount = targetCount;
        this.isCompleted = false;
        this.date = date;
        this.startTime = 0;
        this.completionTime = 0;
        this.sessionDuration = 0;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAzkarId() {
        return azkarId;
    }

    public void setAzkarId(int azkarId) {
        this.azkarId = azkarId;
    }

    public int getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(int currentCount) {
        this.currentCount = currentCount;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getTargetCount() {
        return targetCount;
    }

    public void setTargetCount(int targetCount) {
        this.targetCount = targetCount;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
        if (completed && completionTime == 0) {
            this.completionTime = System.currentTimeMillis();
            if (startTime > 0) {
                this.sessionDuration = completionTime - startTime;
            }
        }
        this.updatedAt = System.currentTimeMillis();
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(long completionTime) {
        this.completionTime = completionTime;
    }

    public long getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(long sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Helper methods
    public void incrementCount() {
        if (startTime == 0) {
            startTime = System.currentTimeMillis();
        }
        
        currentCount++;
        if (currentCount >= targetCount) {
            setCompleted(true);
        }
        this.updatedAt = System.currentTimeMillis();
    }

    public void resetProgress() {
        this.currentCount = 0;
        this.isCompleted = false;
        this.startTime = 0;
        this.completionTime = 0;
        this.sessionDuration = 0;
        this.updatedAt = System.currentTimeMillis();
    }

    public int getProgressPercentage() {
        if (targetCount == 0) return 0;
        return Math.min(100, (currentCount * 100) / targetCount);
    }

    public boolean isInProgress() {
        return currentCount > 0 && !isCompleted;
    }

    public boolean isNotStarted() {
        return currentCount == 0;
    }

    @Override
    public String toString() {
        return "AzkarProgressEntity{" +
                "id=" + id +
                ", azkarId=" + azkarId +
                ", currentCount=" + currentCount +
                ", targetCount=" + targetCount +
                ", isCompleted=" + isCompleted +
                ", date='" + date + '\'' +
                ", startTime=" + startTime +
                ", completionTime=" + completionTime +
                ", sessionDuration=" + sessionDuration +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}

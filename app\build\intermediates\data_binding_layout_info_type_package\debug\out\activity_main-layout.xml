<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.qurany2019.quranyapp" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="236" endOffset="43"/></Target><Target id="@+id/topBar" view="LinearLayout"><Expressions/><location startLine="17" startOffset="8" endLine="88" endOffset="22"/></Target><Target id="@+id/menuIcon" view="ImageView"><Expressions/><location startLine="28" startOffset="12" endLine="38" endOffset="42"/></Target><Target id="@+id/languageToggleContainer" view="LinearLayout"><Expressions/><location startLine="47" startOffset="12" endLine="86" endOffset="26"/></Target><Target id="@+id/tvLanguageAr" view="TextView"><Expressions/><location startLine="56" startOffset="16" endLine="69" endOffset="46"/></Target><Target id="@+id/tvLanguageEn" view="TextView"><Expressions/><location startLine="71" startOffset="16" endLine="84" endOffset="46"/></Target><Target id="@+id/mainTitle" view="TextView"><Expressions/><location startLine="91" startOffset="8" endLine="106" endOffset="38"/></Target><Target id="@+id/buttonsContainer" view="LinearLayout"><Expressions/><location startLine="109" startOffset="8" endLine="197" endOffset="22"/></Target><Target id="@+id/btnQuranSuras" view="LinearLayout"><Expressions/><location startLine="121" startOffset="12" endLine="144" endOffset="26"/></Target><Target id="@+id/btnQuranListen" view="LinearLayout"><Expressions/><location startLine="147" startOffset="12" endLine="170" endOffset="26"/></Target><Target id="@+id/btnMoreApps" view="LinearLayout"><Expressions/><location startLine="173" startOffset="12" endLine="195" endOffset="26"/></Target><Target id="@+id/adSection" view="LinearLayout"><Expressions/><location startLine="200" startOffset="8" endLine="219" endOffset="22"/></Target><Target id="@+id/adView" view="com.google.android.gms.ads.AdView"><Expressions/><location startLine="211" startOffset="12" endLine="217" endOffset="71"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="224" startOffset="4" endLine="234" endOffset="54"/></Target></Targets></Layout>
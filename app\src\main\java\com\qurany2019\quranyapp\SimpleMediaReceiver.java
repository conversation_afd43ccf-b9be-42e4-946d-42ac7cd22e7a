package com.qurany2019.quranyapp;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * BroadcastReceiver للتحكم في الوسائط من الإشعار البسيط
 */
public class SimpleMediaReceiver extends BroadcastReceiver {

    private static final String TAG = "SimpleMediaReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "🎯🎯🎯 SimpleMediaReceiver.onReceive() تم استدعاؤه!");

        if (intent != null) {
            Log.d(TAG, "Intent Action: " + intent.getAction());
            Log.d(TAG, "Intent Extras: " + intent.getExtras());

            if ("com.qurany2019.quranyapp.SIMPLE_MEDIA_CONTROL".equals(intent.getAction())) {
                String action = intent.getStringExtra("action");
                Log.d(TAG, "الأكشن المستخرج: " + action);

                if ("PLAY_PAUSE".equals(action)) {
                    Log.d(TAG, "🔥 معالجة PLAY_PAUSE");

                    // إرسال Intent للتطبيق الرئيسي
                    Intent appIntent = new Intent(context, managerdb.class);
                    appIntent.setAction("SIMPLE_MEDIA_ACTION");
                    appIntent.putExtra("media_action", "PLAY_PAUSE");
                    appIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

                    Log.d(TAG, "🚀 إرسال Intent للتطبيق الرئيسي");
                    context.startActivity(appIntent);

                } else if ("NEXT".equals(action)) {
                    Log.d(TAG, "🔥 معالجة NEXT");

                    Intent appIntent = new Intent(context, managerdb.class);
                    appIntent.setAction("SIMPLE_MEDIA_ACTION");
                    appIntent.putExtra("media_action", "NEXT");
                    appIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

                    context.startActivity(appIntent);

                } else if ("PREVIOUS".equals(action)) {
                    Log.d(TAG, "🔥 معالجة PREVIOUS");

                    Intent appIntent = new Intent(context, managerdb.class);
                    appIntent.setAction("SIMPLE_MEDIA_ACTION");
                    appIntent.putExtra("media_action", "PREVIOUS");
                    appIntent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);

                    context.startActivity(appIntent);
                }
            } else {
                Log.e(TAG, "❌ Action غير متطابق: " + intent.getAction());
            }
        } else {
            Log.e(TAG, "❌ Intent is null!");
        }
    }
}
